#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断XML解析错误工具
分析 syntax error: line 1, column 0 的具体原因
"""

import asyncio
import sys
import os
from pathlib import Path
import xml.etree.ElementTree as ET

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.leidianapi.LeiDian_Reorganized import Dnconsole
from core.logger_manager import log_info, log_error, log_warning
from core.simple_config import get_config_manager

class XMLErrorDiagnoser:
    def __init__(self):
        # 初始化配置管理器
        self.config_manager = get_config_manager()
        
        # 从配置文件获取雷电模拟器路径
        base_path = self.config_manager.get("emulator_path", "G:/leidian/LDPlayer9")
        share_path = self.config_manager.get("basic_config.emulator_shared_path", "C:/Users/<USER>/Documents/leidian64")
        
        if not share_path:
            share_path = "C:/Users/<USER>/Documents/leidian64"
        
        self.ld = Dnconsole(base_path=base_path, share_path=share_path)
        self.emulator_id = 2
        
    async def diagnose_xml_error(self):
        """诊断XML解析错误"""
        log_info("=" * 80)
        log_info("🔍 诊断XML解析错误: syntax error: line 1, column 0")
        log_info("=" * 80)
        
        # 测试1: 分析uiautomator命令执行
        await self.test_uiautomator_command()
        
        # 测试2: 分析XML内容
        await self.analyze_xml_content()
        
        # 测试3: 测试不同的获取方法
        await self.test_different_methods()
        
        # 测试4: 模拟器状态检查
        await self.check_emulator_status()
        
    async def test_uiautomator_command(self):
        """测试uiautomator命令执行"""
        log_info("\n🔧 测试uiautomator命令执行")
        log_info("-" * 60)
        
        try:
            # 测试1: 标准方法
            log_info("📝 测试1: 标准execute_ld方法")
            success1, result1 = self.ld.execute_ld(self.emulator_id, "uiautomator dump /sdcard/ui_standard.xml")
            log_info(f"   命令执行: {'✅ 成功' if success1 else '❌ 失败'}")
            log_info(f"   返回内容: {repr(result1[:100]) if result1 else 'None'}...")
            
            if success1:
                success1_read, xml1 = self.ld.execute_ld(self.emulator_id, "cat /sdcard/ui_standard.xml")
                log_info(f"   文件读取: {'✅ 成功' if success1_read else '❌ 失败'}")
                if xml1:
                    log_info(f"   XML长度: {len(xml1)} 字符")
                    log_info(f"   XML开头: {repr(xml1[:50])}")
            
            # 测试2: 多语言支持方法
            log_info("\n📝 测试2: 多语言支持方法")
            success2, result2 = self.ld.execute_ld_with_multilingual_support(self.emulator_id, "uiautomator dump /sdcard/ui_multilingual.xml")
            log_info(f"   命令执行: {'✅ 成功' if success2 else '❌ 失败'}")
            log_info(f"   返回内容长度: {len(result2) if result2 else 0} 字符")
            
            if result2:
                log_info(f"   内容开头: {repr(result2[:50])}")
                log_info(f"   内容结尾: {repr(result2[-50:])}")
                
                # 检查是否是有效的XML
                if result2.strip().startswith('<?xml') or result2.strip().startswith('<'):
                    log_info(f"   XML格式: ✅ 看起来正确")
                else:
                    log_warning(f"   XML格式: ⚠️ 不是标准XML格式")
            
            # 测试3: 直接命令测试
            log_info("\n📝 测试3: 直接uiautomator命令")
            success3, result3 = self.ld.execute_ld(self.emulator_id, "uiautomator dump")
            log_info(f"   直接命令: {'✅ 成功' if success3 else '❌ 失败'}")
            log_info(f"   返回信息: {repr(result3) if result3 else 'None'}")
            
        except Exception as e:
            log_error(f"❌ uiautomator命令测试异常: {e}")
    
    async def analyze_xml_content(self):
        """分析XML内容"""
        log_info("\n📊 分析XML内容")
        log_info("-" * 60)
        
        try:
            # 获取XML内容进行详细分析
            success, xml_content = self.ld.execute_ld_with_multilingual_support(
                self.emulator_id, 
                "uiautomator dump /sdcard/ui_analysis.xml"
            )
            
            if not success:
                log_error("❌ 无法获取XML内容进行分析")
                return
            
            log_info(f"📝 XML内容基本信息:")
            log_info(f"   内容长度: {len(xml_content) if xml_content else 0} 字符")
            log_info(f"   是否为空: {'是' if not xml_content else '否'}")
            log_info(f"   是否只有空白: {'是' if xml_content and not xml_content.strip() else '否'}")
            
            if xml_content:
                # 分析内容类型
                content = xml_content.strip()
                log_info(f"   去空白后长度: {len(content)} 字符")
                
                if len(content) == 0:
                    log_warning("⚠️ 内容为空或只有空白字符")
                elif content.startswith('<?xml'):
                    log_info("✅ 标准XML声明开头")
                elif content.startswith('<'):
                    log_info("✅ XML标签开头")
                else:
                    log_warning(f"⚠️ 非XML格式内容，开头: {repr(content[:20])}")
                
                # 尝试解析XML
                log_info(f"\n📝 尝试XML解析:")
                try:
                    root = ET.fromstring(xml_content)
                    log_info(f"   ✅ XML解析成功")
                    log_info(f"   根元素: {root.tag}")
                    log_info(f"   子元素数量: {len(list(root))}")
                except ET.ParseError as e:
                    log_error(f"   ❌ XML解析失败: {e}")
                    
                    # 详细分析错误位置
                    error_str = str(e)
                    if "line 1, column 0" in error_str:
                        log_warning("   🔍 错误位置: 第1行第0列 - 文件开头就有问题")
                        log_info(f"   文件开头字符: {repr(xml_content[:10])}")
                        log_info(f"   开头字符编码: {[ord(c) for c in xml_content[:5]]}")
                except Exception as e:
                    log_error(f"   ❌ 其他解析异常: {e}")
                
                # 保存内容用于手动检查
                try:
                    with open("xml_debug_content.txt", 'w', encoding='utf-8') as f:
                        f.write(xml_content)
                    log_info(f"   💾 内容已保存到: xml_debug_content.txt")
                except Exception as e:
                    log_warning(f"   ⚠️ 保存文件失败: {e}")
            
        except Exception as e:
            log_error(f"❌ XML内容分析异常: {e}")
    
    async def test_different_methods(self):
        """测试不同的获取方法"""
        log_info("\n🔄 测试不同的获取方法")
        log_info("-" * 60)
        
        methods = [
            ("标准方法", lambda: self.ld.execute_ld(self.emulator_id, "uiautomator dump /sdcard/ui_test1.xml")),
            ("多语言方法", lambda: self.ld.execute_ld_with_multilingual_support(self.emulator_id, "uiautomator dump /sdcard/ui_test2.xml")),
            ("便捷方法", lambda: self.ld.get_ui_xml_with_multilingual_support(self.emulator_id)),
        ]
        
        for method_name, method_func in methods:
            try:
                log_info(f"📝 测试 {method_name}:")
                success, result = method_func()
                
                log_info(f"   执行结果: {'✅ 成功' if success else '❌ 失败'}")
                
                if success and result:
                    log_info(f"   内容长度: {len(result)} 字符")
                    
                    # 尝试解析
                    try:
                        root = ET.fromstring(result)
                        log_info(f"   XML解析: ✅ 成功")
                    except ET.ParseError as e:
                        log_warning(f"   XML解析: ❌ 失败 - {e}")
                    except Exception as e:
                        log_error(f"   XML解析: ❌ 异常 - {e}")
                else:
                    log_warning(f"   无有效内容返回")
                
                # 短暂等待
                await asyncio.sleep(0.5)
                
            except Exception as e:
                log_error(f"❌ {method_name} 测试异常: {e}")
    
    async def check_emulator_status(self):
        """检查模拟器状态"""
        log_info("\n📱 检查模拟器状态")
        log_info("-" * 60)
        
        try:
            # 检查模拟器是否运行
            log_info("📝 检查模拟器运行状态:")
            success, result = self.ld.execute_ld(self.emulator_id, "echo 'test'")
            log_info(f"   基本命令执行: {'✅ 正常' if success else '❌ 异常'}")
            
            # 检查Android系统状态
            log_info("\n📝 检查Android系统状态:")
            success, result = self.ld.execute_ld(self.emulator_id, "getprop ro.build.version.release")
            if success and result:
                log_info(f"   Android版本: {result.strip()}")
            else:
                log_warning(f"   无法获取Android版本")
            
            # 检查uiautomator服务
            log_info("\n📝 检查uiautomator服务:")
            success, result = self.ld.execute_ld(self.emulator_id, "ps | grep uiautomator")
            if success and result:
                log_info(f"   uiautomator进程: 运行中")
            else:
                log_warning(f"   uiautomator进程: 可能未运行")
            
            # 检查存储空间
            log_info("\n📝 检查存储空间:")
            success, result = self.ld.execute_ld(self.emulator_id, "df /sdcard")
            if success and result:
                log_info(f"   存储状态: 正常")
                log_info(f"   详细信息: {result.strip()}")
            else:
                log_warning(f"   无法检查存储状态")
            
            # 检查权限
            log_info("\n📝 检查文件权限:")
            success, result = self.ld.execute_ld(self.emulator_id, "ls -la /sdcard/ui*.xml")
            if success and result:
                log_info(f"   文件权限: {result.strip()}")
            else:
                log_info(f"   无相关XML文件或权限问题")
            
        except Exception as e:
            log_error(f"❌ 模拟器状态检查异常: {e}")
    
    async def test_xml_recovery_strategies(self):
        """测试XML恢复策略"""
        log_info("\n🛠️ 测试XML恢复策略")
        log_info("-" * 60)
        
        strategies = [
            ("清理临时文件", "rm /sdcard/ui*.xml"),
            ("重启uiautomator", "pkill uiautomator; sleep 1"),
            ("清理缓存", "rm -rf /data/local/tmp/uiautomator*"),
        ]
        
        for strategy_name, command in strategies:
            try:
                log_info(f"📝 策略: {strategy_name}")
                success, result = self.ld.execute_ld(self.emulator_id, command)
                log_info(f"   执行: {'✅ 成功' if success else '❌ 失败'}")
                
                # 测试恢复效果
                await asyncio.sleep(1)
                success, xml_content = self.ld.execute_ld_with_multilingual_support(
                    self.emulator_id, 
                    "uiautomator dump /sdcard/ui_recovery.xml"
                )
                
                if success and xml_content:
                    try:
                        root = ET.fromstring(xml_content)
                        log_info(f"   恢复效果: ✅ XML解析成功")
                        break  # 找到有效策略，退出
                    except:
                        log_warning(f"   恢复效果: ⚠️ 仍有解析问题")
                else:
                    log_warning(f"   恢复效果: ⚠️ 无法获取XML")
                
            except Exception as e:
                log_error(f"❌ 策略 {strategy_name} 异常: {e}")

async def main():
    """主函数"""
    diagnoser = XMLErrorDiagnoser()
    
    try:
        await diagnoser.diagnose_xml_error()
        await diagnoser.test_xml_recovery_strategies()
        
        log_info("\n" + "=" * 80)
        log_info("🎉 XML错误诊断完成！")
        log_info("=" * 80)
        log_info("💡 常见原因和解决方案:")
        log_info("   1. 空内容: uiautomator命令执行失败")
        log_info("   2. 编码问题: 特殊字符导致解析失败")
        log_info("   3. 服务异常: uiautomator服务需要重启")
        log_info("   4. 权限问题: 文件读写权限不足")
        log_info("   5. 系统状态: Android系统或模拟器异常")
        log_info("")
        log_info("🔧 建议的修复步骤:")
        log_info("   1. 检查模拟器是否正常运行")
        log_info("   2. 清理临时XML文件")
        log_info("   3. 重启uiautomator服务")
        log_info("   4. 使用重试机制处理临时错误")
        
    except Exception as e:
        log_error(f"❌ 诊断过程异常: {e}")
        import traceback
        log_error(f"详细错误信息:\n{traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(main())
