#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 实用的2FA密钥提取工具
========================================
功能描述: 基于实际可行方法的2FA密钥提取工具

核心策略:
1. 正确启用雷电模拟器root权限
2. 安装并配置Google Authenticator
3. 使用实际可行的方法提取密钥
4. 提供完整的操作指导

使用方法:
python fb/practical_2fa_extractor.py [emulator_id]

注意事项:
- 需要正确配置雷电模拟器root权限
- 需要安装Google Authenticator应用
- 仅用于合法的账户恢复目的
========================================
"""

import asyncio
import sys
import re
import json
import base64
import tempfile
import struct
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class PracticalTwoFactorExtractor:
    """实用的2FA密钥提取器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化实用提取器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.temp_dir = Path(tempfile.gettempdir()) / f"practical_extract_{emulator_id}"
        self.temp_dir.mkdir(exist_ok=True)
        
        log_info(f"[实用提取] 实用2FA提取器初始化 - 模拟器{emulator_id}", component="PracticalExtractor")

    async def run_practical_extraction(self):
        """运行实用提取流程"""
        try:
            log_info(f"[实用提取] 开始实用2FA提取", component="PracticalExtractor")
            
            print("🔓 实用的2FA密钥提取工具")
            print("=" * 50)
            print("⚠️  基于实际可行的方法")
            print("⚠️  提供完整的操作指导")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：检查和配置环境
            if not await self._check_and_configure_environment():
                return
            
            # 第二步：安装和配置认证器应用
            if not await self._install_and_configure_authenticator():
                return
            
            # 第三步：创建测试2FA账户
            await self._create_test_accounts()
            
            # 第四步：使用实际可行的方法提取密钥
            secrets = await self._extract_using_practical_methods()
            
            # 第五步：验证和显示结果
            verified_secrets = await self._verify_and_display_results(secrets)
            
            # 第六步：保存结果和提供指导
            await self._save_results_and_guidance(verified_secrets)
            
            log_info(f"[实用提取] 实用2FA提取完成", component="PracticalExtractor")
            
        except Exception as e:
            log_error(f"[实用提取] 实用提取失败: {e}", component="PracticalExtractor")
        finally:
            await self._cleanup()

    async def _check_and_configure_environment(self) -> bool:
        """检查和配置环境"""
        try:
            print("🔧 检查和配置环境...")
            print("-" * 40)
            
            # 检查基本连接
            success, result = self.task.ld.execute_ld(self.emulator_id, "whoami")
            if not success:
                print("❌ 无法连接到模拟器")
                return False
            
            current_user = result.strip()
            print(f"👤 当前用户: {current_user}")
            
            # 检查root权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'whoami'")
            if success and "root" in result:
                print("✅ Root权限可用")
            else:
                print("❌ Root权限不可用")
                print("\n🔧 雷电模拟器Root权限配置指导:")
                print("1. 打开雷电模拟器")
                print("2. 点击右侧工具栏的'设置'按钮")
                print("3. 在'高级设置'选项卡中")
                print("4. 找到'Root权限'选项，勾选启用")
                print("5. 重启模拟器")
                print("6. 重新运行此工具")
                print()
                
                confirm = input("是否已经配置好Root权限? (y/N): ").strip().lower()
                if confirm != 'y':
                    print("❌ 请先配置Root权限后再运行")
                    return False
                
                # 重新检查
                success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'whoami'")
                if not success or "root" not in result:
                    print("❌ Root权限仍然不可用")
                    return False
                else:
                    print("✅ Root权限配置成功")
            
            # 检查adb root权限
            print("🔍 尝试启用adb root权限...")
            success, result = self.task.ld.execute_ld(self.emulator_id, "adb root")
            if success:
                print("✅ adb root权限已启用")
                # 等待adb重启
                await asyncio.sleep(2)
            else:
                print("⚠️  adb root权限启用失败，继续使用su命令")
            
            # 尝试重新挂载系统分区为可写
            print("🔧 尝试重新挂载系统分区...")
            remount_commands = [
                "su -c 'mount -o remount,rw /system'",
                "su -c 'mount -o remount,rw /'",
                "su -c 'mount -o remount,rw /data'"
            ]
            
            for cmd in remount_commands:
                success, result = self.task.ld.execute_ld(self.emulator_id, cmd)
                if success:
                    print(f"   ✅ {cmd.split()[-1]} 重新挂载成功")
                else:
                    print(f"   ⚠️  {cmd.split()[-1]} 重新挂载失败")
            
            print("✅ 环境配置完成")
            print()
            return True
            
        except Exception as e:
            log_error(f"[实用提取] 环境配置失败: {e}", component="PracticalExtractor")
            return False

    async def _install_and_configure_authenticator(self) -> bool:
        """安装和配置认证器应用"""
        try:
            print("📱 安装和配置认证器应用...")
            print("-" * 40)
            
            # 检查Google Authenticator是否已安装
            success, result = self.task.ld.execute_ld(self.emulator_id, "pm list packages | grep com.google.android.apps.authenticator2")
            
            if success and "com.google.android.apps.authenticator2" in result:
                print("✅ Google Authenticator已安装")
            else:
                print("❌ Google Authenticator未安装")
                print("\n📥 Google Authenticator安装指导:")
                print("1. 在雷电模拟器中打开Google Play商店")
                print("2. 搜索'Google Authenticator'")
                print("3. 安装官方的Google Authenticator应用")
                print("4. 或者下载APK文件手动安装")
                print()
                
                # 尝试打开Play Store
                success, result = self.task.ld.execute_ld(self.emulator_id, "am start -a android.intent.action.VIEW -d 'market://details?id=com.google.android.apps.authenticator2'")
                if success:
                    print("✅ 已打开Play Store安装页面")
                
                print("💡 请安装Google Authenticator后按回车继续...")
                input("按回车键继续...")
                
                # 重新检查
                success, result = self.task.ld.execute_ld(self.emulator_id, "pm list packages | grep com.google.android.apps.authenticator2")
                if not success or "com.google.android.apps.authenticator2" not in result:
                    print("❌ Google Authenticator仍未安装")
                    print("💡 您可以继续使用其他方法，但建议安装认证器应用")
                    
                    confirm = input("是否继续不使用认证器应用? (y/N): ").strip().lower()
                    if confirm != 'y':
                        return False
                else:
                    print("✅ Google Authenticator安装成功")
            
            # 启动Google Authenticator
            print("🚀 启动Google Authenticator...")
            success, result = self.task.ld.execute_ld(self.emulator_id, "am start -n com.google.android.apps.authenticator2/.AuthenticatorActivity")
            if success:
                print("✅ Google Authenticator已启动")
                print("💡 请在应用中添加一些测试账户，然后按回车继续...")
                input("按回车键继续...")
            else:
                print("⚠️  无法启动Google Authenticator，但可以继续")
            
            print("✅ 认证器应用配置完成")
            print()
            return True
            
        except Exception as e:
            log_error(f"[实用提取] 认证器应用配置失败: {e}", component="PracticalExtractor")
            return False

    async def _create_test_accounts(self):
        """创建测试2FA账户"""
        try:
            print("🔑 创建测试2FA账户...")
            print("-" * 40)
            
            # 提供测试密钥供用户添加到认证器
            test_accounts = [
                {
                    'name': 'Test Facebook',
                    'secret': 'JBSWY3DPEHPK3PXP',
                    'issuer': 'Facebook'
                },
                {
                    'name': 'Demo Account',
                    'secret': 'KNRZ2OB5CK7L3THMQHACQRSTG6OB7VDF',
                    'issuer': 'TestService'
                }
            ]
            
            print("📋 测试账户信息 (可添加到Google Authenticator中):")
            print()
            
            for i, account in enumerate(test_accounts, 1):
                print(f"{i}. 账户名称: {account['name']}")
                print(f"   发行方: {account['issuer']}")
                print(f"   密钥: {account['secret']}")
                
                # 生成当前TOTP验证码
                current_code = self._generate_totp(account['secret'])
                print(f"   当前验证码: {current_code}")
                
                # 生成QR码URI
                uri = f"otpauth://totp/{account['issuer']}:{account['name']}?secret={account['secret']}&issuer={account['issuer']}"
                print(f"   QR码URI: {uri}")
                print()
            
            print("💡 操作建议:")
            print("1. 在Google Authenticator中点击'+'添加账户")
            print("2. 选择'输入提供的密钥'")
            print("3. 输入上述账户信息")
            print("4. 验证生成的验证码是否与上述显示的一致")
            print()
            
            input("添加测试账户后按回车继续...")
            
        except Exception as e:
            log_error(f"[实用提取] 创建测试账户失败: {e}", component="PracticalExtractor")

    def _generate_totp(self, secret: str) -> str:
        """生成TOTP验证码"""
        try:
            import hmac
            import hashlib
            import struct
            import time
            
            # 解码Base32密钥
            key = base64.b32decode(secret)
            
            # 获取当前时间戳
            timestamp = int(time.time()) // 30
            
            # 生成TOTP
            msg = struct.pack('>Q', timestamp)
            hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
            offset = hmac_digest[-1] & 0x0f
            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
            code = (code & 0x7fffffff) % 1000000
            
            return f"{code:06d}"
            
        except Exception as e:
            log_error(f"[实用提取] TOTP生成失败: {e}", component="PracticalExtractor")
            return "000000"

    async def _extract_using_practical_methods(self) -> List[Dict[str, Any]]:
        """使用实际可行的方法提取密钥"""
        try:
            print("🔍 使用实际可行的方法提取密钥...")
            print("-" * 40)
            
            all_secrets = []
            
            # 方法1：直接访问Google Authenticator数据库
            print("📊 方法1: 直接访问Google Authenticator数据库")
            secrets1 = await self._method1_direct_database_access()
            all_secrets.extend(secrets1)
            print(f"   结果: 找到 {len(secrets1)} 个密钥")
            
            # 方法2：使用run-as命令访问应用数据
            print("🔧 方法2: 使用run-as命令访问应用数据")
            secrets2 = await self._method2_run_as_access()
            all_secrets.extend(secrets2)
            print(f"   结果: 找到 {len(secrets2)} 个密钥")
            
            # 方法3：复制数据库到可访问位置
            print("📋 方法3: 复制数据库到可访问位置")
            secrets3 = await self._method3_copy_database()
            all_secrets.extend(secrets3)
            print(f"   结果: 找到 {len(secrets3)} 个密钥")
            
            # 方法4：使用strings命令提取
            print("🔤 方法4: 使用strings命令提取")
            secrets4 = await self._method4_strings_extraction()
            all_secrets.extend(secrets4)
            print(f"   结果: 找到 {len(secrets4)} 个密钥")
            
            # 方法5：分析应用备份
            print("💾 方法5: 分析应用备份")
            secrets5 = await self._method5_backup_analysis()
            all_secrets.extend(secrets5)
            print(f"   结果: 找到 {len(secrets5)} 个密钥")
            
            print(f"\n✅ 实际方法提取完成: 总共找到 {len(all_secrets)} 个可能的密钥")
            print()
            return all_secrets
            
        except Exception as e:
            log_error(f"[实用提取] 实际方法提取失败: {e}", component="PracticalExtractor")
            return []

    async def _method1_direct_database_access(self) -> List[Dict[str, Any]]:
        """方法1: 直接访问Google Authenticator数据库"""
        try:
            secrets = []
            package = "com.google.android.apps.authenticator2"
            db_path = f"/data/data/{package}/databases/accounts.db"
            
            # 尝试直接访问数据库
            access_commands = [
                f"su -c 'ls -la {db_path}'",
                f"su -c 'sqlite3 {db_path} \"SELECT * FROM accounts\"'",
                f"su -c 'cat {db_path} | strings | grep -E \"[A-Z2-7]{{16,}}\"'"
            ]
            
            for cmd in access_commands:
                success, result = self.task.ld.execute_ld(self.emulator_id, cmd)
                if success and result.strip():
                    print(f"   ✅ 命令成功: {cmd.split()[-1]}")
                    
                    # 搜索Base32模式
                    base32_matches = re.findall(r'[A-Z2-7]{16,}', result)
                    for match in base32_matches:
                        if self._is_valid_base32_key(match):
                            secrets.append({
                                'secret_key': match,
                                'source': 'google_authenticator_direct',
                                'method': 'direct_database_access',
                                'confidence': 'high'
                            })
                            print(f"   🔑 发现密钥: {match[:8]}...")
                    break
                else:
                    print(f"   ❌ 命令失败: {cmd.split()[-1]}")
            
            return secrets
            
        except Exception as e:
            log_error(f"[实用提取] 方法1失败: {e}", component="PracticalExtractor")
            return []

    async def _method2_run_as_access(self) -> List[Dict[str, Any]]:
        """方法2: 使用run-as命令访问应用数据"""
        try:
            secrets = []
            package = "com.google.android.apps.authenticator2"
            
            # 使用run-as命令
            run_as_commands = [
                f"run-as {package} ls -la databases/",
                f"run-as {package} cat databases/accounts.db | strings",
                f"run-as {package} sqlite3 databases/accounts.db 'SELECT * FROM accounts'"
            ]
            
            for cmd in run_as_commands:
                success, result = self.task.ld.execute_ld(self.emulator_id, cmd)
                if success and result.strip():
                    print(f"   ✅ run-as命令成功")
                    
                    # 搜索Base32模式
                    base32_matches = re.findall(r'[A-Z2-7]{16,}', result)
                    for match in base32_matches:
                        if self._is_valid_base32_key(match):
                            secrets.append({
                                'secret_key': match,
                                'source': 'google_authenticator_runas',
                                'method': 'run_as_access',
                                'confidence': 'high'
                            })
                            print(f"   🔑 发现密钥: {match[:8]}...")
                    break
                else:
                    print(f"   ❌ run-as命令失败")
            
            return secrets
            
        except Exception as e:
            log_error(f"[实用提取] 方法2失败: {e}", component="PracticalExtractor")
            return []

    async def _method3_copy_database(self) -> List[Dict[str, Any]]:
        """方法3: 复制数据库到可访问位置"""
        try:
            secrets = []
            package = "com.google.android.apps.authenticator2"
            source_db = f"/data/data/{package}/databases/accounts.db"
            temp_db = "/sdcard/temp_accounts.db"

            # 复制数据库文件
            copy_commands = [
                f"su -c 'cp {source_db} {temp_db}'",
                f"su -c 'chmod 777 {temp_db}'"
            ]

            copy_success = True
            for cmd in copy_commands:
                success, result = self.task.ld.execute_ld(self.emulator_id, cmd)
                if not success:
                    print(f"   ❌ 复制命令失败: {cmd}")
                    copy_success = False
                    break

            if copy_success:
                print(f"   ✅ 数据库复制成功")

                # 分析复制的数据库
                analysis_commands = [
                    f"sqlite3 {temp_db} 'SELECT * FROM accounts'",
                    f"strings {temp_db} | grep -E '[A-Z2-7]{{16,}}'",
                    f"hexdump -C {temp_db} | grep -E '[A-Z2-7]{{16,}}'"
                ]

                for cmd in analysis_commands:
                    success, result = self.task.ld.execute_ld(self.emulator_id, cmd)
                    if success and result.strip():
                        print(f"   ✅ 分析命令成功")

                        # 搜索Base32模式
                        base32_matches = re.findall(r'[A-Z2-7]{16,}', result)
                        for match in base32_matches:
                            if self._is_valid_base32_key(match):
                                secrets.append({
                                    'secret_key': match,
                                    'source': 'google_authenticator_copy',
                                    'method': 'copy_database',
                                    'confidence': 'high'
                                })
                                print(f"   🔑 发现密钥: {match[:8]}...")
                        break

                # 清理临时文件
                self.task.ld.execute_ld(self.emulator_id, f"rm -f {temp_db}")

            return secrets

        except Exception as e:
            log_error(f"[实用提取] 方法3失败: {e}", component="PracticalExtractor")
            return []

    async def _method4_strings_extraction(self) -> List[Dict[str, Any]]:
        """方法4: 使用strings命令提取"""
        try:
            secrets = []
            package = "com.google.android.apps.authenticator2"

            # 搜索应用目录中的所有文件
            search_paths = [
                f"/data/data/{package}/",
                f"/data/data/{package}/databases/",
                f"/data/data/{package}/shared_prefs/",
                f"/data/data/{package}/files/"
            ]

            for path in search_paths:
                print(f"   🔍 搜索路径: {path}")

                # 查找文件并使用strings命令
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {path} -type f -exec strings {{}} \\; 2>/dev/null | grep -E \"[A-Z2-7]{{16,}}\"'")
                if success and result.strip():
                    print(f"   ✅ 在 {path} 找到数据")

                    potential_keys = result.strip().split('\n')
                    for key in potential_keys:
                        if self._is_valid_base32_key(key.strip()):
                            secrets.append({
                                'secret_key': key.strip(),
                                'source': f'strings_extraction:{path}',
                                'method': 'strings_extraction',
                                'confidence': 'medium'
                            })
                            print(f"   🔑 发现密钥: {key.strip()[:8]}...")
                else:
                    print(f"   ❌ 在 {path} 未找到数据")

            return secrets

        except Exception as e:
            log_error(f"[实用提取] 方法4失败: {e}", component="PracticalExtractor")
            return []

    async def _method5_backup_analysis(self) -> List[Dict[str, Any]]:
        """方法5: 分析应用备份"""
        try:
            secrets = []
            package = "com.google.android.apps.authenticator2"
            backup_file = "/sdcard/authenticator_backup.ab"

            # 创建应用备份
            print(f"   📦 创建应用备份...")
            success, result = self.task.ld.execute_ld(self.emulator_id, f"pm dump {package} > {backup_file}")
            if success:
                print(f"   ✅ 备份创建成功")

                # 分析备份文件
                success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"strings {backup_file} | grep -E '[A-Z2-7]{{16,}}'")
                if success2 and result2.strip():
                    potential_keys = result2.strip().split('\n')
                    for key in potential_keys:
                        if self._is_valid_base32_key(key.strip()):
                            secrets.append({
                                'secret_key': key.strip(),
                                'source': 'backup_analysis',
                                'method': 'backup_analysis',
                                'confidence': 'medium'
                            })
                            print(f"   🔑 发现密钥: {key.strip()[:8]}...")

                # 清理备份文件
                self.task.ld.execute_ld(self.emulator_id, f"rm -f {backup_file}")
            else:
                print(f"   ❌ 备份创建失败")

            return secrets

        except Exception as e:
            log_error(f"[实用提取] 方法5失败: {e}", component="PracticalExtractor")
            return []

    def _is_valid_base32_key(self, key: str) -> bool:
        """验证是否为有效的Base32密钥"""
        try:
            if not key or len(key) < 16:
                return False

            # 清理密钥
            cleaned = re.sub(r'[^A-Z2-7]', '', key.upper())
            if len(cleaned) < 16:
                return False

            # 验证Base32字符集
            if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in cleaned):
                return False

            # 尝试解码
            try:
                base64.b32decode(cleaned)
                return True
            except Exception:
                return False

        except Exception:
            return False

    async def _verify_and_display_results(self, all_secrets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证和显示结果"""
        try:
            print("🔍 验证提取的密钥...")
            print("-" * 40)

            # 去重
            unique_secrets = []
            seen_keys = set()

            for secret in all_secrets:
                key = secret.get('secret_key', '')
                if key and key not in seen_keys:
                    seen_keys.add(key)

                    # 验证密钥并生成测试TOTP
                    if self._is_valid_base32_key(key):
                        try:
                            current_code = self._generate_totp(key)

                            secret['test_totp'] = current_code
                            secret['verified'] = True
                            secret['key_length'] = len(key)

                            print(f"✅ 验证密钥: {key[:8]}...")
                            print(f"   来源: {secret.get('source', 'unknown')}")
                            print(f"   方法: {secret.get('method', 'unknown')}")
                            print(f"   当前验证码: {current_code}")
                            print(f"   置信度: {secret.get('confidence', 'unknown')}")
                            print()

                        except Exception as e:
                            secret['verified'] = False
                            secret['error'] = str(e)
                            print(f"❌ 密钥验证失败: {key[:8]}... - {e}")

                        unique_secrets.append(secret)

            # 按置信度排序
            confidence_order = {'high': 3, 'medium': 2, 'low': 1}
            unique_secrets.sort(key=lambda x: confidence_order.get(x.get('confidence', 'low'), 0), reverse=True)

            print(f"✅ 验证完成: {len(unique_secrets)} 个有效密钥")
            print()
            return unique_secrets

        except Exception as e:
            log_error(f"[实用提取] 验证结果失败: {e}", component="PracticalExtractor")
            return []

    async def _save_results_and_guidance(self, secrets: List[Dict[str, Any]]):
        """保存结果和提供指导"""
        try:
            if not secrets:
                print("❌ 未找到任何2FA密钥")
                print("\n💡 可能的原因和解决方案:")
                print("1. Google Authenticator中没有添加任何账户")
                print("   解决方案: 在应用中添加一些测试账户")
                print("2. Root权限配置不正确")
                print("   解决方案: 重新配置雷电模拟器的Root权限")
                print("3. 应用数据被加密或存储在其他位置")
                print("   解决方案: 尝试其他认证器应用")
                print("4. 权限限制导致无法访问应用数据")
                print("   解决方案: 使用adb root或其他提权方法")
                print()
                return

            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存到JSON文件
            output_dir = Path("./fb_2fa_results/practical_extraction/")
            output_dir.mkdir(parents=True, exist_ok=True)

            json_file = output_dir / f"practical_extracted_secrets_{self.emulator_id}_{timestamp}.json"

            result_data = {
                'emulator_id': self.emulator_id,
                'extraction_time': datetime.datetime.now().isoformat(),
                'extraction_method': 'practical_multi_method_extraction',
                'total_secrets': len(secrets),
                'secrets': secrets
            }

            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)

            # 保存为可导入格式
            import_file = output_dir / f"import_ready_secrets_{self.emulator_id}_{timestamp}.txt"

            with open(import_file, 'w', encoding='utf-8') as f:
                f.write("# 2FA密钥导入文件\n")
                f.write("# 可直接复制到认证器应用中\n")
                f.write(f"# 生成时间: {result_data['extraction_time']}\n")
                f.write("# " + "=" * 50 + "\n\n")

                for i, secret in enumerate(secrets, 1):
                    f.write(f"密钥 {i}:\n")
                    f.write(f"密钥: {secret['secret_key']}\n")
                    f.write(f"来源: {secret.get('source', 'unknown')}\n")
                    f.write(f"当前验证码: {secret.get('test_totp', 'N/A')}\n")
                    f.write(f"置信度: {secret.get('confidence', 'unknown')}\n")
                    f.write("-" * 30 + "\n")

            print(f"💾 结果已保存:")
            print(f"   📄 详细结果: {json_file}")
            print(f"   📄 导入文件: {import_file}")
            print()

            # 显示使用指导
            print("📋 使用指导:")
            print("=" * 40)
            print("1. 验证提取的密钥:")
            print("   - 将密钥添加到新的认证器应用中")
            print("   - 检查生成的验证码是否与显示的一致")
            print()
            print("2. Facebook 2FA设置:")
            print("   - 登录Facebook账户")
            print("   - 进入安全设置 → 双重验证")
            print("   - 添加认证器应用作为验证方法")
            print("   - 使用提取的密钥配置")
            print()
            print("3. 安全建议:")
            print("   - 立即更换手机号码绑定")
            print("   - 启用多种2FA验证方法")
            print("   - 定期备份2FA密钥")
            print("   - 妥善保管密钥文件")

        except Exception as e:
            log_error(f"[实用提取] 保存结果失败: {e}", component="PracticalExtractor")

    async def _cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir, ignore_errors=True)

            # 清理模拟器上的临时文件
            cleanup_files = [
                "/sdcard/temp_*.db",
                "/sdcard/authenticator_backup.ab",
                "/sdcard/temp_accounts.db"
            ]

            for pattern in cleanup_files:
                self.task.ld.execute_ld(self.emulator_id, f"rm -f {pattern}")

        except Exception:
            pass

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2

        print("🔓 实用的2FA密钥提取工具")
        print("⚠️  基于实际可行的方法")
        print("⚠️  提供完整的操作指导")
        print("⚠️  需要正确配置雷电模拟器root权限")
        print("⚠️  仅用于合法的账户恢复目的")
        print()

        confirm = input("确认继续实用提取? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return

        # 创建实用提取器
        extractor = PracticalTwoFactorExtractor(emulator_id)

        # 运行实用提取
        await extractor.run_practical_extraction()

        print("\n" + "=" * 50)
        print("✅ 实用2FA提取完成")

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
