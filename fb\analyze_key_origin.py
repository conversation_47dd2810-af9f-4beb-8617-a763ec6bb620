#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 分析TOTP密钥来源和真实性
========================================
功能描述: 深度分析TOTP密钥的来源，确定是否为真实账户密钥

核心功能:
1. 追踪密钥在系统中的来源
2. 分析密钥的创建时间和上下文
3. 检查密钥是否与账户绑定
4. 搜索真实的Facebook 2FA密钥

使用方法:
python fb/analyze_key_origin.py [emulator_id]

注意事项:
- 需要root权限
- 深度分析系统数据
- 寻找真实的2FA密钥
========================================
"""

import asyncio
import sys
import re
import json
import base64
import tempfile
import struct
import hashlib
import time
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class KeyOriginAnalyzer:
    """密钥来源分析器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化分析器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.suspect_key = "XZJKVEGNFN6S2C2Z7LTDRW5TUZYH4WPU"
        
        # 目标应用包名
        self.target_packages = [
            "com.facebook.katana",      # Facebook
            "com.instagram.android",    # Instagram
            "com.google.android.gms",   # Google Services
            "com.android.keychain",     # Android Keychain
            "com.google.android.apps.authenticator2",  # Google Authenticator
            "com.microsoft.authenticator",  # Microsoft Authenticator
            "com.authy.authy"           # Authy
        ]
        
        log_info(f"[密钥分析] 密钥来源分析器初始化 - 模拟器{emulator_id}", component="KeyOriginAnalyzer")

    async def analyze_key_origin(self):
        """分析密钥来源"""
        try:
            log_info(f"[密钥分析] 开始分析密钥来源", component="KeyOriginAnalyzer")
            
            print("🔍 分析TOTP密钥来源和真实性")
            print("=" * 50)
            print(f"🔑 分析密钥: {self.suspect_key}")
            print("⚠️  深度分析密钥来源和真实性")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：分析密钥在系统中的分布
            key_locations = await self._find_key_locations()
            
            # 第二步：分析密钥的创建时间和上下文
            key_context = await self._analyze_key_context(key_locations)
            
            # 第三步：检查密钥是否与Facebook账户绑定
            binding_status = await self._check_account_binding()
            
            # 第四步：搜索其他可能的真实密钥
            real_keys = await self._search_real_facebook_keys()
            
            # 第五步：分析结果并给出结论
            await self._analyze_results(key_locations, key_context, binding_status, real_keys)
            
            log_info(f"[密钥分析] 密钥来源分析完成", component="KeyOriginAnalyzer")
            
        except Exception as e:
            log_error(f"[密钥分析] 密钥来源分析失败: {e}", component="KeyOriginAnalyzer")

    async def _find_key_locations(self) -> List[Dict[str, Any]]:
        """查找密钥在系统中的所有位置"""
        try:
            print("📍 查找密钥在系统中的位置...")
            print("-" * 40)
            
            locations = []
            
            # 在所有目标应用中搜索密钥
            for package in self.target_packages:
                print(f"🔍 搜索应用: {package}")
                
                data_path = f"/data/data/{package}"
                
                # 检查应用是否存在
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'ls -la {data_path} 2>/dev/null'")
                if not success or "No such file" in result:
                    print(f"   ❌ 应用不存在: {package}")
                    continue
                
                print(f"   ✅ 应用存在: {package}")
                
                # 在应用数据中搜索密钥
                search_cmd = f"su -c 'find {data_path} -type f -exec grep -l \"{self.suspect_key}\" {{}} \\; 2>/dev/null'"
                success2, result2 = self.task.ld.execute_ld(self.emulator_id, search_cmd)
                
                if success2 and result2.strip():
                    files = result2.strip().split('\n')
                    print(f"   🎯 找到密钥: {len(files)} 个文件")
                    
                    for file_path in files:
                        if file_path.strip():
                            # 获取文件详细信息
                            success3, result3 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'ls -la {file_path}'")
                            if success3:
                                file_info = result3.strip()
                                
                                location_info = {
                                    'package': package,
                                    'file_path': file_path,
                                    'file_info': file_info,
                                    'found_in_app': True
                                }
                                locations.append(location_info)
                                print(f"      📄 {file_path.split('/')[-1]}")
                else:
                    print(f"   ❌ 未找到密钥")
            
            # 在系统级位置搜索
            print("\n🔍 搜索系统级位置...")
            system_paths = [
                "/system/",
                "/vendor/",
                "/cache/",
                "/data/misc/",
                "/sdcard/"
            ]
            
            for sys_path in system_paths:
                print(f"   🔍 搜索: {sys_path}")
                search_cmd = f"su -c 'find {sys_path} -type f -exec grep -l \"{self.suspect_key}\" {{}} \\; 2>/dev/null | head -5'"
                success, result = self.task.ld.execute_ld(self.emulator_id, search_cmd)
                
                if success and result.strip():
                    files = result.strip().split('\n')
                    for file_path in files:
                        if file_path.strip():
                            location_info = {
                                'package': 'system',
                                'file_path': file_path,
                                'found_in_app': False
                            }
                            locations.append(location_info)
                            print(f"      📄 {file_path}")
            
            print(f"\n✅ 密钥位置搜索完成: 找到 {len(locations)} 个位置")
            print()
            return locations
            
        except Exception as e:
            log_error(f"[密钥分析] 密钥位置搜索失败: {e}", component="KeyOriginAnalyzer")
            return []

    async def _analyze_key_context(self, locations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析密钥的上下文信息"""
        try:
            print("📊 分析密钥上下文信息...")
            print("-" * 40)
            
            context = {
                'creation_patterns': [],
                'surrounding_data': [],
                'file_types': [],
                'timestamps': []
            }
            
            for location in locations:
                file_path = location['file_path']
                print(f"🔍 分析文件: {file_path.split('/')[-1]}")
                
                # 分析文件类型
                file_ext = file_path.split('.')[-1] if '.' in file_path else 'unknown'
                context['file_types'].append(file_ext)
                
                # 获取文件时间戳
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'stat {file_path} 2>/dev/null'")
                if success and result.strip():
                    context['timestamps'].append(result.strip())
                    print(f"   📅 时间戳信息已获取")
                
                # 分析密钥周围的数据
                success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'grep -C 3 \"{self.suspect_key}\" {file_path} 2>/dev/null'")
                if success2 and result2.strip():
                    surrounding = result2.strip()
                    context['surrounding_data'].append({
                        'file': file_path,
                        'context': surrounding[:200]  # 限制长度
                    })
                    print(f"   📝 上下文数据已获取")
                    
                    # 分析是否包含测试相关关键词
                    test_keywords = ['test', 'demo', 'example', 'sample', 'debug']
                    if any(keyword in surrounding.lower() for keyword in test_keywords):
                        context['creation_patterns'].append('可能是测试数据')
                        print(f"   ⚠️  可能是测试数据")
            
            print(f"✅ 密钥上下文分析完成")
            print()
            return context
            
        except Exception as e:
            log_error(f"[密钥分析] 密钥上下文分析失败: {e}", component="KeyOriginAnalyzer")
            return {}

    async def _check_account_binding(self) -> Dict[str, Any]:
        """检查密钥是否与Facebook账户绑定"""
        try:
            print("🔗 检查密钥与Facebook账户绑定状态...")
            print("-" * 40)
            
            binding_status = {
                'has_user_data': False,
                'has_device_registration': False,
                'has_server_sync': False,
                'binding_evidence': []
            }
            
            facebook_data = "/data/data/com.facebook.katana"
            
            # 检查是否有用户数据
            user_data_paths = [
                f"{facebook_data}/shared_prefs",
                f"{facebook_data}/databases",
                f"{facebook_data}/files"
            ]
            
            for path in user_data_paths:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {path} -type f | wc -l' 2>/dev/null")
                if success and result.strip() and int(result.strip()) > 0:
                    binding_status['has_user_data'] = True
                    print(f"   ✅ 发现用户数据: {path.split('/')[-1]}")
                    break
            
            # 检查设备注册信息
            device_patterns = ['device', 'registration', 'auth', 'token']
            for pattern in device_patterns:
                search_cmd = f"su -c 'find {facebook_data} -type f -exec grep -l \"{pattern}\" {{}} \\; 2>/dev/null | head -3'"
                success, result = self.task.ld.execute_ld(self.emulator_id, search_cmd)
                if success and result.strip():
                    binding_status['has_device_registration'] = True
                    binding_status['binding_evidence'].append(f'找到{pattern}相关文件')
                    print(f"   ✅ 发现设备注册信息: {pattern}")
            
            # 检查网络同步证据
            network_files = ['cache', 'sync', 'server']
            for net_file in network_files:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {facebook_data} -name \"*{net_file}*\" -type f | head -3' 2>/dev/null")
                if success and result.strip():
                    binding_status['has_server_sync'] = True
                    print(f"   ✅ 发现网络同步证据: {net_file}")
            
            print(f"✅ 账户绑定状态检查完成")
            print()
            return binding_status
            
        except Exception as e:
            log_error(f"[密钥分析] 账户绑定检查失败: {e}", component="KeyOriginAnalyzer")
            return {}

    async def _search_real_facebook_keys(self) -> List[Dict[str, Any]]:
        """搜索真实的Facebook 2FA密钥"""
        try:
            print("🔎 搜索真实的Facebook 2FA密钥...")
            print("-" * 40)
            
            real_keys = []
            facebook_data = "/data/data/com.facebook.katana"
            
            # 搜索所有可能的Base32密钥
            print("   🔍 搜索Base32模式密钥...")
            search_cmd = f"su -c 'find {facebook_data} -type f -exec grep -oE \"[A-Z2-7]{{16,}}\" {{}} \\; 2>/dev/null | sort | uniq'"
            success, result = self.task.ld.execute_ld(self.emulator_id, search_cmd)
            
            if success and result.strip():
                potential_keys = result.strip().split('\n')
                print(f"   📊 找到 {len(potential_keys)} 个潜在密钥")
                
                for key in potential_keys:
                    if key.strip() and len(key.strip()) >= 16:
                        cleaned_key = key.strip()
                        
                        # 验证是否为有效的Base32密钥
                        if self._is_valid_base32_key(cleaned_key):
                            # 检查是否与我们的可疑密钥不同
                            if cleaned_key != self.suspect_key:
                                # 生成测试验证码
                                test_code = self._generate_totp_code(cleaned_key)
                                
                                real_key_info = {
                                    'key': cleaned_key,
                                    'length': len(cleaned_key),
                                    'test_code': test_code,
                                    'is_different': True
                                }
                                real_keys.append(real_key_info)
                                print(f"      🔑 发现新密钥: {cleaned_key[:8]}... (验证码: {test_code})")
                            else:
                                print(f"      ⚠️  重复密钥: {cleaned_key[:8]}...")
            
            # 搜索最近修改的文件
            print("   🕒 搜索最近修改的文件...")
            recent_cmd = f"su -c 'find {facebook_data} -type f -mtime -7 -exec ls -lt {{}} \\; | head -10'"
            success2, result2 = self.task.ld.execute_ld(self.emulator_id, recent_cmd)
            if success2 and result2.strip():
                recent_files = result2.strip().split('\n')
                print(f"   📅 找到 {len(recent_files)} 个最近修改的文件")
                
                for file_info in recent_files:
                    if file_info.strip():
                        # 在最近修改的文件中搜索密钥
                        file_path = file_info.split()[-1] if file_info.split() else ""
                        if file_path and "/" in file_path:
                            success3, result3 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'grep -oE \"[A-Z2-7]{{16,}}\" {file_path} 2>/dev/null'")
                            if success3 and result3.strip():
                                keys_in_file = result3.strip().split('\n')
                                for key in keys_in_file:
                                    if key.strip() and self._is_valid_base32_key(key.strip()) and key.strip() != self.suspect_key:
                                        test_code = self._generate_totp_code(key.strip())
                                        real_key_info = {
                                            'key': key.strip(),
                                            'length': len(key.strip()),
                                            'test_code': test_code,
                                            'source': 'recent_file',
                                            'file': file_path.split('/')[-1]
                                        }
                                        real_keys.append(real_key_info)
                                        print(f"      🆕 最近文件中的密钥: {key.strip()[:8]}... (验证码: {test_code})")
            
            # 去重
            unique_keys = []
            seen_keys = set()
            for key_info in real_keys:
                if key_info['key'] not in seen_keys:
                    seen_keys.add(key_info['key'])
                    unique_keys.append(key_info)
            
            print(f"✅ 真实密钥搜索完成: 找到 {len(unique_keys)} 个不同的密钥")
            print()
            return unique_keys
            
        except Exception as e:
            log_error(f"[密钥分析] 真实密钥搜索失败: {e}", component="KeyOriginAnalyzer")
            return []

    def _is_valid_base32_key(self, key: str) -> bool:
        """验证是否为有效的Base32密钥"""
        try:
            if not key or len(key) < 16:
                return False
            
            # 清理密钥
            cleaned = re.sub(r'[^A-Z2-7]', '', key.upper())
            if len(cleaned) < 16:
                return False
            
            # 验证Base32字符集
            if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in cleaned):
                return False
            
            # 尝试解码
            try:
                base64.b32decode(cleaned)
                return True
            except Exception:
                return False
                
        except Exception:
            return False

    def _generate_totp_code(self, secret: str) -> str:
        """生成TOTP验证码"""
        try:
            import hmac
            import hashlib
            import struct
            
            # 解码Base32密钥
            key = base64.b32decode(secret)
            
            # 获取当前时间戳
            timestamp = int(time.time()) // 30
            
            # 生成TOTP
            msg = struct.pack('>Q', timestamp)
            hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
            offset = hmac_digest[-1] & 0x0f
            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
            code = (code & 0x7fffffff) % 1000000
            
            return f"{code:06d}"
            
        except Exception:
            return "000000"

    async def _analyze_results(self, locations: List[Dict[str, Any]], context: Dict[str, Any],
                             binding_status: Dict[str, Any], real_keys: List[Dict[str, Any]]):
        """分析结果并给出结论"""
        try:
            print("🎯 分析结果和结论")
            print("=" * 50)

            print(f"🔑 分析密钥: {self.suspect_key}")
            print()

            # 分析密钥位置
            print("📍 密钥位置分析:")
            if locations:
                print(f"   ✅ 在 {len(locations)} 个位置找到密钥")
                for loc in locations[:3]:  # 只显示前3个
                    package = loc.get('package', 'unknown')
                    file_name = loc.get('file_path', '').split('/')[-1]
                    print(f"      📄 {package}: {file_name}")
                if len(locations) > 3:
                    print(f"      ... 还有 {len(locations) - 3} 个位置")
            else:
                print("   ❌ 未在系统中找到密钥")

            # 分析上下文
            print("\n📊 上下文分析:")
            if context.get('creation_patterns'):
                for pattern in context['creation_patterns']:
                    print(f"   ⚠️  {pattern}")

            file_types = context.get('file_types', [])
            if file_types:
                unique_types = list(set(file_types))
                print(f"   📁 文件类型: {', '.join(unique_types)}")

            # 分析账户绑定
            print("\n🔗 账户绑定分析:")
            if binding_status.get('has_user_data'):
                print("   ✅ 发现用户数据")
            else:
                print("   ❌ 未发现用户数据")

            if binding_status.get('has_device_registration'):
                print("   ✅ 发现设备注册信息")
            else:
                print("   ❌ 未发现设备注册信息")

            # 分析真实密钥
            print("\n🔎 真实密钥分析:")
            if real_keys:
                print(f"   ✅ 发现 {len(real_keys)} 个其他可能的真实密钥:")
                for i, key_info in enumerate(real_keys[:5], 1):  # 只显示前5个
                    key = key_info['key']
                    test_code = key_info['test_code']
                    print(f"      🔑 密钥 {i}: {key[:8]}... (当前验证码: {test_code})")

                if len(real_keys) > 5:
                    print(f"      ... 还有 {len(real_keys) - 5} 个密钥")
            else:
                print("   ❌ 未发现其他可能的真实密钥")

            # 给出最终结论
            print("\n🎯 最终结论:")
            await self._provide_conclusion(locations, context, binding_status, real_keys)

        except Exception as e:
            log_error(f"[密钥分析] 结果分析失败: {e}", component="KeyOriginAnalyzer")

    async def _provide_conclusion(self, locations: List[Dict[str, Any]], context: Dict[str, Any],
                                binding_status: Dict[str, Any], real_keys: List[Dict[str, Any]]):
        """提供最终结论"""
        try:
            # 计算可信度评分
            credibility_score = 0
            reasons = []

            # 位置评分
            if locations:
                facebook_locations = [loc for loc in locations if 'facebook' in loc.get('package', '')]
                if facebook_locations:
                    credibility_score += 30
                    reasons.append("在Facebook应用中找到")
                else:
                    credibility_score += 10
                    reasons.append("在系统中找到但不在Facebook应用中")

            # 上下文评分
            if '可能是测试数据' in context.get('creation_patterns', []):
                credibility_score -= 40
                reasons.append("可能是测试数据")
            else:
                credibility_score += 20
                reasons.append("不像测试数据")

            # 绑定评分
            if binding_status.get('has_user_data') and binding_status.get('has_device_registration'):
                credibility_score += 30
                reasons.append("有完整的用户数据和设备注册")
            elif binding_status.get('has_user_data'):
                credibility_score += 15
                reasons.append("有用户数据但缺少设备注册")
            else:
                credibility_score -= 20
                reasons.append("缺少用户数据和设备注册")

            # 其他密钥评分
            if real_keys:
                credibility_score -= 10
                reasons.append(f"发现了{len(real_keys)}个其他可能的真实密钥")
            else:
                credibility_score += 10
                reasons.append("没有发现其他竞争密钥")

            # 给出结论
            print(f"📊 可信度评分: {credibility_score}/100")
            print()

            if credibility_score >= 70:
                print("✅ 结论: 这很可能是您Facebook账户的真实TOTP密钥")
                print("💡 建议: 继续尝试在Facebook中使用此密钥")
                print("🔧 可能需要: 完成设备注册流程")
            elif credibility_score >= 40:
                print("⚠️  结论: 这个密钥的真实性存疑")
                print("💡 建议: 需要进一步验证")
                if real_keys:
                    print("🔧 建议: 尝试其他发现的密钥")
            else:
                print("❌ 结论: 这很可能不是您Facebook账户的真实TOTP密钥")
                print("💡 建议: 寻找真实的密钥")
                if real_keys:
                    print("🔧 建议: 优先测试其他发现的密钥")

            print("\n📋 评分依据:")
            for reason in reasons:
                print(f"   • {reason}")

            # 提供下一步行动建议
            print("\n🚀 下一步行动建议:")
            if real_keys:
                print("1. 测试其他发现的密钥:")
                for i, key_info in enumerate(real_keys[:3], 1):
                    key = key_info['key']
                    test_code = key_info['test_code']
                    print(f"   密钥 {i}: {key} (验证码: {test_code})")
                print()

            print("2. 如果所有密钥都失败:")
            print("   - 重新设置Facebook 2FA")
            print("   - 联系Facebook客服")
            print("   - 使用备用验证方法")

        except Exception as e:
            log_error(f"[密钥分析] 结论提供失败: {e}", component="KeyOriginAnalyzer")

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2

        print("🔍 分析TOTP密钥来源和真实性")
        print("⚠️  深度分析密钥来源")
        print("⚠️  寻找真实的Facebook 2FA密钥")
        print("⚠️  需要root权限")
        print()

        confirm = input("确认分析密钥来源和真实性? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return

        # 创建分析器
        analyzer = KeyOriginAnalyzer(emulator_id)

        # 分析密钥来源
        await analyzer.analyze_key_origin()

        print("\n" + "=" * 50)
        print("✅ 密钥来源分析完成")

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
