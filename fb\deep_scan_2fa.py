#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Facebook 2FA深度扫描脚本
========================================
功能描述: 深度扫描所有可能存储2FA数据的位置

主要功能:
1. 扫描所有应用数据目录
2. 检查系统级存储位置
3. 搜索所有可能的2FA相关文件
4. 分析应用使用情况

使用方法:
python fb/deep_scan_2fa.py [emulator_id]
========================================
"""

import asyncio
import sys
import re
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class DeepTwoFactorScanner:
    """深度2FA扫描器"""
    
    def __init__(self, emulator_id: int = 2):
        self.emulator_id = emulator_id
        self.task = None
        
    async def run_deep_scan(self):
        """运行深度扫描"""
        try:
            log_info(f"[深度扫描] 开始深度2FA扫描 - 模拟器{self.emulator_id}", component="DeepScanner")
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            # 扫描1: 检查Facebook应用使用情况
            await self._scan_facebook_usage()
            
            # 扫描2: 全面扫描应用数据
            await self._scan_all_app_data()
            
            # 扫描3: 检查系统存储位置
            await self._scan_system_locations()
            
            # 扫描4: 搜索2FA相关文件
            await self._search_2fa_files()
            
            # 扫描5: 检查认证器应用可能性
            await self._check_authenticator_possibilities()
            
            log_info(f"[深度扫描] 深度2FA扫描完成", component="DeepScanner")
            
        except Exception as e:
            log_error(f"[深度扫描] 深度扫描失败: {e}", component="DeepScanner")

    async def _scan_facebook_usage(self):
        """扫描1: 检查Facebook应用使用情况"""
        try:
            log_info(f"[扫描1] ========== Facebook应用使用情况 ==========", component="DeepScanner")
            
            # 检查应用是否正在运行
            success, result = self.task.ld.execute_ld(self.emulator_id, "ps | grep facebook")
            if success and "facebook" in result:
                log_info(f"[扫描1] ✅ Facebook应用正在运行", component="DeepScanner")
                log_info(f"[扫描1] 进程信息: {result.strip()}", component="DeepScanner")
            else:
                log_info(f"[扫描1] ❌ Facebook应用未运行", component="DeepScanner")
            
            # 检查应用最后使用时间
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys usagestats | grep facebook")
            if success and result.strip():
                log_info(f"[扫描1] 📊 应用使用统计:", component="DeepScanner")
                lines = result.strip().split('\n')[:3]
                for line in lines:
                    if line.strip():
                        log_info(f"[扫描1]   {line.strip()}", component="DeepScanner")
            
            # 检查应用权限使用
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys appops | grep facebook")
            if success and result.strip():
                log_info(f"[扫描1] 🔐 权限使用情况:", component="DeepScanner")
                lines = result.strip().split('\n')[:5]
                for line in lines:
                    if line.strip():
                        log_info(f"[扫描1]   {line.strip()}", component="DeepScanner")
            
            log_info(f"[扫描1] ==========================================", component="DeepScanner")
            
        except Exception as e:
            log_error(f"[扫描1] Facebook使用情况扫描失败: {e}", component="DeepScanner")

    async def _scan_all_app_data(self):
        """扫描2: 全面扫描应用数据"""
        try:
            log_info(f"[扫描2] ========== 全面应用数据扫描 ==========", component="DeepScanner")
            
            package = "com.facebook.katana"
            
            # 扫描所有可能的数据目录
            data_locations = [
                f"/data/data/{package}/",
                f"/data/user/0/{package}/",
                f"/storage/emulated/0/Android/data/{package}/",
                f"/sdcard/Android/data/{package}/",
            ]
            
            for location in data_locations:
                log_info(f"[扫描2] 检查位置: {location}", component="DeepScanner")
                
                success, result = self.task.ld.execute_ld(self.emulator_id, f"find {location} -type f 2>/dev/null | head -20")
                if success and result.strip():
                    files = result.strip().split('\n')
                    log_info(f"[扫描2]   ✅ 找到 {len(files)} 个文件", component="DeepScanner")
                    
                    for file_path in files[:10]:  # 只显示前10个
                        if file_path.strip():
                            # 检查文件大小
                            success2, size_result = self.task.ld.execute_ld(self.emulator_id, f"ls -lh '{file_path}' 2>/dev/null")
                            if success2:
                                size_info = size_result.strip().split()[4] if len(size_result.strip().split()) > 4 else "unknown"
                                log_info(f"[扫描2]     📄 {file_path} ({size_info})", component="DeepScanner")
                else:
                    log_info(f"[扫描2]   ❌ 位置不存在或无文件", component="DeepScanner")
            
            # 特别检查数据库文件
            log_info(f"[扫描2] 特别检查数据库文件:", component="DeepScanner")
            success, result = self.task.ld.execute_ld(self.emulator_id, f"find /data/data/{package} -name '*.db' 2>/dev/null")
            if success and result.strip():
                db_files = result.strip().split('\n')
                log_info(f"[扫描2]   🗄️ 找到 {len(db_files)} 个数据库文件", component="DeepScanner")
                for db_file in db_files:
                    if db_file.strip():
                        log_info(f"[扫描2]     📊 {db_file}", component="DeepScanner")
            else:
                log_info(f"[扫描2]   ❌ 未找到数据库文件", component="DeepScanner")
            
            log_info(f"[扫描2] ==========================================", component="DeepScanner")
            
        except Exception as e:
            log_error(f"[扫描2] 全面应用数据扫描失败: {e}", component="DeepScanner")

    async def _scan_system_locations(self):
        """扫描3: 检查系统存储位置"""
        try:
            log_info(f"[扫描3] ========== 系统存储位置扫描 ==========", component="DeepScanner")
            
            # 系统级可能的2FA存储位置
            system_locations = [
                "/data/system/",
                "/data/misc/",
                "/data/system_ce/0/",
                "/data/system_de/0/",
            ]
            
            for location in system_locations:
                log_info(f"[扫描3] 检查系统位置: {location}", component="DeepScanner")
                
                success, result = self.task.ld.execute_ld(self.emulator_id, f"find {location} -name '*auth*' -o -name '*2fa*' -o -name '*token*' 2>/dev/null | head -10")
                if success and result.strip():
                    files = result.strip().split('\n')
                    log_info(f"[扫描3]   🔍 找到 {len(files)} 个相关文件", component="DeepScanner")
                    for file_path in files:
                        if file_path.strip():
                            log_info(f"[扫描3]     🔐 {file_path}", component="DeepScanner")
                else:
                    log_info(f"[扫描3]   ❌ 未找到相关文件", component="DeepScanner")
            
            # 检查账户管理器
            log_info(f"[扫描3] 检查账户管理器:", component="DeepScanner")
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys account | grep -i facebook")
            if success and result.strip():
                log_info(f"[扫描3]   👤 Facebook账户信息:", component="DeepScanner")
                lines = result.strip().split('\n')[:5]
                for line in lines:
                    if line.strip():
                        log_info(f"[扫描3]     {line.strip()}", component="DeepScanner")
            else:
                log_info(f"[扫描3]   ❌ 未找到Facebook账户", component="DeepScanner")
            
            log_info(f"[扫描3] ==========================================", component="DeepScanner")
            
        except Exception as e:
            log_error(f"[扫描3] 系统存储位置扫描失败: {e}", component="DeepScanner")

    async def _search_2fa_files(self):
        """扫描4: 搜索2FA相关文件"""
        try:
            log_info(f"[扫描4] ========== 2FA相关文件搜索 ==========", component="DeepScanner")
            
            # 在整个系统中搜索2FA相关文件
            search_patterns = [
                "*auth*",
                "*2fa*", 
                "*totp*",
                "*secret*",
                "*token*",
                "*otp*"
            ]
            
            for pattern in search_patterns:
                log_info(f"[扫描4] 搜索模式: {pattern}", component="DeepScanner")
                
                success, result = self.task.ld.execute_ld(self.emulator_id, f"find /data -name '{pattern}' 2>/dev/null | head -10")
                if success and result.strip():
                    files = result.strip().split('\n')
                    log_info(f"[扫描4]   🔍 找到 {len(files)} 个匹配文件", component="DeepScanner")
                    for file_path in files:
                        if file_path.strip() and 'facebook' in file_path.lower():
                            log_info(f"[扫描4]     🎯 Facebook相关: {file_path}", component="DeepScanner")
                        elif file_path.strip():
                            log_info(f"[扫描4]     📄 {file_path}", component="DeepScanner")
                else:
                    log_info(f"[扫描4]   ❌ 未找到匹配文件", component="DeepScanner")
            
            # 搜索包含Base32模式的文件
            log_info(f"[扫描4] 搜索包含可能密钥的文件:", component="DeepScanner")
            success, result = self.task.ld.execute_ld(self.emulator_id, "grep -r '[A-Z2-7]\\{16,\\}' /data/data/com.facebook.katana/ 2>/dev/null | head -5")
            if success and result.strip():
                log_info(f"[扫描4]   🔑 找到可能的密钥模式:", component="DeepScanner")
                lines = result.strip().split('\n')
                for line in lines:
                    if line.strip():
                        log_info(f"[扫描4]     {line.strip()}", component="DeepScanner")
            else:
                log_info(f"[扫描4]   ❌ 未找到密钥模式", component="DeepScanner")
            
            log_info(f"[扫描4] ==========================================", component="DeepScanner")
            
        except Exception as e:
            log_error(f"[扫描4] 2FA文件搜索失败: {e}", component="DeepScanner")

    async def _check_authenticator_possibilities(self):
        """扫描5: 检查认证器应用可能性"""
        try:
            log_info(f"[扫描5] ========== 认证器应用可能性检查 ==========", component="DeepScanner")
            
            # 检查是否有其他可能的认证器应用
            success, result = self.task.ld.execute_ld(self.emulator_id, "pm list packages | grep -E '(auth|otp|2fa|security)'")
            if success and result.strip():
                log_info(f"[扫描5] 🔐 发现可能的认证相关应用:", component="DeepScanner")
                apps = result.strip().split('\n')
                for app in apps:
                    if app.strip():
                        log_info(f"[扫描5]   📱 {app.strip()}", component="DeepScanner")
            else:
                log_info(f"[扫描5] ❌ 未发现认证相关应用", component="DeepScanner")
            
            # 检查浏览器中可能的2FA数据
            browser_packages = [
                "com.android.chrome",
                "com.android.browser",
                "org.mozilla.firefox"
            ]
            
            for browser in browser_packages:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages | grep {browser}")
                if success and browser in result:
                    log_info(f"[扫描5] 🌐 检查浏览器: {browser}", component="DeepScanner")
                    
                    # 检查浏览器数据
                    success2, browser_data = self.task.ld.execute_ld(self.emulator_id, f"find /data/data/{browser} -name '*' 2>/dev/null | wc -l")
                    if success2 and browser_data.strip():
                        file_count = browser_data.strip()
                        log_info(f"[扫描5]   📊 浏览器数据文件数: {file_count}", component="DeepScanner")
            
            # 建议安装认证器应用进行测试
            log_info(f"[扫描5] 💡 建议:", component="DeepScanner")
            log_info(f"[扫描5]   1. 安装Google Authenticator应用", component="DeepScanner")
            log_info(f"[扫描5]   2. 在Facebook中启用2FA", component="DeepScanner")
            log_info(f"[扫描5]   3. 配置认证器应用", component="DeepScanner")
            log_info(f"[扫描5]   4. 重新运行扫描", component="DeepScanner")
            
            log_info(f"[扫描5] ==========================================", component="DeepScanner")
            
        except Exception as e:
            log_error(f"[扫描5] 认证器应用检查失败: {e}", component="DeepScanner")

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2
        
        print(f"🔍 开始Facebook 2FA深度扫描 - 模拟器{emulator_id}")
        print("=" * 60)
        
        # 创建深度扫描器
        scanner = DeepTwoFactorScanner(emulator_id)
        
        # 运行深度扫描
        await scanner.run_deep_scan()
        
        print("=" * 60)
        print("✅ Facebook 2FA深度扫描完成")
        
    except KeyboardInterrupt:
        print("\n⚠️  扫描被用户中断")
    except Exception as e:
        print(f"❌ 扫描执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
