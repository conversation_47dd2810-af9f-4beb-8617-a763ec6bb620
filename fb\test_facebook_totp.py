#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 Facebook TOTP密钥真实性测试工具
========================================
功能描述: 测试TOTP密钥是否真的对应Facebook账户

核心功能:
1. 生成TOTP验证码
2. 尝试在Facebook中验证
3. 模拟登录验证过程
4. 确认密钥真实性

使用方法:
python fb/test_facebook_totp.py [emulator_id]

注意事项:
- 需要Facebook应用已登录
- 会尝试触发2FA验证
========================================
"""

import asyncio
import sys
import re
import json
import base64
import tempfile
import struct
import hashlib
import time
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class FacebookTOTPTester:
    """Facebook TOTP密钥测试器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化测试器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.facebook_package = "com.facebook.katana"
        
        log_info(f"[TOTP测试] Facebook TOTP测试器初始化 - 模拟器{emulator_id}", component="TOTPTester")

    async def test_totp_key(self, secret_key: str):
        """测试TOTP密钥"""
        try:
            log_info(f"[TOTP测试] 开始测试TOTP密钥", component="TOTPTester")
            
            print("🔍 Facebook TOTP密钥真实性测试")
            print("=" * 50)
            print(f"🔑 测试密钥: {secret_key}")
            print("⚠️  将尝试验证此密钥是否对应您的Facebook账户")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return False
            
            # 第一步：生成当前验证码
            current_codes = await self._generate_verification_codes(secret_key)
            
            # 第二步：检查Facebook应用状态
            await self._check_facebook_status()
            
            # 第三步：尝试触发2FA验证
            verification_triggered = await self._trigger_2fa_verification()
            
            # 第四步：尝试输入验证码
            if verification_triggered:
                success = await self._attempt_code_verification(current_codes)
                if success:
                    print("🎉 密钥验证成功！这是您Facebook账户的真实TOTP密钥！")
                    return True
                else:
                    print("❌ 密钥验证失败，这可能不是您Facebook账户的真实密钥")
                    return False
            else:
                print("⚠️  无法触发2FA验证，可能需要手动测试")
                await self._provide_manual_test_instructions(current_codes)
                return None
            
        except Exception as e:
            log_error(f"[TOTP测试] TOTP密钥测试失败: {e}", component="TOTPTester")
            return False

    async def _generate_verification_codes(self, secret_key: str) -> List[str]:
        """生成验证码"""
        try:
            print("🔢 生成TOTP验证码...")
            print("-" * 40)
            
            codes = []
            
            # 生成当前时间窗口的验证码
            current_time = int(time.time())
            
            # 生成前一个、当前、下一个时间窗口的验证码
            for offset in [-30, 0, 30]:
                timestamp = (current_time + offset) // 30
                code = self._generate_totp_code(secret_key, timestamp)
                codes.append(code)
                
                time_desc = "前一个" if offset == -30 else ("当前" if offset == 0 else "下一个")
                print(f"   {time_desc}验证码: {code}")
            
            print(f"✅ 生成了 {len(codes)} 个验证码")
            print()
            return codes
            
        except Exception as e:
            log_error(f"[TOTP测试] 验证码生成失败: {e}", component="TOTPTester")
            return []

    def _generate_totp_code(self, secret: str, timestamp: int) -> str:
        """生成TOTP验证码"""
        try:
            import hmac
            import hashlib
            import struct
            
            # 解码Base32密钥
            key = base64.b32decode(secret)
            
            # 生成TOTP
            msg = struct.pack('>Q', timestamp)
            hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
            offset = hmac_digest[-1] & 0x0f
            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
            code = (code & 0x7fffffff) % 1000000
            
            return f"{code:06d}"
            
        except Exception:
            return "000000"

    async def _check_facebook_status(self):
        """检查Facebook应用状态"""
        try:
            print("📱 检查Facebook应用状态...")
            print("-" * 40)
            
            # 检查应用是否安装
            success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages | grep {self.facebook_package}")
            if success and self.facebook_package in result:
                print("✅ Facebook应用已安装")
            else:
                print("❌ Facebook应用未安装")
                return False
            
            # 检查应用是否正在运行
            success, result = self.task.ld.execute_ld(self.emulator_id, f"ps | grep {self.facebook_package}")
            if success and result.strip():
                print("✅ Facebook应用正在运行")
            else:
                print("⚠️  Facebook应用未运行，尝试启动...")
                # 启动Facebook应用
                success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"am start -n {self.facebook_package}/.LoginActivity")
                if success2:
                    print("✅ Facebook应用启动成功")
                    await asyncio.sleep(5)  # 等待应用启动
                else:
                    print("❌ Facebook应用启动失败")
            
            print("✅ Facebook应用状态检查完成")
            print()
            return True
            
        except Exception as e:
            log_error(f"[TOTP测试] Facebook状态检查失败: {e}", component="TOTPTester")
            return False

    async def _trigger_2fa_verification(self) -> bool:
        """触发2FA验证"""
        try:
            print("🔐 尝试触发2FA验证...")
            print("-" * 40)
            
            # 方法1: 尝试访问安全设置
            print("🔍 方法1: 访问安全设置...")
            success1, result1 = self.task.ld.execute_ld(self.emulator_id, 
                "am start -a android.intent.action.VIEW -d 'https://www.facebook.com/settings/security'")
            if success1:
                print("   ✅ 安全设置页面启动成功")
                await asyncio.sleep(3)
            
            # 方法2: 发送2FA相关广播
            print("📡 方法2: 发送2FA广播...")
            broadcast_actions = [
                "am broadcast -a com.facebook.katana.ACTION_2FA_REQUEST",
                "am broadcast -a com.facebook.katana.ACTION_SECURITY_CHECK"
            ]
            
            for action in broadcast_actions:
                success2, result2 = self.task.ld.execute_ld(self.emulator_id, action)
                if success2:
                    print("   ✅ 广播发送成功")
                await asyncio.sleep(1)
            
            # 方法3: 模拟登录操作
            print("🔑 方法3: 模拟登录操作...")
            success3, result3 = self.task.ld.execute_ld(self.emulator_id, f"am start -n {self.facebook_package}/.LoginActivity")
            if success3:
                print("   ✅ 登录页面启动成功")
                await asyncio.sleep(2)
            
            # 方法4: 尝试注销再登录
            print("🔄 方法4: 尝试注销操作...")
            success4, result4 = self.task.ld.execute_ld(self.emulator_id, 
                "am broadcast -a com.facebook.katana.ACTION_LOGOUT")
            if success4:
                print("   ✅ 注销广播发送成功")
                await asyncio.sleep(2)
            
            print("✅ 2FA触发尝试完成")
            print()
            
            # 检查是否出现了2FA输入界面
            return await self._check_for_2fa_input_screen()
            
        except Exception as e:
            log_error(f"[TOTP测试] 2FA触发失败: {e}", component="TOTPTester")
            return False

    async def _check_for_2fa_input_screen(self) -> bool:
        """检查是否出现2FA输入界面"""
        try:
            print("👀 检查2FA输入界面...")
            
            # 截图检查
            screenshot_path = "/sdcard/2fa_check.png"
            success, result = self.task.ld.execute_ld(self.emulator_id, f"screencap -p {screenshot_path}")
            if success:
                print("   📸 截图成功")
                
                # 检查当前界面文本
                success2, result2 = self.task.ld.execute_ld(self.emulator_id, "dumpsys window windows | grep -E 'mCurrentFocus|mFocusedApp'")
                if success2 and result2.strip():
                    current_focus = result2.strip()
                    print(f"   🔍 当前焦点: {current_focus}")
                    
                    # 检查是否包含2FA相关关键词
                    if any(keyword in current_focus.lower() for keyword in ['2fa', 'two', 'factor', 'code', 'verify', 'security']):
                        print("   ✅ 可能出现了2FA相关界面")
                        return True
            
            print("   ❌ 未检测到2FA输入界面")
            return False
            
        except Exception as e:
            log_error(f"[TOTP测试] 2FA界面检查失败: {e}", component="TOTPTester")
            return False

    async def _attempt_code_verification(self, codes: List[str]) -> bool:
        """尝试验证码验证"""
        try:
            print("🔢 尝试输入验证码...")
            print("-" * 40)
            
            for i, code in enumerate(codes):
                print(f"🔍 尝试验证码 {i+1}: {code}")
                
                # 清除输入框
                self.task.ld.execute_ld(self.emulator_id, "input keyevent KEYCODE_CTRL_A")
                self.task.ld.execute_ld(self.emulator_id, "input keyevent KEYCODE_DEL")
                await asyncio.sleep(1)
                
                # 输入验证码
                success, result = self.task.ld.execute_ld(self.emulator_id, f"input text {code}")
                if success:
                    print(f"   ✅ 验证码输入成功")
                    await asyncio.sleep(1)
                    
                    # 点击确认按钮
                    self.task.ld.execute_ld(self.emulator_id, "input keyevent KEYCODE_ENTER")
                    await asyncio.sleep(3)
                    
                    # 检查是否验证成功
                    if await self._check_verification_result():
                        print(f"   🎉 验证码 {code} 验证成功！")
                        return True
                    else:
                        print(f"   ❌ 验证码 {code} 验证失败")
                else:
                    print(f"   ❌ 验证码输入失败")
            
            print("❌ 所有验证码都验证失败")
            return False
            
        except Exception as e:
            log_error(f"[TOTP测试] 验证码验证失败: {e}", component="TOTPTester")
            return False

    async def _check_verification_result(self) -> bool:
        """检查验证结果"""
        try:
            # 等待页面响应
            await asyncio.sleep(2)
            
            # 检查当前界面
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys window windows | grep -E 'mCurrentFocus'")
            if success and result.strip():
                current_focus = result.strip()
                
                # 检查是否包含成功相关关键词
                if any(keyword in current_focus.lower() for keyword in ['success', 'welcome', 'home', 'feed']):
                    return True
                
                # 检查是否仍在验证界面 (失败)
                if any(keyword in current_focus.lower() for keyword in ['code', 'verify', 'invalid', 'error']):
                    return False
            
            return False
            
        except Exception:
            return False

    async def _provide_manual_test_instructions(self, codes: List[str]):
        """提供手动测试指导"""
        try:
            print("📋 手动测试指导")
            print("=" * 50)
            
            print("由于无法自动触发2FA验证，请手动进行以下测试：")
            print()
            
            print("🔧 手动测试步骤:")
            print("1. 打开Facebook应用")
            print("2. 进入设置 → 安全和登录 → 双重验证")
            print("3. 如果要求输入验证码，请尝试以下验证码：")
            print()
            
            for i, code in enumerate(codes):
                print(f"   验证码 {i+1}: {code}")
            
            print()
            print("4. 如果任何一个验证码被接受，说明密钥是真实的")
            print("5. 如果所有验证码都被拒绝，说明密钥可能不正确")
            print()
            
            print("💡 其他测试方法:")
            print("- 尝试注销Facebook账户然后重新登录")
            print("- 在登录时选择使用认证器应用")
            print("- 输入上述验证码之一")
            print()
            
            print("🔄 验证码会每30秒更新一次，请及时使用")
            
        except Exception as e:
            log_error(f"[TOTP测试] 手动测试指导失败: {e}", component="TOTPTester")

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2
        
        print("🔍 Facebook TOTP密钥真实性测试工具")
        print("⚠️  将测试TOTP密钥是否对应您的Facebook账户")
        print("⚠️  需要Facebook应用已登录")
        print()
        
        # 要测试的密钥
        test_key = "XZJKVEGNFN6S2C2Z7LTDRW5TUZYH4WPU"
        
        confirm = input(f"确认测试密钥 {test_key}? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return
        
        # 创建测试器
        tester = FacebookTOTPTester(emulator_id)
        
        # 测试TOTP密钥
        result = await tester.test_totp_key(test_key)
        
        print("\n" + "=" * 50)
        if result is True:
            print("✅ 密钥验证成功！这是您Facebook账户的真实TOTP密钥！")
        elif result is False:
            print("❌ 密钥验证失败，这可能不是您Facebook账户的真实密钥")
        else:
            print("⚠️  无法自动验证，请参考手动测试指导")
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
