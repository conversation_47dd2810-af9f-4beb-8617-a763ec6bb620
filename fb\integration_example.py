#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Facebook 2FA提取功能集成示例
========================================
功能描述: 展示如何将Facebook 2FA提取功能集成到现有的任务流程中

集成方式:
1. 异步桥梁集成 - 通过async_bridge.py调用
2. UI界面集成 - 添加到主窗口界面
3. 配置管理集成 - 使用统一的配置系统
4. 任务调度集成 - 与现有任务流程协调

使用场景:
- 作为独立任务执行
- 与Instagram任务配合使用
- 批量模拟器处理
- 定时任务执行

注意事项:
- 本文件仅为集成示例，实际集成需要根据具体需求调整
- 建议先运行测试脚本验证功能正常
========================================
"""

import asyncio
import sys
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

# ============================================================================
# 🎯 1. 异步桥梁集成示例
# ============================================================================

class FacebookTwoFactorBridge:
    """Facebook 2FA任务异步桥梁"""
    
    def __init__(self):
        self.active_tasks = {}  # 活跃任务字典
        
    async def execute_fb_2fa_task(self, emulator_ids: List[int], task_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行Facebook 2FA提取任务
        
        Args:
            emulator_ids: 模拟器ID列表
            task_config: 任务配置
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            log_info(f"开始执行Facebook 2FA提取任务 - 模拟器: {emulator_ids}", component="FacebookTwoFactorBridge")
            
            # 并发执行多个模拟器的任务
            tasks = []
            for emulator_id in emulator_ids:
                task = FacebookTwoFactorTask(emulator_id)
                tasks.append(self._execute_single_task(emulator_id, task))
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            success_count = 0
            total_secrets = 0
            task_results = {}
            
            for i, result in enumerate(results):
                emulator_id = emulator_ids[i]
                if isinstance(result, Exception):
                    task_results[emulator_id] = {
                        'success': False,
                        'error': str(result)
                    }
                else:
                    task_results[emulator_id] = result
                    if result.get('success', False):
                        success_count += 1
                        extraction_result = result.get('extraction_result', {})
                        total_secrets += len(extraction_result.get('secrets', []))
            
            # 创建汇总结果
            summary_result = {
                'success': success_count > 0,
                'total_emulators': len(emulator_ids),
                'successful_emulators': success_count,
                'total_secrets_found': total_secrets,
                'task_results': task_results,
                'timestamp': asyncio.get_event_loop().time()
            }
            
            log_info(f"Facebook 2FA提取任务完成 - 成功: {success_count}/{len(emulator_ids)}, 总密钥: {total_secrets}", component="FacebookTwoFactorBridge")
            return summary_result
            
        except Exception as e:
            log_error(f"Facebook 2FA提取任务执行失败: {e}", component="FacebookTwoFactorBridge")
            return {
                'success': False,
                'error': str(e),
                'timestamp': asyncio.get_event_loop().time()
            }
    
    async def _execute_single_task(self, emulator_id: int, task: FacebookTwoFactorTask) -> Dict[str, Any]:
        """执行单个模拟器的任务"""
        try:
            self.active_tasks[emulator_id] = task
            result = await task.execute()
            return result
        finally:
            if emulator_id in self.active_tasks:
                del self.active_tasks[emulator_id]

# ============================================================================
# 🎯 2. 批量处理示例
# ============================================================================

class FacebookTwoFactorBatchProcessor:
    """Facebook 2FA批量处理器"""
    
    def __init__(self):
        self.bridge = FacebookTwoFactorBridge()
    
    async def process_all_emulators(self, emulator_range: range = range(1, 5)) -> Dict[str, Any]:
        """
        处理所有模拟器的Facebook 2FA提取
        
        Args:
            emulator_range: 模拟器ID范围
            
        Returns:
            Dict[str, Any]: 批量处理结果
        """
        try:
            log_info(f"开始批量处理Facebook 2FA提取 - 模拟器范围: {list(emulator_range)}", component="FacebookTwoFactorBatchProcessor")
            
            # 分批处理，避免同时启动过多任务
            batch_size = 3  # 每批处理3个模拟器
            all_results = {}
            
            emulator_list = list(emulator_range)
            for i in range(0, len(emulator_list), batch_size):
                batch = emulator_list[i:i + batch_size]
                log_info(f"处理批次: {batch}", component="FacebookTwoFactorBatchProcessor")
                
                batch_result = await self.bridge.execute_fb_2fa_task(batch)
                all_results.update(batch_result.get('task_results', {}))
                
                # 批次间延迟
                if i + batch_size < len(emulator_list):
                    await asyncio.sleep(2)
            
            # 生成汇总报告
            summary = self._generate_batch_summary(all_results)
            log_info(f"批量处理完成: {summary}", component="FacebookTwoFactorBatchProcessor")
            
            return {
                'success': True,
                'summary': summary,
                'detailed_results': all_results
            }
            
        except Exception as e:
            log_error(f"批量处理失败: {e}", component="FacebookTwoFactorBatchProcessor")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_batch_summary(self, results: Dict[int, Dict[str, Any]]) -> Dict[str, Any]:
        """生成批量处理摘要"""
        total_emulators = len(results)
        successful_emulators = len([r for r in results.values() if r.get('success', False)])
        total_secrets = 0
        
        for result in results.values():
            if result.get('success', False):
                extraction_result = result.get('extraction_result', {})
                total_secrets += len(extraction_result.get('secrets', []))
        
        return {
            'total_emulators': total_emulators,
            'successful_emulators': successful_emulators,
            'failed_emulators': total_emulators - successful_emulators,
            'success_rate': f"{successful_emulators/total_emulators*100:.1f}%" if total_emulators > 0 else "0%",
            'total_secrets_found': total_secrets,
            'average_secrets_per_emulator': f"{total_secrets/successful_emulators:.1f}" if successful_emulators > 0 else "0"
        }

# ============================================================================
# 🎯 3. 与现有任务流程集成示例
# ============================================================================

class IntegratedTaskFlow:
    """集成任务流程示例"""
    
    def __init__(self):
        self.fb_bridge = FacebookTwoFactorBridge()
    
    async def execute_combined_task(self, emulator_id: int) -> Dict[str, Any]:
        """
        执行组合任务：先提取2FA密钥，再执行其他任务
        
        Args:
            emulator_id: 模拟器ID
            
        Returns:
            Dict[str, Any]: 组合任务结果
        """
        try:
            log_info(f"[模拟器{emulator_id}] 开始执行组合任务", component="IntegratedTaskFlow")
            
            # 阶段1: 提取Facebook 2FA密钥
            log_info(f"[模拟器{emulator_id}] 阶段1: 提取Facebook 2FA密钥", component="IntegratedTaskFlow")
            fb_result = await self.fb_bridge.execute_fb_2fa_task([emulator_id])
            
            # 检查2FA提取结果
            fb_task_result = fb_result.get('task_results', {}).get(emulator_id, {})
            if not fb_task_result.get('success', False):
                log_warning(f"[模拟器{emulator_id}] 2FA提取失败，继续执行其他任务", component="IntegratedTaskFlow")
            else:
                extraction_result = fb_task_result.get('extraction_result', {})
                secrets_count = len(extraction_result.get('secrets', []))
                log_info(f"[模拟器{emulator_id}] 2FA提取成功，找到{secrets_count}个密钥", component="IntegratedTaskFlow")
            
            # 阶段2: 执行其他任务（示例）
            log_info(f"[模拟器{emulator_id}] 阶段2: 执行其他任务", component="IntegratedTaskFlow")
            other_result = await self._execute_other_tasks(emulator_id)
            
            # 合并结果
            combined_result = {
                'success': True,
                'emulator_id': emulator_id,
                'fb_2fa_result': fb_task_result,
                'other_tasks_result': other_result,
                'timestamp': asyncio.get_event_loop().time()
            }
            
            log_info(f"[模拟器{emulator_id}] 组合任务执行完成", component="IntegratedTaskFlow")
            return combined_result
            
        except Exception as e:
            log_error(f"[模拟器{emulator_id}] 组合任务执行失败: {e}", component="IntegratedTaskFlow")
            return {
                'success': False,
                'emulator_id': emulator_id,
                'error': str(e),
                'timestamp': asyncio.get_event_loop().time()
            }
    
    async def _execute_other_tasks(self, emulator_id: int) -> Dict[str, Any]:
        """执行其他任务的示例方法"""
        # 这里可以集成Instagram任务或其他现有任务
        await asyncio.sleep(1)  # 模拟任务执行时间
        return {
            'success': True,
            'message': '其他任务执行成功'
        }

# ============================================================================
# 🎯 4. 使用示例
# ============================================================================

async def example_usage():
    """使用示例"""
    print("🚀 Facebook 2FA提取功能集成示例")
    print("=" * 50)
    
    try:
        # 示例1: 单个模拟器提取
        print("\n📋 示例1: 单个模拟器提取")
        bridge = FacebookTwoFactorBridge()
        result = await bridge.execute_fb_2fa_task([2])
        print(f"结果: {result.get('success', False)}")
        
        # 示例2: 批量处理
        print("\n📋 示例2: 批量处理")
        batch_processor = FacebookTwoFactorBatchProcessor()
        batch_result = await batch_processor.process_all_emulators(range(2, 4))
        print(f"批量处理结果: {batch_result.get('success', False)}")
        
        # 示例3: 集成任务流程
        print("\n📋 示例3: 集成任务流程")
        integrated_flow = IntegratedTaskFlow()
        combined_result = await integrated_flow.execute_combined_task(2)
        print(f"组合任务结果: {combined_result.get('success', False)}")
        
        print("\n✅ 所有示例执行完成")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(example_usage())
