#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 模拟器2基础诊断工具
========================================
功能描述: 诊断模拟器2的基础状态和问题

核心功能:
1. 检查模拟器连接状态
2. 检查网络连接
3. 检查已安装应用
4. 测试基础操作
5. 提供解决方案

使用方法:
python fb/emulator_diagnosis.py
========================================
"""

import subprocess
import time

def diagnose_emulator():
    """诊断模拟器2"""
    print("🔧 模拟器2基础诊断工具")
    print("=" * 50)
    
    emulator_id = 2
    ldconsole_path = "G:/leidian/LDPlayer9/ldconsole.exe"
    
    print(f"📱 诊断目标: 模拟器{emulator_id}")
    print()
    
    # 诊断1: 检查模拟器基础状态
    print("🔍 诊断1: 检查模拟器基础状态...")
    try:
        result = subprocess.run([ldconsole_path, "list2"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            found = False
            for line in lines:
                if f"雷电模拟器-{emulator_id}" in line:
                    print(f"   ✅ 模拟器{emulator_id}存在")
                    parts = line.split(',')
                    if len(parts) >= 5:
                        status = parts[4].strip()
                        if status == "1":
                            print(f"   ✅ 模拟器{emulator_id}正在运行")
                        else:
                            print(f"   ❌ 模拟器{emulator_id}未运行 (状态: {status})")
                            return False
                    found = True
                    break
            
            if not found:
                print(f"   ❌ 未找到模拟器{emulator_id}")
                return False
        else:
            print("   ❌ 无法获取模拟器列表")
            return False
    except Exception as e:
        print(f"   ❌ 检查模拟器状态失败: {e}")
        return False
    
    # 诊断2: 测试ADB连接
    print("\n🔍 诊断2: 测试ADB连接...")
    test_commands = [
        ("获取Android版本", "getprop ro.build.version.release"),
        ("获取设备型号", "getprop ro.product.model"),
        ("获取当前时间", "date"),
        ("检查存储空间", "df /data | head -2")
    ]
    
    adb_working = True
    for desc, cmd in test_commands:
        print(f"   🔍 {desc}...")
        try:
            full_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", f"shell {cmd}"]
            result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                output = result.stdout.strip()
                # 检查是否是真正的Android输出
                if "dnplayer" in output.lower() or "command line" in output.lower():
                    print(f"      ❌ ADB命令返回错误格式: {output[:50]}...")
                    adb_working = False
                else:
                    print(f"      ✅ 成功: {output[:50]}...")
            else:
                print(f"      ❌ 失败或无输出")
                adb_working = False
        except Exception as e:
            print(f"      ❌ 异常: {e}")
            adb_working = False
    
    if not adb_working:
        print("   ❌ ADB连接存在问题")
        return False
    
    # 诊断3: 检查网络连接
    print("\n🔍 诊断3: 检查网络连接...")
    network_tests = [
        ("Ping Google DNS", "ping -c 1 *******"),
        ("Ping百度", "ping -c 1 baidu.com"),
        ("检查网络接口", "ip addr show | grep inet")
    ]
    
    network_working = False
    for desc, cmd in network_tests:
        print(f"   🔍 {desc}...")
        try:
            full_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", f"shell {cmd}"]
            result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0 and result.stdout.strip():
                output = result.stdout.strip()
                if "1 packets transmitted, 1 received" in output or "inet" in output:
                    print(f"      ✅ 网络正常")
                    network_working = True
                    break
                else:
                    print(f"      ⚠️  网络测试结果: {output[:50]}...")
            else:
                print(f"      ❌ 网络测试失败")
        except Exception as e:
            print(f"      ❌ 网络测试异常: {e}")
    
    if not network_working:
        print("   ❌ 网络连接存在问题")
    
    # 诊断4: 检查已安装应用
    print("\n🔍 诊断4: 检查已安装应用...")
    try:
        full_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", "shell pm list packages"]
        result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0 and result.stdout.strip():
            packages = result.stdout.strip().split('\n')
            print(f"   📊 总共安装了 {len(packages)} 个应用")
            
            # 检查关键应用
            key_apps = {
                "Chrome浏览器": "com.android.chrome",
                "系统浏览器": "com.android.browser", 
                "Firefox": "org.mozilla.firefox",
                "Facebook": "com.facebook.katana",
                "Facebook Lite": "com.facebook.lite",
                "设置": "com.android.settings"
            }
            
            found_apps = []
            for app_name, package_name in key_apps.items():
                if any(package_name in pkg for pkg in packages):
                    found_apps.append(app_name)
                    print(f"      ✅ {app_name}: 已安装")
                else:
                    print(f"      ❌ {app_name}: 未安装")
            
            if not found_apps:
                print("   ❌ 没有找到任何关键应用")
                return False
                
        else:
            print("   ❌ 无法获取应用列表")
            return False
    except Exception as e:
        print(f"   ❌ 检查应用失败: {e}")
        return False
    
    # 诊断5: 测试屏幕操作
    print("\n🔍 诊断5: 测试屏幕操作...")
    screen_tests = [
        ("屏幕截图", "screencap -p /sdcard/test_screenshot.png"),
        ("获取屏幕尺寸", "wm size"),
        ("获取屏幕密度", "wm density")
    ]
    
    screen_working = True
    for desc, cmd in screen_tests:
        print(f"   🔍 {desc}...")
        try:
            full_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", f"shell {cmd}"]
            result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                if result.stdout.strip():
                    print(f"      ✅ 成功: {result.stdout.strip()[:50]}...")
                else:
                    print(f"      ✅ 成功执行")
            else:
                print(f"      ❌ 失败")
                screen_working = False
        except Exception as e:
            print(f"      ❌ 异常: {e}")
            screen_working = False
    
    # 诊断6: 测试简单的屏幕点击
    print("\n🔍 诊断6: 测试屏幕点击...")
    print("   🔍 执行屏幕点击测试...")
    try:
        # 点击屏幕中央
        full_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", "shell input tap 360 640"]
        result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("      ✅ 屏幕点击命令执行成功")
            time.sleep(1)
            
            # 按Home键
            full_cmd2 = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", "shell input keyevent KEYCODE_HOME"]
            result2 = subprocess.run(full_cmd2, capture_output=True, text=True, timeout=10)
            
            if result2.returncode == 0:
                print("      ✅ 按键操作执行成功")
            else:
                print("      ❌ 按键操作失败")
        else:
            print("      ❌ 屏幕点击失败")
    except Exception as e:
        print(f"      ❌ 屏幕操作异常: {e}")
    
    return True

def provide_solutions():
    """提供解决方案"""
    print("\n🔧 解决方案建议")
    print("=" * 50)
    
    print("基于诊断结果，建议尝试以下解决方案:")
    print()
    
    print("💡 方案1: 手动操作")
    print("1. 直接在模拟器2中手动点击")
    print("2. 寻找浏览器应用图标")
    print("3. 手动输入Facebook网址")
    print("4. 完成密码重置")
    print()
    
    print("💡 方案2: 重启模拟器")
    print("1. 关闭模拟器2")
    print("2. 重新启动模拟器2") 
    print("3. 等待完全启动后再试")
    print()
    
    print("💡 方案3: 检查网络设置")
    print("1. 确保模拟器有网络连接")
    print("2. 检查代理设置")
    print("3. 尝试切换网络模式")
    print()
    
    print("💡 方案4: 使用其他模拟器")
    print("1. 检查其他模拟器是否有Facebook")
    print("2. 在有Facebook的模拟器中操作")
    print("3. 或者安装Facebook到模拟器2")
    print()
    
    print("💡 方案5: 直接在电脑浏览器操作")
    print("1. 在电脑上打开浏览器")
    print("2. 访问: https://www.facebook.com/login/identify")
    print("3. 输入邮箱地址重置密码")
    print("4. 这是最直接有效的方法")

def main():
    """主函数"""
    try:
        print("🔧 模拟器2基础诊断工具")
        print("⚠️ 诊断模拟器2的基础状态和问题")
        print()
        
        confirm = input("确认开始诊断模拟器2? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 诊断取消")
            return
        
        # 执行诊断
        success = diagnose_emulator()
        
        if success:
            print("\n✅ 基础诊断完成，模拟器状态正常")
            print("💡 但Facebook跳转仍然失败，可能需要手动操作")
        else:
            print("\n❌ 诊断发现问题，模拟器状态异常")
        
        # 提供解决方案
        provide_solutions()
        
        print("\n" + "=" * 50)
        print("🎯 最直接的解决方案:")
        print("在您的电脑浏览器中访问:")
        print("https://www.facebook.com/login/identify")
        print("输入邮箱地址，完成密码重置")
        
    except KeyboardInterrupt:
        print("\n⚠️ 诊断被用户中断")
    except Exception as e:
        print(f"❌ 诊断执行失败: {e}")

if __name__ == "__main__":
    main()
