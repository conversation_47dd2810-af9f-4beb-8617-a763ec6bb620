2025-07-31 13:17:23 - root - INFO - 简化日志系统初始化完成
2025-07-31 13:17:23 - DeepScanner - INFO - [深度扫描] 开始深度2FA扫描 - 模拟器2
2025-07-31 13:17:23 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-31 13:17:23 - FacebookTwoFactorTask - INFO - [模拟器2] 雷电模拟器API初始化成功
2025-07-31 13:17:23 - FacebookTwoFactorTask - INFO - [模拟器2] 模拟器路径: G:/leidian/LDPlayer9
2025-07-31 13:17:23 - FacebookTwoFactorTask - INFO - [模拟器2] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-31 13:17:23 - FacebookTwoFactorTask - INFO - [模拟器2] FB 2FA任务配置加载完成
2025-07-31 13:17:23 - FacebookTwoFactorTask - INFO - [模拟器2] Facebook 2FA任务执行器初始化完成
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描1] ========== Facebook应用使用情况 ==========
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描1] ❌ Facebook应用未运行
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描1] 📊 应用使用统计:
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描1]   time="2025-07-31 11:38:50" type=MOVE_TO_FOREGROUND package=com.facebook.katana class=com.facebook.katana.activity.FbMainTabActivity flags=0x0
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描1]   time="2025-07-31 11:39:15" type=MOVE_TO_BACKGROUND package=com.facebook.katana class=com.facebook.katana.activity.FbMainTabActivity flags=0x0
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描1]   time="2025-07-31 11:39:17" type=MOVE_TO_FOREGROUND package=com.facebook.katana class=com.facebook.katana.activity.FbMainTabActivity flags=0x0
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描1] 🔐 权限使用情况:
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描1]   Package com.facebook.katana:
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描1] ==========================================
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描2] ========== 全面应用数据扫描 ==========
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描2] 检查位置: /data/data/com.facebook.katana/
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描2]   ❌ 位置不存在或无文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描2] 检查位置: /data/user/0/com.facebook.katana/
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描2]   ❌ 位置不存在或无文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描2] 检查位置: /storage/emulated/0/Android/data/com.facebook.katana/
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描2]   ❌ 位置不存在或无文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描2] 检查位置: /sdcard/Android/data/com.facebook.katana/
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描2]   ❌ 位置不存在或无文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描2] 特别检查数据库文件:
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描2]   ❌ 未找到数据库文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描2] ==========================================
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3] ========== 系统存储位置扫描 ==========
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3] 检查系统位置: /data/system/
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3]   ❌ 未找到相关文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3] 检查系统位置: /data/misc/
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3]   ❌ 未找到相关文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3] 检查系统位置: /data/system_ce/0/
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3]   ❌ 未找到相关文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3] 检查系统位置: /data/system_de/0/
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3]   ❌ 未找到相关文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3] 检查账户管理器:
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3]   👤 Facebook账户信息:
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3]     Account {name=**************, type=com.facebook.auth.login}
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3]     ServiceInfo: AuthenticatorDescription {type=com.facebook.auth.login}, ComponentInfo{com.facebook.katana/com.facebook.katana.platform.FacebookAuthenticationService}, uid 10052
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描3] ==========================================
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4] ========== 2FA相关文件搜索 ==========
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4] 搜索模式: *auth*
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4]   ❌ 未找到匹配文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4] 搜索模式: *2fa*
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4]   ❌ 未找到匹配文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4] 搜索模式: *totp*
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4]   ❌ 未找到匹配文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4] 搜索模式: *secret*
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4]   ❌ 未找到匹配文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4] 搜索模式: *token*
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4]   ❌ 未找到匹配文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4] 搜索模式: *otp*
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4]   ❌ 未找到匹配文件
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4] 搜索包含可能密钥的文件:
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4]   ❌ 未找到密钥模式
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描4] ==========================================
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描5] ========== 认证器应用可能性检查 ==========
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描5] ❌ 未发现认证相关应用
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描5] 🌐 检查浏览器: com.android.browser
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描5] 💡 建议:
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描5]   1. 安装Google Authenticator应用
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描5]   2. 在Facebook中启用2FA
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描5]   3. 配置认证器应用
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描5]   4. 重新运行扫描
2025-07-31 13:17:23 - DeepScanner - INFO - [扫描5] ==========================================
2025-07-31 13:17:23 - DeepScanner - INFO - [深度扫描] 深度2FA扫描完成
