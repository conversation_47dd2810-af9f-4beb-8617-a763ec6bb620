#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 Facebook 真实2FA密钥强制提取工具
========================================
功能描述: 从Facebook应用中强制提取真实存在的2FA密钥

核心策略:
1. 深度扫描所有可能的数据存储位置
2. 解密和解析加密的数据文件
3. 从内存中实时捕获2FA相关数据
4. 分析网络请求中的认证信息
5. 逆向工程Facebook应用的2FA实现

适用场景:
- Facebook已启用2FA但无法访问设置界面
- 需要提取真实的2FA密钥而非生成测试密钥
- 手机号码更换导致无法正常访问2FA设置

使用方法:
python fb/extract_real_2fa_secrets.py [emulator_id]

注意事项:
- 需要Facebook应用已启用2FA
- 需要root权限进行深度扫描
- 仅用于合法的账户恢复目的
========================================
"""

import asyncio
import sys
import re
import json
import base64
import sqlite3
import tempfile
import struct
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class FacebookRealSecretExtractor:
    """Facebook 真实2FA密钥提取器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化真实密钥提取器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.temp_dir = Path(tempfile.gettempdir()) / f"fb_real_extract_{emulator_id}"
        self.temp_dir.mkdir(exist_ok=True)
        
        # Facebook可能存储2FA数据的所有位置
        self.facebook_data_locations = [
            # 主应用数据目录
            "/data/data/com.facebook.katana/",
            "/data/user/0/com.facebook.katana/",
            
            # 数据库目录
            "/data/data/com.facebook.katana/databases/",
            "/data/data/com.facebook.katana/app_webview/databases/",
            "/data/data/com.facebook.katana/app_textures/databases/",
            
            # 配置文件目录
            "/data/data/com.facebook.katana/shared_prefs/",
            "/data/data/com.facebook.katana/files/",
            "/data/data/com.facebook.katana/cache/",
            "/data/data/com.facebook.katana/code_cache/",
            
            # 外部存储
            "/storage/emulated/0/Android/data/com.facebook.katana/",
            "/sdcard/Android/data/com.facebook.katana/",
            
            # 系统级存储
            "/data/system_ce/0/accounts/",
            "/data/system/users/0/",
            
            # 可能的隐藏目录
            "/data/data/com.facebook.katana/.facebook/",
            "/data/data/com.facebook.katana/.auth/",
            "/data/data/com.facebook.katana/.security/",
        ]
        
        # 可能包含2FA密钥的文件模式
        self.secret_file_patterns = [
            "*.db", "*.sqlite", "*.sqlite3",
            "*.json", "*.xml", "*.plist",
            "*auth*", "*2fa*", "*totp*", "*secret*", "*token*", "*otp*",
            "*security*", "*verify*", "*code*", "*key*",
            "*.dat", "*.bin", "*.enc", "*.encrypted",
            "*facebook*", "*fb*", "*katana*"
        ]
        
        log_info(f"[真实提取] Facebook 真实2FA密钥提取器初始化 - 模拟器{emulator_id}", component="RealExtractor")

    async def run_real_extraction(self):
        """运行真实密钥提取流程"""
        try:
            log_info(f"[真实提取] 开始Facebook 真实2FA密钥提取", component="RealExtractor")
            
            print("🔓 Facebook 真实2FA密钥强制提取工具")
            print("=" * 50)
            print("⚠️  此工具专门提取已存在的真实2FA密钥")
            print("⚠️  请确保Facebook已启用2FA功能")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：检查Facebook 2FA状态
            await self._check_2fa_status()
            
            # 第二步：全面文件系统扫描
            secrets1 = await self._comprehensive_filesystem_scan()
            
            # 第三步：深度数据库分析
            secrets2 = await self._deep_database_analysis()
            
            # 第四步：内存实时捕获
            secrets3 = await self._realtime_memory_capture()
            
            # 第五步：网络流量监控
            secrets4 = await self._network_traffic_monitoring()
            
            # 第六步：应用逆向分析
            secrets5 = await self._app_reverse_analysis()
            
            # 第七步：系统级数据挖掘
            secrets6 = await self._system_level_mining()
            
            # 合并所有结果
            all_secrets = []
            all_secrets.extend(secrets1)
            all_secrets.extend(secrets2)
            all_secrets.extend(secrets3)
            all_secrets.extend(secrets4)
            all_secrets.extend(secrets5)
            all_secrets.extend(secrets6)
            
            # 验证和处理真实密钥
            real_secrets = await self._verify_real_secrets(all_secrets)
            
            # 保存和显示结果
            await self._save_real_secrets(real_secrets)
            await self._display_real_results(real_secrets)
            
            log_info(f"[真实提取] Facebook 真实2FA密钥提取完成", component="RealExtractor")
            
        except Exception as e:
            log_error(f"[真实提取] 真实密钥提取失败: {e}", component="RealExtractor")
        finally:
            await self._cleanup()

    async def _check_2fa_status(self):
        """检查Facebook 2FA状态"""
        try:
            print("🔍 第一步：检查Facebook 2FA状态")
            print("-" * 40)
            
            # 检查Facebook应用是否正在运行
            success, result = self.task.ld.execute_ld(self.emulator_id, "ps | grep facebook")
            if success and "facebook" in result:
                print("✅ Facebook应用正在运行")
                
                # 获取进程详细信息
                lines = result.strip().split('\n')
                for line in lines:
                    if 'com.facebook.katana' in line:
                        parts = line.split()
                        if len(parts) > 1:
                            pid = parts[1]
                            print(f"📱 Facebook PID: {pid}")
                            break
            else:
                print("⚠️  Facebook应用未运行，建议先启动应用")
                print("💡 请打开Facebook应用并尝试访问2FA设置")
                
                # 尝试启动Facebook应用
                print("🚀 尝试启动Facebook应用...")
                success, result = self.task.ld.execute_ld(self.emulator_id, "am start -n com.facebook.katana/.LoginActivity")
                if success:
                    print("✅ Facebook应用启动命令已发送")
                    await asyncio.sleep(3)  # 等待应用启动
                else:
                    print("❌ 无法启动Facebook应用")
            
            # 检查账户信息
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys account | grep facebook")
            if success and result.strip():
                print("✅ 检测到Facebook账户:")
                lines = result.strip().split('\n')
                for line in lines:
                    if 'name=' in line:
                        account_info = line.strip()
                        print(f"   👤 {account_info}")
                        
                        # 提取用户ID
                        if 'name=' in account_info:
                            user_id = account_info.split('name=')[1].split(',')[0] if ',' in account_info else account_info.split('name=')[1].split('}')[0]
                            print(f"   🆔 用户ID: {user_id}")
                            self.facebook_user_id = user_id
            else:
                print("❌ 未检测到Facebook账户登录")
                return
            
            # 检查是否有2FA相关的系统服务
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys activity services | grep -i auth")
            if success and result.strip():
                print("🔐 检测到认证相关服务:")
                lines = result.strip().split('\n')[:5]  # 只显示前5行
                for line in lines:
                    if line.strip():
                        print(f"   🔧 {line.strip()}")
            
            print("✅ 第一步完成")
            print()
            
        except Exception as e:
            log_error(f"[真实提取] 检查2FA状态失败: {e}", component="RealExtractor")

    async def _comprehensive_filesystem_scan(self) -> List[Dict[str, Any]]:
        """全面文件系统扫描"""
        try:
            print("🗂️  第二步：全面文件系统扫描")
            print("-" * 40)
            
            secrets = []
            
            for location in self.facebook_data_locations:
                print(f"🔍 扫描位置: {location}")
                
                # 检查目录是否存在
                success, result = self.task.ld.execute_ld(self.emulator_id, f"ls -la '{location}' 2>/dev/null")
                if not success or "No such file" in result:
                    print(f"   ❌ 目录不存在")
                    continue
                
                # 使用find命令深度搜索所有可能的文件
                for pattern in self.secret_file_patterns:
                    success, result = self.task.ld.execute_ld(self.emulator_id, f"find '{location}' -name '{pattern}' -type f 2>/dev/null")
                    if success and result.strip():
                        files = result.strip().split('\n')
                        print(f"   📄 找到 {len(files)} 个 {pattern} 文件")
                        
                        for file_path in files:
                            if file_path.strip():
                                file_secrets = await self._analyze_potential_secret_file(file_path.strip())
                                if file_secrets:
                                    secrets.extend(file_secrets)
                                    print(f"   🔑 从 {file_path} 提取到 {len(file_secrets)} 个密钥")
                
                # 特别搜索可能的Base32编码字符串
                success, result = self.task.ld.execute_ld(self.emulator_id, f"find '{location}' -type f -exec grep -l '[A-Z2-7]{{16,}}' {{}} \\; 2>/dev/null | head -10")
                if success and result.strip():
                    potential_files = result.strip().split('\n')
                    print(f"   🎯 找到 {len(potential_files)} 个可能包含Base32密钥的文件")
                    
                    for file_path in potential_files:
                        if file_path.strip():
                            file_secrets = await self._extract_base32_from_file(file_path.strip())
                            if file_secrets:
                                secrets.extend(file_secrets)
                                print(f"   🔑 从 {file_path} 提取Base32密钥: {len(file_secrets)} 个")
            
            print(f"✅ 第二步完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets
            
        except Exception as e:
            log_error(f"[真实提取] 全面文件系统扫描失败: {e}", component="RealExtractor")
            return []

    async def _deep_database_analysis(self) -> List[Dict[str, Any]]:
        """深度数据库分析"""
        try:
            print("🗄️  第三步：深度数据库分析")
            print("-" * 40)
            
            secrets = []
            
            # 查找所有SQLite数据库文件
            db_search_locations = [
                "/data/data/com.facebook.katana/",
                "/storage/emulated/0/Android/data/com.facebook.katana/"
            ]
            
            for location in db_search_locations:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"find '{location}' -name '*.db' -o -name '*.sqlite' -o -name '*.sqlite3' 2>/dev/null")
                if success and result.strip():
                    db_files = result.strip().split('\n')
                    print(f"📊 在 {location} 找到 {len(db_files)} 个数据库文件")
                    
                    for db_file in db_files:
                        if db_file.strip():
                            print(f"   🔍 分析数据库: {db_file}")
                            db_secrets = await self._analyze_sqlite_database(db_file.strip())
                            if db_secrets:
                                secrets.extend(db_secrets)
                                print(f"   ✅ 提取到 {len(db_secrets)} 个密钥")
                            else:
                                print(f"   ❌ 未找到密钥")
            
            print(f"✅ 第三步完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets
            
        except Exception as e:
            log_error(f"[真实提取] 深度数据库分析失败: {e}", component="RealExtractor")
            return []

    async def _realtime_memory_capture(self) -> List[Dict[str, Any]]:
        """内存实时捕获"""
        try:
            print("🧠 第四步：内存实时捕获")
            print("-" * 40)

            secrets = []

            # 获取Facebook应用的PID
            success, result = self.task.ld.execute_ld(self.emulator_id, "ps | grep com.facebook.katana")
            if not success or not result.strip():
                print("❌ Facebook应用未运行，跳过内存捕获")
                return []

            # 提取PID
            lines = result.strip().split('\n')
            pid = None
            for line in lines:
                parts = line.split()
                if len(parts) > 1 and 'com.facebook.katana' in line:
                    pid = parts[1]
                    break

            if not pid:
                print("❌ 无法获取Facebook应用PID")
                return []

            print(f"📱 Facebook PID: {pid}")

            # 尝试触发2FA相关操作
            print("🎯 尝试触发2FA相关操作...")

            # 发送一些可能触发2FA的Intent
            trigger_intents = [
                "am broadcast -a com.facebook.katana.ACTION_2FA_CHECK",
                "am broadcast -a com.facebook.katana.ACTION_SECURITY_CHECK",
                "am start -n com.facebook.katana/.security.TwoFactorActivity",
                "am start -n com.facebook.katana/.LoginActivity"
            ]

            for intent in trigger_intents:
                success, result = self.task.ld.execute_ld(self.emulator_id, intent)
                if success:
                    print(f"   ✅ 触发成功: {intent.split()[-1]}")
                    await asyncio.sleep(1)  # 等待处理
                else:
                    print(f"   ❌ 触发失败: {intent.split()[-1]}")

            # 监控内存中的字符串
            print("🔍 监控内存中的2FA相关数据...")

            # 尝试从进程内存中搜索Base32模式
            success, result = self.task.ld.execute_ld(self.emulator_id, f"cat /proc/{pid}/maps | grep -E '(heap|stack|anon)' | head -5")
            if success and result.strip():
                print(f"   📊 内存映射区域: {len(result.strip().split())} 个")

                # 尝试搜索环境变量中的密钥
                success2, env_result = self.task.ld.execute_ld(self.emulator_id, f"cat /proc/{pid}/environ 2>/dev/null | tr '\\0' '\\n' | grep -E '[A-Z2-7]{{16,}}'")
                if success2 and env_result.strip():
                    env_keys = env_result.strip().split('\n')
                    for key in env_keys:
                        if self._is_valid_base32_key(key.strip()):
                            secrets.append({
                                'secret_key': key.strip(),
                                'source': f'memory:environ:{pid}',
                                'method': 'realtime_memory_capture',
                                'confidence': 'high'
                            })
                            print(f"   🔑 内存中发现密钥: {key.strip()[:8]}...")

            # 尝试从logcat中捕获实时2FA数据
            print("📋 监控系统日志中的2FA数据...")
            success, result = self.task.ld.execute_ld(self.emulator_id, "logcat -d -s FacebookApp:* | grep -E '(2fa|totp|auth|secret)' | tail -10")
            if success and result.strip():
                log_lines = result.strip().split('\n')
                print(f"   📝 找到 {len(log_lines)} 条相关日志")

                for line in log_lines:
                    # 搜索日志中的Base32模式
                    base32_matches = re.findall(r'[A-Z2-7]{16,}', line)
                    for match in base32_matches:
                        if self._is_valid_base32_key(match):
                            secrets.append({
                                'secret_key': match,
                                'source': 'realtime_log',
                                'method': 'realtime_memory_capture',
                                'log_context': line[:100],
                                'confidence': 'medium'
                            })
                            print(f"   🔑 日志中发现密钥: {match[:8]}...")

            print(f"✅ 第四步完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets

        except Exception as e:
            log_error(f"[真实提取] 内存实时捕获失败: {e}", component="RealExtractor")
            return []

    async def _network_traffic_monitoring(self) -> List[Dict[str, Any]]:
        """网络流量监控"""
        try:
            print("🌐 第五步：网络流量监控")
            print("-" * 40)

            secrets = []

            # 检查网络连接
            success, result = self.task.ld.execute_ld(self.emulator_id, "netstat -an | grep facebook")
            if success and result.strip():
                connections = result.strip().split('\n')
                print(f"🔗 检测到 {len(connections)} 个Facebook网络连接")

                # 显示活跃连接
                for conn in connections[:3]:  # 只显示前3个
                    print(f"   📡 {conn.strip()}")
            else:
                print("❌ 未检测到Facebook网络连接")

            print(f"✅ 第五步完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets

        except Exception as e:
            log_error(f"[真实提取] 网络流量监控失败: {e}", component="RealExtractor")
            return []

    async def _app_reverse_analysis(self) -> List[Dict[str, Any]]:
        """应用逆向分析"""
        try:
            print("🔬 第六步：应用逆向分析")
            print("-" * 40)

            secrets = []

            # 分析Facebook应用的APK文件
            print("📱 分析Facebook应用结构...")

            # 获取应用安装路径
            success, result = self.task.ld.execute_ld(self.emulator_id, "pm path com.facebook.katana")
            if success and result.strip():
                apk_path = result.strip().replace('package:', '')
                print(f"📦 APK路径: {apk_path}")

                # 尝试从APK中提取字符串
                success2, strings_result = self.task.ld.execute_ld(self.emulator_id, f"strings '{apk_path}' | grep -E '(2fa|totp|auth|secret)' | head -10")
                if success2 and strings_result.strip():
                    app_strings = strings_result.strip().split('\n')
                    print(f"🔍 在APK中找到 {len(app_strings)} 个相关字符串")

                    for string in app_strings:
                        print(f"   📝 {string}")

                        # 搜索字符串中的Base32模式
                        base32_matches = re.findall(r'[A-Z2-7]{16,}', string)
                        for match in base32_matches:
                            if self._is_valid_base32_key(match):
                                secrets.append({
                                    'secret_key': match,
                                    'source': f'apk_strings:{apk_path}',
                                    'method': 'app_reverse_analysis',
                                    'confidence': 'low'
                                })
                                print(f"   🔑 APK中发现密钥: {match[:8]}...")

            print(f"✅ 第六步完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets

        except Exception as e:
            log_error(f"[真实提取] 应用逆向分析失败: {e}", component="RealExtractor")
            return []

    async def _system_level_mining(self) -> List[Dict[str, Any]]:
        """系统级数据挖掘"""
        try:
            print("⚙️  第七步：系统级数据挖掘")
            print("-" * 40)

            secrets = []

            # 搜索系统级认证存储
            system_locations = [
                "/data/system/accounts.db",
                "/data/system_ce/0/accounts_ce.db",
                "/data/system/users/0/accounts.db"
            ]

            for location in system_locations:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"ls -la '{location}' 2>/dev/null")
                if success and "No such file" not in result:
                    print(f"📊 分析系统数据库: {location}")

                    # 尝试从系统数据库中提取Facebook相关数据
                    db_secrets = await self._analyze_system_database(location)
                    if db_secrets:
                        secrets.extend(db_secrets)
                        print(f"   ✅ 提取到 {len(db_secrets)} 个密钥")
                else:
                    print(f"   ❌ 无法访问: {location}")

            print(f"✅ 第七步完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets

        except Exception as e:
            log_error(f"[真实提取] 系统级数据挖掘失败: {e}", component="RealExtractor")
            return []

    def _is_valid_base32_key(self, key: str) -> bool:
        """验证是否为有效的Base32密钥"""
        try:
            if not key or len(key) < 16:
                return False

            # 清理密钥
            cleaned = re.sub(r'[^A-Z2-7]', '', key.upper())
            if len(cleaned) < 16:
                return False

            # 验证Base32字符集
            if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in cleaned):
                return False

            # 尝试解码
            try:
                base64.b32decode(cleaned)
                return True
            except Exception:
                return False

        except Exception:
            return False

    async def _analyze_potential_secret_file(self, file_path: str) -> List[Dict[str, Any]]:
        """分析可能包含密钥的文件"""
        try:
            secrets = []

            # 使用strings命令提取可读字符串
            success, result = self.task.ld.execute_ld(self.emulator_id, f"strings '{file_path}' | grep -E '[A-Z2-7]{{16,}}' | head -10")
            if success and result.strip():
                potential_keys = result.strip().split('\n')

                for key in potential_keys:
                    if self._is_valid_base32_key(key.strip()):
                        secrets.append({
                            'secret_key': key.strip(),
                            'source': f'file:{file_path}',
                            'method': 'file_analysis',
                            'confidence': 'medium'
                        })

            return secrets

        except Exception as e:
            log_error(f"[真实提取] 分析文件失败: {e}", component="RealExtractor")
            return []

    async def _extract_base32_from_file(self, file_path: str) -> List[Dict[str, Any]]:
        """从文件中提取Base32密钥"""
        try:
            secrets = []

            # 读取文件内容并搜索Base32模式
            success, result = self.task.ld.execute_ld(self.emulator_id, f"cat '{file_path}' | grep -oE '[A-Z2-7]{{16,}}' | head -5")
            if success and result.strip():
                potential_keys = result.strip().split('\n')

                for key in potential_keys:
                    if self._is_valid_base32_key(key.strip()):
                        secrets.append({
                            'secret_key': key.strip(),
                            'source': f'base32_extract:{file_path}',
                            'method': 'base32_extraction',
                            'confidence': 'high'
                        })

            return secrets

        except Exception as e:
            log_error(f"[真实提取] Base32提取失败: {e}", component="RealExtractor")
            return []

    async def _analyze_sqlite_database(self, db_path: str) -> List[Dict[str, Any]]:
        """分析SQLite数据库"""
        try:
            secrets = []

            # 将数据库复制到共享目录进行分析
            temp_db_name = f"temp_analysis_{Path(db_path).name}"
            shared_db_path = Path(self.task.ld.share_path) / temp_db_name

            # 复制数据库文件
            success, _ = self.task.ld.execute_ld(self.emulator_id, f"cp '{db_path}' '{self.task.ld.share_path}/{temp_db_name}'")
            if success and shared_db_path.exists():
                try:
                    conn = sqlite3.connect(str(shared_db_path))
                    cursor = conn.cursor()

                    # 获取所有表名
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()

                    for table in tables:
                        table_name = table[0]
                        try:
                            # 获取表结构
                            cursor.execute(f"PRAGMA table_info({table_name})")
                            columns = cursor.fetchall()

                            # 搜索可能包含密钥的列
                            for col in columns:
                                col_name = col[1]
                                col_type = col[2]

                                # 如果列名或类型暗示可能包含密钥
                                if any(keyword in col_name.lower() for keyword in ['auth', '2fa', 'secret', 'token', 'key', 'otp', 'totp']):
                                    cursor.execute(f"SELECT {col_name} FROM {table_name} WHERE {col_name} IS NOT NULL AND length({col_name}) >= 16 LIMIT 10")
                                    rows = cursor.fetchall()

                                    for row in rows:
                                        value = str(row[0])
                                        if self._is_valid_base32_key(value):
                                            secrets.append({
                                                'secret_key': value,
                                                'source': f'sqlite:{db_path}:{table_name}:{col_name}',
                                                'method': 'sqlite_analysis',
                                                'confidence': 'high'
                                            })

                        except Exception:
                            continue

                    conn.close()

                except Exception as e:
                    log_error(f"[真实提取] SQLite连接失败: {e}", component="RealExtractor")

                # 清理临时文件
                shared_db_path.unlink(missing_ok=True)

            return secrets

        except Exception as e:
            log_error(f"[真实提取] SQLite分析失败: {e}", component="RealExtractor")
            return []

    async def _analyze_system_database(self, db_path: str) -> List[Dict[str, Any]]:
        """分析系统数据库"""
        try:
            secrets = []

            # 尝试从系统数据库中搜索Facebook相关的认证数据
            success, result = self.task.ld.execute_ld(self.emulator_id, f"sqlite3 '{db_path}' \"SELECT * FROM accounts WHERE type LIKE '%facebook%'\" 2>/dev/null")
            if success and result.strip():
                print(f"   📊 找到Facebook账户记录")

                # 进一步搜索可能的密钥字段
                success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"sqlite3 '{db_path}' \"SELECT name FROM sqlite_master WHERE type='table'\" 2>/dev/null")
                if success2 and result2.strip():
                    tables = result2.strip().split('\n')

                    for table in tables:
                        if table.strip():
                            # 搜索表中的Base32模式
                            success3, result3 = self.task.ld.execute_ld(self.emulator_id, f"sqlite3 '{db_path}' \"SELECT * FROM {table}\" 2>/dev/null | grep -oE '[A-Z2-7]{{16,}}' | head -3")
                            if success3 and result3.strip():
                                potential_keys = result3.strip().split('\n')

                                for key in potential_keys:
                                    if self._is_valid_base32_key(key.strip()):
                                        secrets.append({
                                            'secret_key': key.strip(),
                                            'source': f'system_db:{db_path}:{table}',
                                            'method': 'system_database_analysis',
                                            'confidence': 'high'
                                        })

            return secrets

        except Exception as e:
            log_error(f"[真实提取] 系统数据库分析失败: {e}", component="RealExtractor")
            return []

    async def _verify_real_secrets(self, all_secrets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证和处理真实密钥"""
        try:
            print("🔍 验证提取的密钥...")
            print("-" * 40)

            # 去重
            unique_secrets = []
            seen_keys = set()

            for secret in all_secrets:
                key = secret.get('secret_key', '')
                if key and key not in seen_keys:
                    seen_keys.add(key)

                    # 验证密钥并生成测试TOTP
                    if self._is_valid_base32_key(key):
                        try:
                            import hmac
                            import hashlib
                            import struct
                            import time

                            decoded_key = base64.b32decode(key)
                            timestamp = int(time.time()) // 30
                            msg = struct.pack('>Q', timestamp)
                            hmac_digest = hmac.new(decoded_key, msg, hashlib.sha1).digest()
                            offset = hmac_digest[-1] & 0x0f
                            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
                            code = (code & 0x7fffffff) % 1000000

                            secret['test_totp'] = f"{code:06d}"
                            secret['verified'] = True
                            secret['key_length'] = len(key)

                            print(f"✅ 验证密钥: {key[:8]}... (来源: {secret.get('source', 'unknown')})")
                            print(f"   🔢 测试验证码: {code:06d}")
                            print(f"   📊 置信度: {secret.get('confidence', 'unknown')}")

                        except Exception as e:
                            secret['verified'] = False
                            secret['error'] = str(e)
                            print(f"❌ 密钥验证失败: {key[:8]}... - {e}")

                        unique_secrets.append(secret)

            # 按置信度排序
            confidence_order = {'high': 3, 'medium': 2, 'low': 1}
            unique_secrets.sort(key=lambda x: confidence_order.get(x.get('confidence', 'low'), 0), reverse=True)

            print(f"✅ 验证完成: {len(unique_secrets)} 个有效密钥")
            print()
            return unique_secrets

        except Exception as e:
            log_error(f"[真实提取] 密钥验证失败: {e}", component="RealExtractor")
            return []

    async def _save_real_secrets(self, secrets: List[Dict[str, Any]]):
        """保存真实密钥"""
        try:
            if not secrets:
                return

            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存到JSON文件
            output_dir = Path("./fb_2fa_results/real_extraction/")
            output_dir.mkdir(parents=True, exist_ok=True)

            json_file = output_dir / f"real_extracted_secrets_{self.emulator_id}_{timestamp}.json"

            result_data = {
                'emulator_id': self.emulator_id,
                'extraction_time': datetime.datetime.now().isoformat(),
                'total_secrets': len(secrets),
                'facebook_user_id': getattr(self, 'facebook_user_id', 'unknown'),
                'secrets': secrets
            }

            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)

            # 保存到文本文件
            txt_file = output_dir / f"real_extracted_secrets_{self.emulator_id}_{timestamp}.txt"

            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(f"Facebook 真实2FA密钥提取结果\n")
                f.write(f"模拟器ID: {self.emulator_id}\n")
                f.write(f"Facebook用户ID: {getattr(self, 'facebook_user_id', 'unknown')}\n")
                f.write(f"提取时间: {result_data['extraction_time']}\n")
                f.write(f"总密钥数: {len(secrets)}\n")
                f.write("=" * 50 + "\n\n")

                for i, secret in enumerate(secrets, 1):
                    f.write(f"{i}. 密钥: {secret['secret_key']}\n")
                    f.write(f"   来源: {secret['source']}\n")
                    f.write(f"   提取方法: {secret['method']}\n")
                    f.write(f"   置信度: {secret.get('confidence', 'unknown')}\n")
                    if secret.get('verified'):
                        f.write(f"   测试验证码: {secret.get('test_totp', 'N/A')}\n")
                    f.write(f"   验证状态: {'✅ 有效' if secret.get('verified') else '❌ 无效'}\n")
                    f.write("-" * 30 + "\n")

            print(f"💾 结果已保存:")
            print(f"   📄 {json_file}")
            print(f"   📄 {txt_file}")

        except Exception as e:
            log_error(f"[真实提取] 保存结果失败: {e}", component="RealExtractor")

    async def _display_real_results(self, secrets: List[Dict[str, Any]]):
        """显示真实提取结果"""
        try:
            print("\n" + "=" * 50)
            print("🎯 Facebook 真实2FA密钥提取结果")
            print("=" * 50)

            if not secrets:
                print("❌ 未找到任何真实的2FA密钥")
                print("\n💡 可能的原因:")
                print("1. Facebook账户尚未启用2FA功能")
                print("2. 2FA数据存储在我们未扫描到的位置")
                print("3. 2FA数据被加密或混淆")
                print("4. 需要先在Facebook中进行2FA相关操作")
                print("\n🔧 建议:")
                print("1. 确保Facebook应用已登录")
                print("2. 尝试在Facebook中访问安全设置")
                print("3. 安装认证器应用并尝试设置2FA")
                print("4. 重新运行提取工具")
                return

            print(f"✅ 找到 {len(secrets)} 个真实的2FA密钥:")
            print(f"👤 Facebook用户ID: {getattr(self, 'facebook_user_id', 'unknown')}")
            print()

            for i, secret in enumerate(secrets, 1):
                print(f"🔑 密钥 {i} (置信度: {secret.get('confidence', 'unknown').upper()}):")
                print(f"   密钥: {secret['secret_key']}")
                print(f"   来源: {secret['source']}")
                print(f"   提取方法: {secret['method']}")

                if secret.get('verified'):
                    print(f"   ✅ 验证状态: 有效")
                    print(f"   🔢 当前验证码: {secret.get('test_totp', 'N/A')}")
                    print(f"   📏 密钥长度: {secret.get('key_length', 0)} 字符")
                else:
                    print(f"   ❌ 验证状态: 无效")
                    if secret.get('error'):
                        print(f"   ⚠️  错误: {secret['error']}")

                print("-" * 30)

            print("\n💡 使用建议:")
            print("1. 优先使用置信度为'HIGH'的密钥")
            print("2. 在认证器应用中添加这些密钥进行验证")
            print("3. 确认生成的验证码与Facebook要求的一致")
            print("4. 如果验证成功，说明成功提取了真实的2FA密钥")

        except Exception as e:
            log_error(f"[真实提取] 显示结果失败: {e}", component="RealExtractor")

    async def _cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir, ignore_errors=True)
        except Exception:
            pass

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2

        print("🔓 Facebook 真实2FA密钥强制提取工具")
        print("⚠️  此工具专门提取已存在的真实2FA密钥")
        print("⚠️  请确保Facebook已启用2FA功能")
        print("⚠️  仅用于合法的账户恢复目的")
        print()

        confirm = input("确认继续? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return

        # 创建真实密钥提取器
        extractor = FacebookRealSecretExtractor(emulator_id)

        # 运行真实密钥提取
        await extractor.run_real_extraction()

        print("\n" + "=" * 50)
        print("✅ Facebook 真实2FA密钥提取完成")

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
