#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔢 实时TOTP验证码生成器
========================================
功能描述: 实时生成当前有效的TOTP验证码

核心功能:
1. 生成当前时间窗口的验证码
2. 显示验证码剩余有效时间
3. 提供多个时间窗口的验证码
4. 实时更新显示

使用方法:
python fb/generate_current_totp.py

注意事项:
- 验证码每30秒更新一次
- 显示当前和下一个验证码
========================================
"""

import base64
import hmac
import hashlib
import struct
import time
from datetime import datetime

def generate_totp_code(secret: str, timestamp: int = None) -> str:
    """生成TOTP验证码"""
    try:
        # 解码Base32密钥
        key = base64.b32decode(secret)
        
        # 使用指定时间戳或当前时间
        if timestamp is None:
            timestamp = int(time.time()) // 30
        
        # 生成TOTP
        msg = struct.pack('>Q', timestamp)
        hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
        offset = hmac_digest[-1] & 0x0f
        code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
        code = (code & 0x7fffffff) % 1000000
        
        return f"{code:06d}"
        
    except Exception as e:
        return f"ERROR: {e}"

def get_time_remaining():
    """获取当前验证码剩余有效时间"""
    current_time = int(time.time())
    time_in_window = current_time % 30
    remaining = 30 - time_in_window
    return remaining

def main():
    """主函数"""
    # 您的TOTP密钥
    secret_key = "XZJKVEGNFN6S2C2Z7LTDRW5TUZYH4WPU"
    
    print("🔢 实时TOTP验证码生成器")
    print("=" * 50)
    print(f"🔑 密钥: {secret_key}")
    print()
    
    # 获取当前时间信息
    current_time = int(time.time())
    current_timestamp = current_time // 30
    
    print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🕐 时间戳: {current_time}")
    print()
    
    # 生成多个时间窗口的验证码
    print("🔢 验证码 (按时间顺序):")
    print("-" * 40)
    
    time_windows = [
        ("前一个", current_timestamp - 1),
        ("当前", current_timestamp),
        ("下一个", current_timestamp + 1),
        ("下下个", current_timestamp + 2)
    ]
    
    for desc, ts in time_windows:
        code = generate_totp_code(secret_key, ts)
        window_start = ts * 30
        window_end = window_start + 30
        start_time = datetime.fromtimestamp(window_start).strftime('%H:%M:%S')
        end_time = datetime.fromtimestamp(window_end).strftime('%H:%M:%S')
        
        if desc == "当前":
            remaining = get_time_remaining()
            print(f"✅ {desc}验证码: {code} (剩余 {remaining} 秒) [{start_time}-{end_time}]")
        else:
            print(f"   {desc}验证码: {code} [{start_time}-{end_time}]")
    
    print()
    print("🎯 推荐使用:")
    
    # 获取当前最佳验证码
    current_code = generate_totp_code(secret_key, current_timestamp)
    remaining = get_time_remaining()
    
    if remaining > 10:
        print(f"   使用当前验证码: {current_code} (还有 {remaining} 秒有效)")
    else:
        next_code = generate_totp_code(secret_key, current_timestamp + 1)
        print(f"   当前验证码即将过期 ({remaining} 秒)")
        print(f"   建议使用下一个: {next_code} (即将生效)")
    
    print()
    print("⚠️  验证码每30秒更新一次")
    print("⚠️  请在有效时间内使用")
    print("⚠️  如果验证失败，可能需要等待下一个验证码")
    
    # 实时监控模式
    print("\n" + "=" * 50)
    print("🔄 实时监控模式 (按Ctrl+C退出)")
    print("=" * 50)
    
    try:
        last_code = ""
        while True:
            current_code = generate_totp_code(secret_key)
            remaining = get_time_remaining()
            
            if current_code != last_code:
                print(f"\n🔢 新验证码: {current_code}")
                last_code = current_code
            
            # 显示倒计时
            print(f"\r⏱️  剩余时间: {remaining:2d} 秒 | 当前验证码: {current_code}", end="", flush=True)
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n\n✅ 实时监控已停止")

if __name__ == "__main__":
    main()
