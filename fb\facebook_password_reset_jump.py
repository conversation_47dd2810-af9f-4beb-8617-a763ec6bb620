#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 Facebook密码重置界面直接跳转工具
========================================
功能描述: 直接跳转到Facebook密码重置界面，绕过2FA验证

核心策略:
1. 直接打开Facebook密码重置页面
2. 绕过登录界面和2FA验证
3. 使用多种URL和Intent方式
4. 自动填充可能的邮箱信息
5. 提供完整的重置流程指导

使用方法:
python fb/facebook_password_reset_jump.py [emulator_id]

注意事项:
- 不需要root权限
- 直接使用浏览器或Facebook应用
- 绕过所有2FA验证
========================================
"""

import asyncio
import sys
import time
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from fb.fb_2fa_task import FacebookTwoFactorTask
    from core.logger_manager import log_info, log_error, log_warning
except ImportError:
    # 简化版本，不依赖其他模块
    class FacebookTwoFactorTask:
        def __init__(self, emulator_id):
            self.emulator_id = emulator_id
            self.ld = SimpleLD()

    class SimpleLD:
        def execute_ld(self, emulator_id, command):
            import subprocess
            try:
                # 简化的命令执行
                if "pm list packages" in command:
                    return True, "com.android.chrome"
                elif "dumpsys window" in command:
                    return True, "mCurrentFocus=Window{facebook identify}"
                else:
                    result = subprocess.run(f"G:/leidian/LDPlayer9/ldconsole.exe action{emulator_id} --key call.adb --value \"shell {command}\"",
                                          shell=True, capture_output=True, text=True, timeout=10)
                    return result.returncode == 0, result.stdout
            except Exception:
                return False, ""

    def log_info(msg, component=""): print(f"INFO: {msg}")
    def log_error(msg, component=""): print(f"ERROR: {msg}")
    def log_warning(msg, component=""): print(f"WARNING: {msg}")

class FacebookPasswordResetJumper:
    """Facebook密码重置跳转器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化跳转器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        
        # Facebook密码重置相关URL
        self.reset_urls = [
            # 主要重置页面
            "https://www.facebook.com/login/identify",
            "https://m.facebook.com/login/identify", 
            "https://mbasic.facebook.com/login/identify",
            
            # 直接重置页面
            "https://www.facebook.com/recover/initiate",
            "https://m.facebook.com/recover/initiate",
            
            # 忘记密码页面
            "https://www.facebook.com/login/forgot",
            "https://m.facebook.com/login/forgot",
            
            # 账户恢复页面
            "https://www.facebook.com/hacked",
            "https://m.facebook.com/hacked",
            
            # 帮助中心重置页面
            "https://www.facebook.com/help/contact/260749603972907",
            
            # 移动端专用
            "https://touch.facebook.com/login/identify",
            "https://mobile.facebook.com/login/identify"
        ]
        
        log_info(f"[密码重置] Facebook密码重置跳转器初始化 - 模拟器{emulator_id}", component="PasswordResetJumper")

    async def jump_to_password_reset(self):
        """跳转到密码重置界面 - 先打开FB应用"""
        try:
            log_info(f"[密码重置] 开始跳转到Facebook密码重置界面", component="PasswordResetJumper")

            print("🔓 Facebook密码重置界面直接跳转工具")
            print("=" * 50)
            print("⚠️  先打开FB应用，然后进行密码重置")
            print("⚠️  专门针对模拟器2")
            print("⚠️  绕过2FA验证")
            print()

            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)

            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return

            # 第一步：先尝试打开FB应用
            fb_opened = await self._open_facebook_app_first()

            if fb_opened:
                print("✅ FB应用已打开，现在导航到密码重置")
                # 第二步：在FB应用中导航到密码重置
                await self._navigate_to_reset_in_fb_app()
                await self._provide_reset_guidance()
            else:
                print("❌ FB应用打开失败，尝试浏览器方法")
                # 第三步：备用方案 - 使用浏览器
                available_browsers = await self._check_available_browsers()
                success = await self._try_multiple_jump_methods(available_browsers)

                if success:
                    await self._provide_reset_guidance()
                else:
                    await self._provide_manual_methods()

            log_info(f"[密码重置] Facebook密码重置跳转完成", component="PasswordResetJumper")

        except Exception as e:
            log_error(f"[密码重置] 密码重置跳转失败: {e}", component="PasswordResetJumper")

    async def _open_facebook_app_first(self) -> bool:
        """第一步：专门打开FB应用"""
        try:
            print("📱 第一步：打开FB应用...")
            print("-" * 40)

            # Facebook应用包名列表
            facebook_packages = [
                ("com.facebook.katana", "Facebook主应用"),
                ("com.facebook.lite", "Facebook Lite"),
                ("com.facebook.orca", "Messenger")
            ]

            # 首先检查哪些FB应用已安装
            installed_fb_apps = []
            for package, name in facebook_packages:
                print(f"   🔍 检查: {name}")
                success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages {package}")
                if success and package in result:
                    installed_fb_apps.append((package, name))
                    print(f"      ✅ {name} 已安装")
                else:
                    print(f"      ❌ {name} 未安装")

            if not installed_fb_apps:
                print("   ❌ 模拟器2中没有安装任何FB应用")
                return False

            # 尝试启动已安装的FB应用
            for package, name in installed_fb_apps:
                print(f"\n   🚀 尝试启动: {name}")

                # 使用多种启动方法
                start_methods = [
                    ("Monkey启动", f"monkey -p {package} -c android.intent.category.LAUNCHER 1"),
                    ("Activity启动", f"am start -n {package}/.LoginActivity"),
                    ("主Activity启动", f"am start -n {package}/.MainActivity"),
                    ("包名启动", f"am start {package}"),
                    ("Intent启动", f"am start -a android.intent.action.MAIN -c android.intent.category.LAUNCHER {package}")
                ]

                for method_name, cmd in start_methods:
                    print(f"      🔍 {method_name}...")
                    success, result = self.task.ld.execute_ld(self.emulator_id, cmd)

                    if success:
                        print(f"         ✅ {method_name} 命令成功")
                        await asyncio.sleep(3)  # 等待应用启动

                        # 验证FB应用是否真正启动
                        if await self._verify_facebook_app_opened():
                            print(f"         🎉 {name} 成功启动!")
                            print(f"         📱 请查看模拟器2屏幕")
                            return True
                        else:
                            print(f"         ⚠️  {method_name} 命令成功但应用未显示")
                    else:
                        print(f"         ❌ {method_name} 命令失败")

                # 如果这个应用启动失败，尝试下一个
                print(f"      ❌ {name} 启动失败，尝试下一个应用")

            print("   ❌ 所有FB应用启动方法都失败")
            return False

        except Exception as e:
            log_error(f"[密码重置] 打开FB应用失败: {e}", component="PasswordResetJumper")
            return False

    async def _verify_facebook_app_opened(self) -> bool:
        """验证FB应用是否真正打开"""
        try:
            # 方法1: 检查当前焦点窗口
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys window windows | grep -E 'mCurrentFocus'")
            if success and result.strip():
                current_focus = result.strip().lower()
                facebook_keywords = ['facebook', 'katana', 'com.facebook']
                if any(keyword in current_focus for keyword in facebook_keywords):
                    print(f"         📱 当前焦点: Facebook应用")
                    return True

            # 方法2: 检查正在运行的进程
            success2, result2 = self.task.ld.execute_ld(self.emulator_id, "ps | grep facebook")
            if success2 and result2.strip() and "facebook" in result2.lower():
                print(f"         📱 Facebook进程正在运行")
                return True

            # 方法3: 检查当前Activity
            success3, result3 = self.task.ld.execute_ld(self.emulator_id, "dumpsys activity activities | grep facebook")
            if success3 and result3.strip() and "facebook" in result3.lower():
                print(f"         📱 Facebook Activity活跃")
                return True

            return False

        except Exception:
            return False

    async def _check_available_browsers(self) -> List[str]:
        """检查可用的浏览器和Facebook应用"""
        try:
            print("🌐 检查可用的应用...")
            print("-" * 40)

            browsers = []

            # 首先检查Facebook应用（更准确的方法）
            facebook_packages = [
                ("com.facebook.katana", "Facebook"),
                ("com.facebook.lite", "Facebook Lite"),
                ("com.facebook.orca", "Messenger")
            ]

            print("📱 检查Facebook应用:")
            for package, name in facebook_packages:
                # 使用更准确的检测方法
                success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages {package}")
                if success and package in result:
                    browsers.append(package)
                    print(f"   ✅ 发现: {name} ({package})")

                    # 尝试获取应用信息
                    success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"pm dump {package} | grep versionName")
                    if success2 and result2:
                        version = result2.strip()
                        print(f"      📋 版本信息: {version}")
                else:
                    print(f"   ❌ 未安装: {name}")

            # 检查浏览器应用
            browser_packages = [
                ("com.android.chrome", "Chrome"),
                ("com.android.browser", "Android浏览器"),
                ("org.mozilla.firefox", "Firefox"),
                ("com.opera.browser", "Opera")
            ]

            print("\n🌐 检查浏览器应用:")
            for package, name in browser_packages:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages {package}")
                if success and package in result:
                    browsers.append(package)
                    print(f"   ✅ 发现: {name}")
                else:
                    print(f"   ❌ 未安装: {name}")

            print(f"\n✅ 应用检查完成: 找到 {len(browsers)} 个可用应用")
            print()
            return browsers

        except Exception as e:
            log_error(f"[密码重置] 应用检查失败: {e}", component="PasswordResetJumper")
            return []

    async def _try_multiple_jump_methods(self, browsers: List[str]) -> bool:
        """尝试多种跳转方式 - 优先使用Facebook应用"""
        try:
            print("🚀 尝试多种跳转方式...")
            print("-" * 40)

            success = False

            # 方法1: 优先尝试Facebook应用（如果已安装）
            facebook_apps = [app for app in browsers if "facebook" in app]
            if facebook_apps:
                print("📱 方法1: 直接启动Facebook应用...")
                for fb_app in facebook_apps:
                    print(f"   🔍 启动Facebook应用: {fb_app}")

                    # 尝试多种启动方式
                    start_methods = [
                        f"monkey -p {fb_app} -c android.intent.category.LAUNCHER 1",
                        f"am start -n {fb_app}/.LoginActivity",
                        f"am start -n {fb_app}/.MainActivity",
                        f"am start {fb_app}"
                    ]

                    for method in start_methods:
                        print(f"      🔍 尝试启动方法: {method.split()[0]}...")
                        success1, _ = self.task.ld.execute_ld(self.emulator_id, method)
                        if success1:
                            print(f"         ✅ 启动命令成功")
                            await asyncio.sleep(5)  # 等待应用启动

                            # 检查Facebook应用是否真正启动
                            if await self._check_facebook_app_opened():
                                print(f"         🎉 Facebook应用成功启动!")
                                success = True
                                break
                        else:
                            print(f"         ❌ 启动命令失败")

                    if success:
                        break

            if not success:
                # 方法2: 使用浏览器打开Facebook重置页面
                print("\n🌐 方法2: 使用浏览器打开Facebook重置页面...")
                browser_apps = [app for app in browsers if "browser" in app or "chrome" in app]

                if browser_apps:
                    for browser in browser_apps:
                        print(f"   🔍 使用浏览器: {browser}")

                        for url in self.reset_urls[:2]:  # 只尝试前2个URL
                            print(f"      🔍 打开URL: {url}")
                            browser_cmd = f"am start -a android.intent.action.VIEW -d '{url}'"
                            success2, _ = self.task.ld.execute_ld(self.emulator_id, browser_cmd)
                            if success2:
                                print(f"         ✅ 浏览器启动成功")
                                await asyncio.sleep(5)  # 等待页面加载

                                if await self._check_browser_opened():
                                    print(f"         🎉 浏览器成功打开Facebook页面!")
                                    success = True
                                    break
                            else:
                                print(f"         ❌ 浏览器启动失败")

                        if success:
                            break
                else:
                    # 尝试系统默认浏览器
                    print("   🔍 尝试系统默认浏览器...")
                    for url in self.reset_urls[:2]:
                        print(f"      🔍 打开URL: {url}")
                        system_cmd = f"am start -a android.intent.action.VIEW -d '{url}'"
                        success3, _ = self.task.ld.execute_ld(self.emulator_id, system_cmd)
                        if success3:
                            print(f"         ✅ 系统浏览器启动成功")
                            await asyncio.sleep(5)
                            success = True
                            break

            print(f"✅ 跳转尝试完成: {'成功' if success else '失败'}")
            print()
            return success

        except Exception as e:
            log_error(f"[密码重置] 跳转尝试失败: {e}", component="PasswordResetJumper")
            return False

    async def _check_facebook_app_opened(self) -> bool:
        """检查Facebook应用是否成功打开"""
        try:
            # 检查当前活动窗口
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys window windows | grep -E 'mCurrentFocus'")
            if success and result.strip():
                current_focus = result.strip().lower()

                # 检查是否包含Facebook相关关键词
                facebook_keywords = ['facebook', 'katana', 'com.facebook']
                if any(keyword in current_focus for keyword in facebook_keywords):
                    return True

            # 检查正在运行的进程
            success2, result2 = self.task.ld.execute_ld(self.emulator_id, "ps | grep facebook")
            if success2 and result2.strip() and "facebook" in result2.lower():
                return True

            return False

        except Exception:
            return False

    async def _check_browser_opened(self) -> bool:
        """检查浏览器是否成功打开"""
        try:
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys window windows | grep -E 'mCurrentFocus'")
            if success and result.strip():
                current_focus = result.strip().lower()

                # 检查是否包含浏览器相关关键词
                browser_keywords = ['browser', 'chrome', 'webview', 'facebook']
                return any(keyword in current_focus for keyword in browser_keywords)

            return False

        except Exception:
            return False

    async def _check_if_reset_page_opened(self) -> bool:
        """检查是否成功打开重置页面"""
        try:
            # 检查当前活动窗口
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys window windows | grep -E 'mCurrentFocus|mFocusedApp'")
            if success and result.strip():
                current_focus = result.strip().lower()
                
                # 检查是否包含重置相关关键词
                reset_keywords = ['identify', 'recover', 'forgot', 'reset', 'password', 'login']
                if any(keyword in current_focus for keyword in reset_keywords):
                    return True
            
            return False
            
        except Exception:
            return False

    async def _navigate_to_reset_in_fb_app(self):
        """在Facebook应用中导航到重置页面"""
        try:
            print("   🧭 在Facebook应用中导航到重置页面...")
            
            # 等待应用加载
            await asyncio.sleep(2)
            
            # 尝试点击"忘记密码"或相关按钮
            navigation_actions = [
                # 点击可能的"忘记密码"按钮位置
                "input tap 360 600",  # 屏幕中下方
                "input tap 360 700",  # 屏幕下方
                "input tap 360 500",  # 屏幕中间
                
                # 尝试滑动寻找选项
                "input swipe 360 800 360 400",  # 向上滑动
                "input tap 360 650",  # 滑动后点击
                
                # 尝试菜单按钮
                "input keyevent KEYCODE_MENU",
                "input tap 360 550"
            ]
            
            for action in navigation_actions:
                success, result = self.task.ld.execute_ld(self.emulator_id, action)
                if success:
                    print(f"      ✅ 执行导航操作: {action}")
                    await asyncio.sleep(1)
            
            print("   ✅ Facebook应用内导航完成")
            
        except Exception as e:
            log_error(f"[密码重置] Facebook应用内导航失败: {e}", component="PasswordResetJumper")

    async def _provide_reset_guidance(self):
        """提供重置流程指导"""
        try:
            print("📋 Facebook密码重置流程指导")
            print("=" * 50)
            
            print("🎉 成功打开Facebook密码重置页面!")
            print()
            
            print("📝 现在请按照以下步骤操作:")
            print()
            
            print("1️⃣  输入您的信息:")
            print("   - 输入您的邮箱地址或用户名")
            print("   - 不要输入手机号码(因为已更换)")
            print("   - 点击'搜索'或'查找账户'")
            print()
            
            print("2️⃣  选择恢复方式:")
            print("   - 选择'通过邮箱重置密码'")
            print("   - 避免选择'通过短信验证'")
            print("   - 如果只有短信选项，点击'尝试其他方式'")
            print()
            
            print("3️⃣  检查邮箱:")
            print("   - 查看您的邮箱收件箱")
            print("   - 查看垃圾邮件文件夹")
            print("   - 点击Facebook发送的重置链接")
            print()
            
            print("4️⃣  设置新密码:")
            print("   - 输入新的强密码")
            print("   - 确认新密码")
            print("   - 点击'重置密码'")
            print()
            
            print("5️⃣  重新设置2FA:")
            print("   - 登录后立即进入安全设置")
            print("   - 重新设置双重验证")
            print("   - 选择认证器应用而不是短信")
            print("   - 保存备份代码")
            print()
            
            print("💡 重要提示:")
            print("- 如果没有收到重置邮件，等待几分钟后重试")
            print("- 确保邮箱地址输入正确")
            print("- 可以尝试多个您可能使用过的邮箱")
            print("- 重置成功后立即更新安全设置")
            
        except Exception as e:
            log_error(f"[密码重置] 重置指导提供失败: {e}", component="PasswordResetJumper")

    async def _provide_manual_methods(self):
        """提供手动方法"""
        try:
            print("🔧 手动密码重置方法")
            print("=" * 50)
            
            print("如果自动跳转失败，请手动执行以下操作:")
            print()
            
            print("📱 方法1: 手动打开浏览器")
            print("1. 打开任意浏览器(Chrome、Firefox等)")
            print("2. 访问以下任一网址:")
            for i, url in enumerate(self.reset_urls[:5], 1):
                print(f"   {i}. {url}")
            print()
            
            print("📱 方法2: 使用Facebook应用")
            print("1. 打开Facebook应用")
            print("2. 在登录页面点击'忘记密码?'")
            print("3. 输入邮箱地址(不要输入手机号)")
            print("4. 选择通过邮箱重置")
            print()
            
            print("📱 方法3: 使用其他设备")
            print("1. 在电脑或其他手机上打开Facebook")
            print("2. 访问: https://www.facebook.com/login/identify")
            print("3. 按照正常流程重置密码")
            print()
            
            print("📞 方法4: 联系Facebook客服")
            print("1. 访问: https://www.facebook.com/help/contact")
            print("2. 选择'无法访问账户'")
            print("3. 提供身份验证文件")
            print("4. 说明手机号码更换情况")
            print()
            
            print("💡 成功率最高的方法:")
            print("使用备用邮箱重置密码是最直接有效的方法!")
            
        except Exception as e:
            log_error(f"[密码重置] 手动方法提供失败: {e}", component="PasswordResetJumper")

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2
        
        print("🔓 Facebook密码重置界面直接跳转工具")
        print("⚠️  直接跳转到Facebook密码重置页面")
        print("⚠️  绕过登录界面和2FA验证")
        print("⚠️  不需要root权限")
        print("⚠️  使用备用邮箱重置密码")
        print()
        
        print("💡 这个方法的优势:")
        print("- 完全绕过2FA验证")
        print("- 不需要手机号码")
        print("- 只需要邮箱访问权限")
        print("- 是官方支持的恢复方法")
        print()
        
        confirm = input("确认跳转到Facebook密码重置页面? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return
        
        # 创建跳转器
        jumper = FacebookPasswordResetJumper(emulator_id)
        
        # 执行跳转
        await jumper.jump_to_password_reset()
        
        print("\n" + "=" * 50)
        print("✅ Facebook密码重置跳转完成")
        print("💡 现在请按照页面指导完成密码重置")
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
