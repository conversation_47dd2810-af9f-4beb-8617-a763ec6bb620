2025-07-28 08:15:18 - root - INFO - 简化日志系统初始化完成
2025-07-28 08:15:18 - main - INFO - 应用程序启动
2025-07-28 08:15:18 - __main__ - INFO - Qt应用程序已创建
2025-07-28 08:15:18 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 08:15:18 - __main__ - INFO - 统一配置管理器已创建
2025-07-28 08:15:18 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 08:15:18 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-28 08:15:18 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 08:15:18 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-28 08:15:18 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 08:15:18 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 08:15:18 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 08:15:18 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 08:15:18 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-28 08:15:18 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 08:15:18 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-28 08:15:18 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 08:15:18 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-28 08:15:18 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-28 08:15:18 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-28 08:15:18 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-28 08:15:18 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-28 08:15:18 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-28 08:15:18 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-28 08:15:18 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-28 08:15:18 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-28 08:15:18 - __main__ - INFO - UI主窗口已创建
2025-07-28 08:15:18 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-28 08:15:19 - __main__ - INFO - 主窗口已显示
2025-07-28 08:15:19 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 08:15:19 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-28 08:15:19 - __main__ - INFO - UI层和业务层已连接
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 08:15:19 - __main__ - INFO - 启动Qt事件循环
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 08:15:19 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 08:15:19 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:15:19 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 08:15:19 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 08:15:19 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:15:19 - App - INFO - ldconsole命令执行成功，输出长度: 48430
2025-07-28 08:15:19 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 08:15:19 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 08:15:19 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 08:15:19 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 08:15:19 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.23s | count: 1229
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 08:15:19 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 08:15:19 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 08:15:19 - App - INFO - ldconsole命令执行成功，输出长度: 48430
2025-07-28 08:15:19 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 08:15:19 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 08:15:19 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 08:15:19 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 08:15:19 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.22s | count: 1229
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 08:15:19 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 08:15:19 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 08:15:19 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 08:15:20 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-28 08:15:20 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-28 08:15:20 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-28 08:15:20 - __main__ - INFO - 后台服务已启动
2025-07-28 08:15:20 - __main__ - INFO - 延迟启动服务完成
2025-07-28 08:15:32 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 12 -> 5
2025-07-28 08:15:32 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 08:15:32 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:15:32 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 5
2025-07-28 08:15:32 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 5 -> 55
2025-07-28 08:15:32 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 08:15:32 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:15:32 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 55
2025-07-28 08:15:33 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 55 -> 555
2025-07-28 08:15:33 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 08:15:33 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:15:33 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 555
2025-07-28 08:15:34 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-28 08:15:34 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-28 08:15:34 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 08:15:34 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-28 08:15:35 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_fans_task
2025-07-28 08:15:35 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram关注粉丝任务请求
2025-07-28 08:15:35 - MainWindowV2 - INFO - Instagram关注粉丝任务已启动，涉及3个模拟器
2025-07-28 08:15:35 - FixedAsyncBridge - INFO - 开始处理Instagram关注粉丝任务（线程池模式），模拟器数量: 3
2025-07-28 08:15:35 - MainWindowV2 - INFO - 用户启动Instagram关注粉丝任务，模拟器数量: 3
2025-07-28 08:15:35 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [6, 7, 8]
2025-07-28 08:15:35 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 3
2025-07-28 08:15:35 - StartupManager - INFO - 批量启动请求 | count: 3
2025-07-28 08:15:35 - StartupManager - INFO - 启动调度器已启动
2025-07-28 08:15:35 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 08:15:35 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-28 08:15:35 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 08:15:35 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-28 08:15:35 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: fans
2025-07-28 08:15:35 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 3
2025-07-28 08:15:35 - InstagramFollowTaskThread - INFO - [模拟器6] Instagram关注任务线程初始化完成，模式: fans
2025-07-28 08:15:35 - InstagramFollowTaskManager - INFO - 为模拟器 6 创建Instagram关注任务线程，模式: fans
2025-07-28 08:15:35 - InstagramFollowTaskThread - INFO - [模拟器7] Instagram关注任务线程初始化完成，模式: fans
2025-07-28 08:15:35 - InstagramFollowTaskManager - INFO - 为模拟器 7 创建Instagram关注任务线程，模式: fans
2025-07-28 08:15:35 - InstagramFollowTaskThread - INFO - [模拟器8] Instagram关注任务线程初始化完成，模式: fans
2025-07-28 08:15:35 - InstagramFollowTaskManager - INFO - 为模拟器 8 创建Instagram关注任务线程，模式: fans
2025-07-28 08:15:35 - MainWindowV2 - INFO - 3个模拟器: 未知 -> 排队中
2025-07-28 08:15:35 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建3个Instagram线程
2025-07-28 08:15:35 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 3 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-28 08:15:35 - FixedAsyncBridge - INFO - 异步桥接器: Instagram关注粉丝任务请求已处理，状态: started
2025-07-28 08:15:35 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-28 08:15:35 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-28 08:15:35 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 08:15:35 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-28 08:15:35 - MainWindowV2 - INFO - 批量启动完成: 0/3 成功
2025-07-28 08:15:35 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 6
2025-07-28 08:15:35 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 3
2025-07-28 08:15:35 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_fans_task
2025-07-28 08:15:35 - Emulator - INFO - 模拟器状态变化 | emulator_id: 6 | old_state: 排队中 | new_state: 启动中
2025-07-28 08:15:35 - MainWindowV2 - INFO - 模拟器6: 排队中 -> 启动中
2025-07-28 08:15:35 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 08:15:35 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-28 08:15:35 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 6
2025-07-28 08:15:45 - Emulator - INFO - Android系统启动完成 | emulator_id: 6 | elapsed_time: 10.0秒
2025-07-28 08:15:45 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器6状态变化: 启动中 -> 运行中
2025-07-28 08:15:45 - InstagramFollowTaskManager - INFO - 启动模拟器6的Instagram关注任务线程 - 当前并发: 1/1
2025-07-28 08:15:45 - Emulator - INFO - 模拟器状态变化 | emulator_id: 6 | old_state: 启动中 | new_state: 运行中
2025-07-28 08:15:45 - Emulator - INFO - 模拟器启动成功 | emulator_id: 6 | running_count: 1
2025-07-28 08:15:45 - TaskActivityHeartbeatManager - INFO - 模拟器 6 已添加到任务活动监控，失败计数: 0
2025-07-28 08:15:45 - MainWindowV2 - INFO - 任务完成: 模拟器6, 任务start
2025-07-28 08:15:45 - InstagramTaskThread - INFO - [模拟器6] 开始等待启动完成
2025-07-28 08:15:45 - MainWindowV2 - INFO - 模拟器6启动成功
2025-07-28 08:15:45 - InstagramTaskThread - INFO - [模拟器6] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 08:15:45 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=6, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 08:15:45 - InstagramTaskThread - INFO - [模拟器6] 开始窗口排列
2025-07-28 08:15:45 - WindowArrangementManager - INFO - 模拟器6启动完成，立即触发窗口排列
2025-07-28 08:15:45 - MainWindowV2 - WARNING - 未找到模拟器6，无法更新状态
2025-07-28 08:15:45 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中
2025-07-28 08:15:45 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 08:15:45 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-28 08:15:45 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中, PID: 19216
2025-07-28 08:15:45 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中
2025-07-28 08:15:47 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 08:15:47 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 08:15:47 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 08:15:47 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 08:15:47 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 08:15:47 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 08:15:47 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-6' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 08:15:47 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 08:15:47 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 08:15:47 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-6' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 08:15:47 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 08:15:47 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 08:15:53 - InstagramTaskThread - INFO - [模拟器6] 窗口排列完成
2025-07-28 08:15:53 - InstagramFollowTaskThread - INFO - [模拟器6] 开始执行Instagram关注粉丝任务
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] 雷电模拟器API初始化成功
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] 已设置ld.emulator_id = 6
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置热加载观察者已注册
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] Instagram私信任务执行器初始化完成
2025-07-28 08:15:53 - InstagramFollowTask - INFO - [模拟器6] Instagram关注任务配置加载完成
2025-07-28 08:15:53 - InstagramFollowTask - INFO - [模拟器6] Instagram关注任务执行器初始化完成
2025-07-28 08:15:53 - InstagramFollowTask - INFO - [模拟器6] 关注模式已设置为: fans
2025-07-28 08:15:53 - InstagramFollowTask - INFO - [模拟器6] 开始执行Instagram关注任务
2025-07-28 08:15:53 - InstagramFollowTask - WARNING - [模拟器6] 任务开始时间未由线程传递，在此设置
2025-07-28 08:15:53 - InstagramFollowTask - INFO - [模拟器6] 任务超时设置: 555秒，已运行: 0.00秒
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] 模拟器Android系统运行正常，桌面稳定
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] 开始检查应用安装状态
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] 📊 应用安装状态检测结果:
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] ✅ 所有必要应用已安装
2025-07-28 08:15:53 - InstagramDMTask - INFO - [模拟器6] 开始启动V2Ray应用
2025-07-28 08:15:54 - InstagramDMTask - INFO - [模拟器6] V2Ray启动命令执行成功，等待应用加载
2025-07-28 08:15:54 - InstagramDMTask - INFO - [模拟器6] V2Ray应用启动结果: 成功
2025-07-28 08:15:57 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray应用启动成功
2025-07-28 08:15:57 - InstagramDMTask - INFO - [模拟器6] 开始检查V2Ray节点列表状态
2025-07-28 08:15:58 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:15:58 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:15:58 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-28 08:15:58 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-28 08:15:58 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:15:58 - InstagramDMTask - INFO - [模拟器6] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 08:15:58 - InstagramDMTask - INFO - [模拟器6] 开始连接V2Ray节点
2025-07-28 08:15:59 - InstagramDMTask - INFO - [模拟器6] 当前连接状态: 未连接
2025-07-28 08:15:59 - InstagramDMTask - INFO - [模拟器6] V2Ray节点未连接，开始连接
2025-07-28 08:16:01 - InstagramDMTask - INFO - [模拟器6] 已点击连接按钮，等待连接完成
2025-07-28 08:16:03 - InstagramDMTask - INFO - [模拟器6] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 08:16:03 - InstagramDMTask - INFO - [模拟器6] V2Ray节点连接成功
2025-07-28 08:16:03 - InstagramDMTask - INFO - [模拟器6] 开始测试V2Ray节点延迟
2025-07-28 08:16:03 - InstagramDMTask - INFO - [模拟器6] 开始V2Ray节点延迟测试
2025-07-28 08:16:04 - InstagramDMTask - INFO - [模拟器6] 当前测试状态: 已连接，点击测试连接
2025-07-28 08:16:04 - InstagramDMTask - INFO - [模拟器6] 点击开始延迟测试
2025-07-28 08:16:04 - InstagramDMTask - INFO - [模拟器6] 已点击测试按钮，等待测试结果
2025-07-28 08:16:06 - InstagramDMTask - INFO - [模拟器6] 测试状态监控 (1/30): 连接成功：延时 533 毫秒
2025-07-28 08:16:06 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray节点延迟测试成功: 连接成功：延时 533 毫秒
2025-07-28 08:16:06 - InstagramDMTask - INFO - [模拟器6] 等待5秒后进入下一阶段
2025-07-28 08:16:11 - InstagramDMTask - INFO - [模拟器6] 开始启动Instagram应用
2025-07-28 08:16:11 - InstagramDMTask - INFO - [模拟器6] Instagram启动命令执行成功，等待应用加载
2025-07-28 08:16:14 - InstagramDMTask - INFO - [模拟器6] ✅ Instagram应用启动命令执行完成
2025-07-28 08:16:14 - InstagramDMTask - INFO - [模拟器6] Instagram启动检测 第1/5次
2025-07-28 08:16:18 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:16:18 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:16:18 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-28 08:16:18 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-28 08:16:18 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:16:19 - InstagramDMTask - INFO - [模拟器6] ✅ 批量验证成功
2025-07-28 08:16:19 - InstagramDMTask - INFO - [模拟器6] ✅ 验证成功
2025-07-28 08:16:19 - InstagramFollowTask - INFO - [模拟器6] Instagram页面状态检测结果: 正常-在主页面
2025-07-28 08:16:19 - InstagramFollowTask - INFO - [模拟器6] ✅ Instagram已在主页面，可以继续执行任务
2025-07-28 08:16:19 - InstagramFollowTask - INFO - [模拟器6] 开始执行关注任务，模式: fans
2025-07-28 08:16:19 - InstagramFollowTask - INFO - [模拟器6] 开始执行【模式二：关注粉丝模式】
2025-07-28 08:16:19 - InstagramFollowTask - INFO - [模拟器6] 开始【模式二：关注粉丝循环】，目标关注数: 2
2025-07-28 08:16:19 - InstagramFollowTask - INFO - [模拟器6] 👥 开始处理目标用户: ___c_0711 (关注其粉丝)
2025-07-28 08:16:19 - InstagramFollowTask - INFO - [模拟器6] 开始处理用户：___c_0711
2025-07-28 08:16:19 - InstagramFollowTask - INFO - [模拟器6] 跳转到用户主页：___c_0711
2025-07-28 08:16:23 - InstagramFollowTask - INFO - [模拟器6] ✅ 标题栏验证成功，当前在用户 ___c_0711 的资料页
2025-07-28 08:16:23 - InstagramFollowTask - INFO - [模拟器6] 当前已在用户主页：___c_0711
2025-07-28 08:16:25 - InstagramFollowTask - INFO - [模拟器6] 🔍 检测到私密账户标识: '这是私密帐户'
2025-07-28 08:16:25 - InstagramFollowTask - INFO - [模拟器6] 用户 ___c_0711 是私密账户，跳过
2025-07-28 08:16:25 - InstagramFollowTask - INFO - [模拟器6] 📊 目标用户 ___c_0711 处理结果: 私密账户
2025-07-28 08:16:25 - InstagramFollowTask - INFO - [模拟器6] ⏱️ 切换用户延迟: 1954毫秒
2025-07-28 08:16:27 - InstagramFollowTask - INFO - [模拟器6] 👥 开始处理目标用户: ui_y117 (关注其粉丝)
2025-07-28 08:16:27 - InstagramFollowTask - INFO - [模拟器6] 开始处理用户：ui_y117
2025-07-28 08:16:27 - InstagramFollowTask - INFO - [模拟器6] 跳转到用户主页：ui_y117
2025-07-28 08:16:31 - InstagramFollowTask - INFO - [模拟器6] ✅ 标题栏验证成功，当前在用户 ui_y117 的资料页
2025-07-28 08:16:31 - InstagramFollowTask - INFO - [模拟器6] 当前已在用户主页：ui_y117
2025-07-28 08:16:38 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:16:38 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:16:38 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-28 08:16:38 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-28 08:16:38 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:16:39 - InstagramFollowTask - INFO - [模拟器6] 成功打开粉丝列表
2025-07-28 08:16:39 - InstagramFollowTask - INFO - [模拟器6] 开始检测粉丝列表
2025-07-28 08:16:40 - InstagramFollowTask - INFO - [模拟器6] 找到7个粉丝栏
2025-07-28 08:16:40 - InstagramFollowTask - INFO - [模拟器6] 成功提取 5 个粉丝信息
2025-07-28 08:16:40 - InstagramFollowTask - INFO - [模拟器6] 粉丝列表检测完成，耗时1.25秒
2025-07-28 08:16:40 - InstagramFollowTask - INFO - [模拟器6] 🔍 语言检测: 日文, 筛选结果: True
2025-07-28 08:16:40 - InstagramFollowTask - INFO - [模拟器6] 用户: ヘアメイク/ Noriko Mannen / 萬年 典子 符合地区筛选
2025-07-28 08:16:40 - InstagramFollowTask - INFO - [模拟器6] 点击API返回值：，类型：<class 'str'>
2025-07-28 08:16:40 - InstagramFollowTask - INFO - [模拟器6] 关注成功，坐标：(215, 274)，API返回字符串：''
2025-07-28 08:16:40 - InstagramFollowTask - INFO - [模拟器6] 已关注数：1/2, 711毫秒后下一个, 耗时: 1.48 秒
2025-07-28 08:16:40 - InstagramFollowTask - INFO - [模拟器6] Instagram关注任务状态更新信号已发送: 关注中 (1/2)
2025-07-28 08:16:40 - MainWindowV2 - INFO - 模拟器6的Instagram任务状态已更新: 关注中 (1/2)
2025-07-28 08:16:41 - InstagramFollowTask - INFO - [模拟器6] 🔍 语言检测: 日文, 筛选结果: True
2025-07-28 08:16:41 - InstagramFollowTask - INFO - [模拟器6] 用户: アトリエプルーシュ | 結婚式プロフィールムービーテンプレ 符合地区筛选
2025-07-28 08:16:41 - InstagramFollowTask - INFO - [模拟器6] 点击API返回值：，类型：<class 'str'>
2025-07-28 08:16:41 - InstagramFollowTask - INFO - [模拟器6] 关注成功，坐标：(215, 319)，API返回字符串：''
2025-07-28 08:16:41 - InstagramFollowTask - INFO - [模拟器6] Instagram关注任务状态更新信号已发送: 已完成 (2/2)
2025-07-28 08:16:41 - MainWindowV2 - INFO - 模拟器6的Instagram任务状态已更新: 已完成 (2/2)
2025-07-28 08:16:41 - InstagramFollowTask - INFO - [模拟器6] 🎉 完成目标用户 ui_y117 的粉丝关注: 已完成任务，共关注 2 人
2025-07-28 08:16:41 - InstagramFollowTask - INFO - [模拟器6] ✅ 【模式二：关注粉丝循环】完成，成功关注: 2, 跳过蓝V: 0, 跳过私密: 0
2025-07-28 08:16:41 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置观察者已注销
2025-07-28 08:16:41 - InstagramDMTask - INFO - [模拟器6] 开始任务完成后清理工作
2025-07-28 08:16:41 - TaskActivityHeartbeatManager - INFO - 模拟器 6 已从任务活动监控移除
2025-07-28 08:16:41 - InstagramDMTask - INFO - [模拟器6] 心跳监控已移除
2025-07-28 08:16:41 - InstagramDMTask - INFO - [模拟器6] 准备关闭模拟器
2025-07-28 08:16:41 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 6 | remaining_running: 0
2025-07-28 08:16:41 - Emulator - INFO - 模拟器停止成功 | emulator_id: 6
2025-07-28 08:16:41 - InstagramDMTask - INFO - [模拟器6] 模拟器已成功关闭
2025-07-28 08:16:41 - InstagramDMTask - INFO - [模拟器6] 任务完成后清理工作完成
2025-07-28 08:16:41 - InstagramFollowTaskThread - INFO - [模拟器6] Instagram关注粉丝任务执行成功
2025-07-28 08:16:41 - MainWindowV2 - INFO - 任务完成: 模拟器6, 任务stop
2025-07-28 08:16:41 - MainWindowV2 - INFO - 模拟器6停止成功
2025-07-28 08:16:41 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=6, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 08:16:41 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 08:16:42 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器7状态变化: 排队中 -> 启动中
2025-07-28 08:16:42 - Emulator - INFO - 模拟器状态变化 | emulator_id: 7 | old_state: 排队中 | new_state: 启动中
2025-07-28 08:16:42 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器7状态变化: 排队中 -> 启动中
2025-07-28 08:16:42 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 7
2025-07-28 08:16:42 - MainWindowV2 - INFO - 模拟器7: 排队中 -> 启动中
2025-07-28 08:16:42 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 08:16:42 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-28 08:16:42 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 7
2025-07-28 08:16:55 - Emulator - INFO - Android系统启动完成 | emulator_id: 7 | elapsed_time: 13.0秒
2025-07-28 08:16:55 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器7状态变化: 启动中 -> 运行中
2025-07-28 08:16:55 - InstagramFollowTaskManager - INFO - 启动模拟器7的Instagram关注任务线程 - 当前并发: 2/1
2025-07-28 08:16:55 - Emulator - INFO - 模拟器启动成功 | emulator_id: 7 | running_count: 1
2025-07-28 08:16:55 - Emulator - INFO - 模拟器状态变化 | emulator_id: 7 | old_state: 启动中 | new_state: 运行中
2025-07-28 08:16:55 - TaskActivityHeartbeatManager - INFO - 模拟器 7 已添加到任务活动监控，失败计数: 0
2025-07-28 08:16:55 - InstagramTaskThread - INFO - [模拟器7] 开始等待启动完成
2025-07-28 08:16:55 - InstagramTaskThread - INFO - [模拟器7] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 08:16:55 - MainWindowV2 - INFO - 任务完成: 模拟器7, 任务start
2025-07-28 08:16:55 - InstagramTaskThread - INFO - [模拟器7] 开始窗口排列
2025-07-28 08:16:55 - MainWindowV2 - INFO - 模拟器7启动成功
2025-07-28 08:16:55 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=7, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 08:16:55 - WindowArrangementManager - INFO - 模拟器7启动完成，立即触发窗口排列
2025-07-28 08:16:55 - MainWindowV2 - WARNING - 未找到模拟器7，无法更新状态
2025-07-28 08:16:55 - MainWindowV2 - INFO - 模拟器7: 启动中 -> 运行中
2025-07-28 08:16:55 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 08:16:55 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-28 08:16:55 - MainWindowV2 - INFO - 模拟器7: 启动中 -> 运行中, PID: 10972
2025-07-28 08:16:55 - MainWindowV2 - INFO - 模拟器7: 启动中 -> 运行中
2025-07-28 08:16:57 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 08:16:57 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 08:16:57 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 08:16:57 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 08:16:57 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 08:16:57 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 08:16:57 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-7' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 08:16:57 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 08:16:57 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 08:16:57 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-7' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 08:16:57 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 08:16:57 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 08:16:58 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:16:58 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:16:58 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 7
2025-07-28 08:16:58 - MainWindowV2 - WARNING - 模拟器7心跳状态更新未产生变化
2025-07-28 08:16:58 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:17:03 - InstagramTaskThread - INFO - [模拟器7] 窗口排列完成
2025-07-28 08:17:03 - InstagramFollowTaskThread - INFO - [模拟器7] 开始执行Instagram关注粉丝任务
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] 雷电模拟器API初始化成功
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] 已设置ld.emulator_id = 7
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] Instagram任务配置热加载观察者已注册
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] Instagram私信任务执行器初始化完成
2025-07-28 08:17:03 - InstagramFollowTask - INFO - [模拟器7] Instagram关注任务配置加载完成
2025-07-28 08:17:03 - InstagramFollowTask - INFO - [模拟器7] Instagram关注任务执行器初始化完成
2025-07-28 08:17:03 - InstagramFollowTask - INFO - [模拟器7] 关注模式已设置为: fans
2025-07-28 08:17:03 - InstagramFollowTask - INFO - [模拟器7] 开始执行Instagram关注任务
2025-07-28 08:17:03 - InstagramFollowTask - WARNING - [模拟器7] 任务开始时间未由线程传递，在此设置
2025-07-28 08:17:03 - InstagramFollowTask - INFO - [模拟器7] 任务超时设置: 555秒，已运行: 0.00秒
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] 模拟器Android系统运行正常，桌面稳定
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] 开始检查应用安装状态
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] 📊 应用安装状态检测结果:
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] ✅ 所有必要应用已安装
2025-07-28 08:17:03 - InstagramDMTask - INFO - [模拟器7] 开始启动V2Ray应用
2025-07-28 08:17:04 - InstagramDMTask - INFO - [模拟器7] V2Ray启动命令执行成功，等待应用加载
2025-07-28 08:17:04 - InstagramDMTask - INFO - [模拟器7] V2Ray应用启动结果: 成功
2025-07-28 08:17:07 - InstagramDMTask - INFO - [模拟器7] ✅ V2Ray应用启动成功
2025-07-28 08:17:07 - InstagramDMTask - INFO - [模拟器7] 开始检查V2Ray节点列表状态
2025-07-28 08:17:08 - InstagramDMTask - INFO - [模拟器7] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 08:17:08 - InstagramDMTask - INFO - [模拟器7] 开始连接V2Ray节点
2025-07-28 08:17:09 - InstagramDMTask - INFO - [模拟器7] 当前连接状态: 未连接
2025-07-28 08:17:09 - InstagramDMTask - INFO - [模拟器7] V2Ray节点未连接，开始连接
2025-07-28 08:17:10 - InstagramDMTask - INFO - [模拟器7] 已点击连接按钮，等待连接完成
2025-07-28 08:17:13 - InstagramDMTask - INFO - [模拟器7] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 08:17:13 - InstagramDMTask - INFO - [模拟器7] V2Ray节点连接成功
2025-07-28 08:17:13 - InstagramDMTask - INFO - [模拟器7] 开始测试V2Ray节点延迟
2025-07-28 08:17:13 - InstagramDMTask - INFO - [模拟器7] 开始V2Ray节点延迟测试
2025-07-28 08:17:14 - InstagramDMTask - INFO - [模拟器7] 当前测试状态: 已连接，点击测试连接
2025-07-28 08:17:14 - InstagramDMTask - INFO - [模拟器7] 点击开始延迟测试
2025-07-28 08:17:14 - InstagramDMTask - INFO - [模拟器7] 已点击测试按钮，等待测试结果
2025-07-28 08:17:15 - InstagramDMTask - INFO - [模拟器7] 测试状态监控 (1/30): 测试中…
2025-07-28 08:17:15 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:17:17 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:17:18 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:17:18 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:17:18 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 7
2025-07-28 08:17:18 - MainWindowV2 - WARNING - 模拟器7心跳状态更新未产生变化
2025-07-28 08:17:18 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:17:18 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:17:19 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:17:21 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:17:22 - InstagramDMTask - INFO - [模拟器7] ✅ V2Ray节点延迟测试成功: 连接成功：延时 7316 毫秒
2025-07-28 08:17:22 - InstagramDMTask - INFO - [模拟器7] 等待5秒后进入下一阶段
2025-07-28 08:17:27 - InstagramDMTask - INFO - [模拟器7] 开始启动Instagram应用
2025-07-28 08:17:28 - InstagramDMTask - INFO - [模拟器7] Instagram启动命令执行成功，等待应用加载
2025-07-28 08:17:31 - InstagramDMTask - INFO - [模拟器7] ✅ Instagram应用启动命令执行完成
2025-07-28 08:17:31 - InstagramDMTask - INFO - [模拟器7] Instagram启动检测 第1/5次
2025-07-28 08:17:37 - InstagramDMTask - INFO - [模拟器7] ✅ 批量验证成功
2025-07-28 08:17:37 - InstagramDMTask - INFO - [模拟器7] ✅ 验证成功
2025-07-28 08:17:37 - InstagramFollowTask - INFO - [模拟器7] Instagram页面状态检测结果: 正常-在主页面
2025-07-28 08:17:37 - InstagramFollowTask - INFO - [模拟器7] ✅ Instagram已在主页面，可以继续执行任务
2025-07-28 08:17:37 - InstagramFollowTask - INFO - [模拟器7] 开始执行关注任务，模式: fans
2025-07-28 08:17:37 - InstagramFollowTask - INFO - [模拟器7] 开始执行【模式二：关注粉丝模式】
2025-07-28 08:17:37 - InstagramFollowTask - INFO - [模拟器7] 开始【模式二：关注粉丝循环】，目标关注数: 2
2025-07-28 08:17:37 - InstagramFollowTask - INFO - [模拟器7] 👥 开始处理目标用户: _nami_sakamoto (关注其粉丝)
2025-07-28 08:17:37 - InstagramFollowTask - INFO - [模拟器7] 开始处理用户：_nami_sakamoto
2025-07-28 08:17:37 - InstagramFollowTask - INFO - [模拟器7] 跳转到用户主页：_nami_sakamoto
2025-07-28 08:17:38 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:17:38 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:17:38 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 7
2025-07-28 08:17:38 - MainWindowV2 - WARNING - 模拟器7心跳状态更新未产生变化
2025-07-28 08:17:38 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:17:40 - InstagramFollowTask - INFO - [模拟器7] ✅ 标题栏验证成功，当前在用户 _nami_sakamoto 的资料页
2025-07-28 08:17:40 - InstagramFollowTask - INFO - [模拟器7] 当前已在用户主页：_nami_sakamoto
2025-07-28 08:17:51 - InstagramFollowTask - INFO - [模拟器7] 成功打开粉丝列表
2025-07-28 08:17:51 - InstagramFollowTask - INFO - [模拟器7] 开始检测粉丝列表
2025-07-28 08:17:52 - InstagramFollowTask - INFO - [模拟器7] 找到7个粉丝栏
2025-07-28 08:17:52 - InstagramFollowTask - INFO - [模拟器7] 成功提取 6 个粉丝信息
2025-07-28 08:17:52 - InstagramFollowTask - INFO - [模拟器7] 粉丝列表检测完成，耗时1.24秒
2025-07-28 08:17:52 - InstagramFollowTask - INFO - [模拟器7] 开始滑动
2025-07-28 08:17:53 - InstagramFollowTask - INFO - [模拟器7] 滚动完成，耗时0.63秒
2025-07-28 08:17:53 - InstagramFollowTask - INFO - [模拟器7] 滚动完成，开始等待1秒让页面稳定加载
2025-07-28 08:17:54 - InstagramFollowTask - INFO - [模拟器7] 1秒等待完成，准备下一轮循环
2025-07-28 08:17:54 - InstagramFollowTask - INFO - [模拟器7] 开始检测粉丝列表
2025-07-28 08:17:55 - InstagramFollowTask - INFO - [模拟器7] 找到8个粉丝栏
2025-07-28 08:17:55 - InstagramFollowTask - INFO - [模拟器7] 成功提取 7 个粉丝信息
2025-07-28 08:17:55 - InstagramFollowTask - INFO - [模拟器7] 粉丝列表检测完成，耗时1.32秒
2025-07-28 08:17:55 - InstagramFollowTask - INFO - [模拟器7] 🔍 语言检测: 日文, 筛选结果: True
2025-07-28 08:17:55 - InstagramFollowTask - INFO - [模拟器7] 用户: ビグムン🌝 符合地区筛选
2025-07-28 08:17:56 - InstagramFollowTask - INFO - [模拟器7] 点击API返回值：，类型：<class 'str'>
2025-07-28 08:17:56 - InstagramFollowTask - INFO - [模拟器7] 关注成功，坐标：(215, 141)，API返回字符串：''
2025-07-28 08:17:56 - InstagramFollowTask - INFO - [模拟器7] 已关注数：1/2, 715毫秒后下一个, 耗时: 4.43 秒
2025-07-28 08:17:56 - InstagramFollowTask - INFO - [模拟器7] Instagram关注任务状态更新信号已发送: 关注中 (1/2)
2025-07-28 08:17:56 - MainWindowV2 - INFO - 模拟器7的Instagram任务状态已更新: 关注中 (1/2)
2025-07-28 08:17:56 - InstagramFollowTask - INFO - [模拟器7] 🔍 语言检测: 日文, 筛选结果: True
2025-07-28 08:17:56 - InstagramFollowTask - INFO - [模拟器7] 用户: るか🐬 符合地区筛选
2025-07-28 08:17:57 - InstagramFollowTask - INFO - [模拟器7] 点击API返回值：，类型：<class 'str'>
2025-07-28 08:17:57 - InstagramFollowTask - INFO - [模拟器7] 关注成功，坐标：(215, 321)，API返回字符串：''
2025-07-28 08:17:57 - InstagramFollowTask - INFO - [模拟器7] Instagram关注任务状态更新信号已发送: 已完成 (2/2)
2025-07-28 08:17:57 - MainWindowV2 - INFO - 模拟器7的Instagram任务状态已更新: 已完成 (2/2)
2025-07-28 08:17:57 - InstagramFollowTask - INFO - [模拟器7] 🎉 完成目标用户 _nami_sakamoto 的粉丝关注: 已完成任务，共关注 2 人
2025-07-28 08:17:57 - InstagramFollowTask - INFO - [模拟器7] ✅ 【模式二：关注粉丝循环】完成，成功关注: 2, 跳过蓝V: 0, 跳过私密: 0
2025-07-28 08:17:57 - InstagramDMTask - INFO - [模拟器7] Instagram任务配置观察者已注销
2025-07-28 08:17:57 - InstagramDMTask - INFO - [模拟器7] 开始任务完成后清理工作
2025-07-28 08:17:57 - TaskActivityHeartbeatManager - INFO - 模拟器 7 已从任务活动监控移除
2025-07-28 08:17:57 - InstagramDMTask - INFO - [模拟器7] 心跳监控已移除
2025-07-28 08:17:57 - InstagramDMTask - INFO - [模拟器7] 准备关闭模拟器
2025-07-28 08:17:57 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 7 | remaining_running: 0
2025-07-28 08:17:57 - Emulator - INFO - 模拟器停止成功 | emulator_id: 7
2025-07-28 08:17:57 - InstagramDMTask - INFO - [模拟器7] 模拟器已成功关闭
2025-07-28 08:17:57 - MainWindowV2 - INFO - 任务完成: 模拟器7, 任务stop
2025-07-28 08:17:57 - InstagramDMTask - INFO - [模拟器7] 任务完成后清理工作完成
2025-07-28 08:17:57 - MainWindowV2 - INFO - 模拟器7停止成功
2025-07-28 08:17:57 - InstagramFollowTaskThread - INFO - [模拟器7] Instagram关注粉丝任务执行成功
2025-07-28 08:17:57 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=7, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 08:17:57 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 08:17:57 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器8状态变化: 排队中 -> 启动中
2025-07-28 08:17:57 - Emulator - INFO - 模拟器状态变化 | emulator_id: 8 | old_state: 排队中 | new_state: 启动中
2025-07-28 08:17:57 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器8状态变化: 排队中 -> 启动中
2025-07-28 08:17:57 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 8
2025-07-28 08:17:57 - MainWindowV2 - INFO - 模拟器8: 排队中 -> 启动中
2025-07-28 08:17:57 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 08:17:57 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-28 08:17:57 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 8
2025-07-28 08:18:10 - Emulator - INFO - Android系统启动完成 | emulator_id: 8 | elapsed_time: 13.2秒
2025-07-28 08:18:10 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器8状态变化: 启动中 -> 运行中
2025-07-28 08:18:10 - Emulator - INFO - 模拟器状态变化 | emulator_id: 8 | old_state: 启动中 | new_state: 运行中
2025-07-28 08:18:10 - InstagramFollowTaskManager - INFO - 启动模拟器8的Instagram关注任务线程 - 当前并发: 3/1
2025-07-28 08:18:10 - TaskActivityHeartbeatManager - INFO - 模拟器 8 已添加到任务活动监控，失败计数: 0
2025-07-28 08:18:10 - Emulator - INFO - 模拟器启动成功 | emulator_id: 8 | running_count: 1
2025-07-28 08:18:10 - MainWindowV2 - INFO - 任务完成: 模拟器8, 任务start
2025-07-28 08:18:10 - MainWindowV2 - INFO - 模拟器8启动成功
2025-07-28 08:18:10 - InstagramTaskThread - INFO - [模拟器8] 开始等待启动完成
2025-07-28 08:18:10 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=8, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 08:18:10 - InstagramTaskThread - INFO - [模拟器8] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 08:18:10 - WindowArrangementManager - INFO - 模拟器8启动完成，立即触发窗口排列
2025-07-28 08:18:10 - InstagramTaskThread - INFO - [模拟器8] 开始窗口排列
2025-07-28 08:18:10 - MainWindowV2 - WARNING - 未找到模拟器8，无法更新状态
2025-07-28 08:18:10 - MainWindowV2 - INFO - 模拟器8: 启动中 -> 运行中
2025-07-28 08:18:10 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 08:18:10 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-28 08:18:10 - MainWindowV2 - INFO - 模拟器8: 启动中 -> 运行中, PID: 22408
2025-07-28 08:18:10 - MainWindowV2 - INFO - 模拟器8: 启动中 -> 运行中
2025-07-28 08:18:11 - StartupManager - INFO - 调度器已停止
2025-07-28 08:18:12 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 08:18:12 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 08:18:12 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 08:18:12 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 08:18:12 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 08:18:12 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 08:18:12 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-8' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 08:18:12 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 08:18:12 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 08:18:12 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-8' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 08:18:12 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 08:18:12 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 08:18:18 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:18:18 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:18:18 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 8
2025-07-28 08:18:18 - MainWindowV2 - WARNING - 模拟器8心跳状态更新未产生变化
2025-07-28 08:18:18 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:18:18 - InstagramTaskThread - INFO - [模拟器8] 窗口排列完成
2025-07-28 08:18:18 - InstagramFollowTaskThread - INFO - [模拟器8] 开始执行Instagram关注粉丝任务
2025-07-28 08:18:18 - InstagramDMTask - INFO - [模拟器8] 雷电模拟器API初始化成功
2025-07-28 08:18:18 - InstagramDMTask - INFO - [模拟器8] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 08:18:18 - InstagramDMTask - INFO - [模拟器8] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 08:18:18 - InstagramDMTask - INFO - [模拟器8] 已设置ld.emulator_id = 8
2025-07-28 08:18:18 - InstagramDMTask - INFO - [模拟器8] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 08:18:18 - InstagramDMTask - INFO - [模拟器8] Instagram任务配置热加载观察者已注册
2025-07-28 08:18:18 - InstagramDMTask - INFO - [模拟器8] Instagram私信任务执行器初始化完成
2025-07-28 08:18:18 - InstagramFollowTask - INFO - [模拟器8] Instagram关注任务配置加载完成
2025-07-28 08:18:18 - InstagramFollowTask - INFO - [模拟器8] Instagram关注任务执行器初始化完成
2025-07-28 08:18:18 - InstagramFollowTask - INFO - [模拟器8] 关注模式已设置为: fans
2025-07-28 08:18:18 - InstagramFollowTask - INFO - [模拟器8] 开始执行Instagram关注任务
2025-07-28 08:18:18 - InstagramFollowTask - WARNING - [模拟器8] 任务开始时间未由线程传递，在此设置
2025-07-28 08:18:18 - InstagramFollowTask - INFO - [模拟器8] 任务超时设置: 555秒，已运行: 0.00秒
2025-07-28 08:18:18 - InstagramDMTask - INFO - [模拟器8] 模拟器Android系统运行正常，桌面稳定
2025-07-28 08:18:18 - InstagramDMTask - INFO - [模拟器8] 开始检查应用安装状态
2025-07-28 08:18:19 - InstagramDMTask - INFO - [模拟器8] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 08:18:19 - InstagramDMTask - INFO - [模拟器8] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 08:18:19 - InstagramDMTask - INFO - [模拟器8] 📊 应用安装状态检测结果:
2025-07-28 08:18:19 - InstagramDMTask - INFO - [模拟器8] ✅ 所有必要应用已安装
2025-07-28 08:18:19 - InstagramDMTask - INFO - [模拟器8] 开始启动V2Ray应用
2025-07-28 08:18:19 - InstagramDMTask - INFO - [模拟器8] V2Ray启动命令执行成功，等待应用加载
2025-07-28 08:18:19 - InstagramDMTask - INFO - [模拟器8] V2Ray应用启动结果: 成功
2025-07-28 08:18:22 - InstagramDMTask - INFO - [模拟器8] ✅ V2Ray应用启动成功
2025-07-28 08:18:22 - InstagramDMTask - INFO - [模拟器8] 开始检查V2Ray节点列表状态
2025-07-28 08:18:23 - InstagramDMTask - INFO - [模拟器8] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 08:18:23 - InstagramDMTask - INFO - [模拟器8] 开始连接V2Ray节点
2025-07-28 08:18:24 - InstagramDMTask - INFO - [模拟器8] 当前连接状态: 未连接
2025-07-28 08:18:24 - InstagramDMTask - INFO - [模拟器8] V2Ray节点未连接，开始连接
2025-07-28 08:18:26 - InstagramDMTask - INFO - [模拟器8] 已点击连接按钮，等待连接完成
2025-07-28 08:18:28 - InstagramDMTask - INFO - [模拟器8] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 08:18:28 - InstagramDMTask - INFO - [模拟器8] V2Ray节点连接成功
2025-07-28 08:18:28 - InstagramDMTask - INFO - [模拟器8] 开始测试V2Ray节点延迟
2025-07-28 08:18:28 - InstagramDMTask - INFO - [模拟器8] 开始V2Ray节点延迟测试
2025-07-28 08:18:29 - InstagramDMTask - INFO - [模拟器8] 当前测试状态: 已连接，点击测试连接
2025-07-28 08:18:29 - InstagramDMTask - INFO - [模拟器8] 点击开始延迟测试
2025-07-28 08:18:29 - InstagramDMTask - INFO - [模拟器8] 已点击测试按钮，等待测试结果
2025-07-28 08:18:31 - InstagramDMTask - INFO - [模拟器8] 测试状态监控 (1/30): 测试中…
2025-07-28 08:18:31 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:32 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:33 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:35 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:36 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:38 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:38 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:18:38 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:18:38 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 8
2025-07-28 08:18:38 - MainWindowV2 - WARNING - 模拟器8心跳状态更新未产生变化
2025-07-28 08:18:38 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:18:39 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:40 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:43 - InstagramDMTask - ERROR - [模拟器8] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: context deadline exceeded
2025-07-28 08:18:43 - InstagramDMTask - INFO - [模拟器8] 点击失败状态重置UI
2025-07-28 08:18:43 - InstagramDMTask - INFO - [模拟器8] 已点击失败状态，等待UI重置
2025-07-28 08:18:43 - InstagramDMTask - INFO - [模拟器8] 继续等待测试结果 (1/3)
2025-07-28 08:18:45 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:46 - InstagramDMTask - INFO - [模拟器8] 测试状态监控 (2/30): 测试中…
2025-07-28 08:18:46 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:47 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:48 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:50 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:51 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:52 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:54 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:56 - InstagramDMTask - ERROR - [模拟器8] ❌ V2Ray节点延迟测试失败 (2/3): 失败：: net/http: request canceled (Client.Timeout exceeded while awaiting headers)
2025-07-28 08:18:56 - InstagramDMTask - INFO - [模拟器8] 点击失败状态重置UI
2025-07-28 08:18:56 - InstagramDMTask - INFO - [模拟器8] 已点击失败状态，等待UI重置
2025-07-28 08:18:57 - InstagramDMTask - INFO - [模拟器8] 继续等待测试结果 (2/3)
2025-07-28 08:18:58 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:18:58 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:18:58 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 8
2025-07-28 08:18:58 - MainWindowV2 - WARNING - 模拟器8心跳状态更新未产生变化
2025-07-28 08:18:58 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:18:58 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:18:59 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:19:01 - InstagramDMTask - INFO - [模拟器8] 测试状态监控 (3/30): 测试中…
2025-07-28 08:19:01 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:19:02 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:19:03 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:19:05 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:19:06 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
