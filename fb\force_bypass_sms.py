#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 强制绕过SMS验证工具
========================================
功能描述: 强制绕过Facebook短信验证，直接访问2FA设置

核心策略:
1. 修改系统级验证状态
2. 伪造SMS验证成功
3. 直接跳转到TOTP设置页面
4. 绕过所有服务器端检查

使用方法:
python fb/force_bypass_sms.py [emulator_id]

注意事项:
- 需要root权限
- 会深度修改系统状态
- 仅用于紧急情况
========================================
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error

class ForceSMSBypass:
    """强制SMS绕过工具"""
    
    def __init__(self, emulator_id: int = 2):
        self.emulator_id = emulator_id
        self.task = None
        self.facebook_package = "com.facebook.katana"
        
    async def force_bypass(self):
        """强制绕过SMS验证"""
        try:
            print("🔓 强制绕过SMS验证工具")
            print("=" * 50)
            print("⚠️  将强制绕过Facebook短信验证")
            print("⚠️  直接访问TOTP设置页面")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：停止Facebook应用
            await self._force_stop_facebook()
            
            # 第二步：修改系统级验证状态
            await self._modify_system_verification_state()
            
            # 第三步：注入绕过数据
            await self._inject_bypass_data()
            
            # 第四步：修改网络请求
            await self._modify_network_requests()
            
            # 第五步：重启应用并直接跳转
            await self._restart_and_jump_to_2fa()
            
            print("✅ 强制绕过完成！")
            print()
            print("📋 现在请尝试:")
            print("1. 在Facebook应用中进入设置")
            print("2. 选择安全和登录")
            print("3. 选择双重验证")
            print("4. 应该能够直接进入TOTP设置页面")
            print("5. 使用验证码: 262329 (如果需要)")
            
        except Exception as e:
            log_error(f"强制绕过失败: {e}", component="ForceSMSBypass")

    async def _force_stop_facebook(self):
        """强制停止Facebook应用"""
        print("🛑 强制停止Facebook应用...")
        
        # 杀死所有Facebook进程
        self.task.ld.execute_ld(self.emulator_id, f"am force-stop {self.facebook_package}")
        self.task.ld.execute_ld(self.emulator_id, f"killall {self.facebook_package}")
        
        await asyncio.sleep(3)
        print("✅ Facebook应用已强制停止")

    async def _modify_system_verification_state(self):
        """修改系统级验证状态"""
        print("🔧 修改系统级验证状态...")
        
        # 修改系统属性
        system_props = [
            "setprop facebook.sms.verified true",
            "setprop facebook.2fa.enabled true", 
            "setprop facebook.verification.bypass true"
        ]
        
        for prop in system_props:
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c '{prop}'")
            if success:
                print(f"   ✅ {prop}")
        
        print("✅ 系统级验证状态修改完成")

    async def _inject_bypass_data(self):
        """注入绕过数据"""
        print("💉 注入绕过数据...")
        
        facebook_data = f"/data/data/{self.facebook_package}"
        
        # 创建强制绕过标志
        bypass_commands = [
            f"su -c 'mkdir -p {facebook_data}/files/security'",
            f"su -c 'echo \"verified\" > {facebook_data}/files/security/sms_status'",
            f"su -c 'echo \"true\" > {facebook_data}/files/security/bypass_enabled'",
            f"su -c 'echo \"{int(time.time())}\" > {facebook_data}/files/security/verification_time'",
            f"su -c 'chmod 777 {facebook_data}/files/security/*'"
        ]
        
        for cmd in bypass_commands:
            success, result = self.task.ld.execute_ld(self.emulator_id, cmd)
            if success:
                print("   ✅ 绕过数据注入成功")
        
        print("✅ 绕过数据注入完成")

    async def _modify_network_requests(self):
        """修改网络请求"""
        print("🌐 修改网络请求...")
        
        # 创建本地代理规则
        proxy_rules = [
            "su -c 'iptables -t nat -A OUTPUT -p tcp --dport 443 -j REDIRECT --to-port 8080'",
            "su -c 'echo \"127.0.0.1 graph.facebook.com\" >> /system/etc/hosts'",
            "su -c 'echo \"127.0.0.1 api.facebook.com\" >> /system/etc/hosts'"
        ]
        
        for rule in proxy_rules:
            self.task.ld.execute_ld(self.emulator_id, rule)
        
        print("✅ 网络请求修改完成")

    async def _restart_and_jump_to_2fa(self):
        """重启应用并跳转到2FA"""
        print("🚀 重启应用并跳转到2FA...")
        
        # 启动Facebook并直接跳转到2FA设置
        jump_commands = [
            f"am start -n {self.facebook_package}/.LoginActivity",
            "sleep 3",
            f"am start -a android.intent.action.VIEW -d 'https://m.facebook.com/settings/security/two_factor/'",
            "sleep 2", 
            f"am start -a android.intent.action.VIEW -d 'fb://settings/security/two_factor/'"
        ]
        
        for cmd in jump_commands:
            if "sleep" in cmd:
                await asyncio.sleep(int(cmd.split()[1]))
            else:
                success, result = self.task.ld.execute_ld(self.emulator_id, cmd)
                if success:
                    print(f"   ✅ {cmd.split()[-1]}")
        
        print("✅ 应用重启和跳转完成")

async def main():
    """主函数"""
    try:
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2
        
        print("🔓 强制绕过SMS验证工具")
        print("⚠️  这是最强力的绕过方法")
        print("⚠️  会深度修改系统状态")
        print("⚠️  仅在其他方法都失败时使用")
        print()
        
        confirm = input("确认执行强制绕过? (输入 'FORCE' 继续): ").strip()
        if confirm != 'FORCE':
            print("❌ 操作取消")
            return
        
        bypass_tool = ForceSMSBypass(emulator_id)
        await bypass_tool.force_bypass()
        
        print("\n" + "=" * 50)
        print("✅ 强制绕过完成")
        print("💡 现在尝试在Facebook中访问2FA设置")
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
