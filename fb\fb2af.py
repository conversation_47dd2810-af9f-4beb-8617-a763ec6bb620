#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度TOTP密钥搜索器
专门搜索加密或隐藏的TOTP密钥
基于之前成功找到Z34OJGIQ5SOTPXV2SR2AZI3IXM的经验
"""

import os
import sys
import json
import re
import base64
import binascii
from datetime import datetime

# 添加雷电API路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from 雷电API import Dnconsole

class DeepTOTPSearcher:
    def __init__(self, emulator_index=11):
        """初始化深度TOTP密钥搜索器"""
        try:
            # 雷电模拟器路径配置
            base_paths = [
                r"G:\leidian\LDPlayer9",
                r"G:\LDPlayer\LDPlayer9", 
                r"C:\LDPlayer\LDPlayer9",
                r"D:\LDPlayer\LDPlayer9",
                r"E:\LDPlayer\LDPlayer9"
            ]
            
            self.base_path = None
            for path in base_paths:
                if os.path.exists(os.path.join(path, "ld.exe")):
                    self.base_path = path
                    break
            
            if not self.base_path:
                raise FileNotFoundError("未找到雷电模拟器安装路径")
            
            self.share_path = os.path.expanduser("~/Documents/leidian64")
            self.emulator_index = emulator_index
            
            # 初始化雷电控制台
            self.ld = Dnconsole(
                base_path=self.base_path,
                share_path=self.share_path,
                emulator_id=emulator_index
            )
            
            print(f"🔍 深度TOTP密钥搜索器初始化成功")
            print(f"📱 目标模拟器: {emulator_index}")
            print(f"🎯 目标: 寻找类似 Z34OJGIQ5SOTPXV2SR2AZI3IXM 的26字符TOTP密钥")
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    def execute_command_safe(self, command):
        """安全执行命令"""
        try:
            success, output = self.ld.execute_ld(self.emulator_index, command)
            return output if success else None
        except Exception as e:
            return None
    
    def search_all_facebook_locations(self):
        """搜索所有可能的Facebook位置"""
        print("\n🔍 搜索所有Facebook相关位置...")
        print("=" * 60)
        
        # 扩展的搜索路径
        search_locations = [
            # 标准应用数据路径
            "/data/data/com.facebook.katana/",
            "/data/user/0/com.facebook.katana/",
            "/data/user_de/0/com.facebook.katana/",
            
            # 外部存储路径
            "/storage/emulated/0/Android/data/com.facebook.katana/",
            "/sdcard/Android/data/com.facebook.katana/",
            "/mnt/sdcard/Android/data/com.facebook.katana/",
            
            # 系统缓存路径
            "/data/system/users/0/",
            "/data/misc/user/0/",
            
            # 验证器应用路径
            "/data/data/com.google.android.apps.authenticator2/",
            "/data/data/com.authy.authy/",
            "/data/data/org.fedorahosted.freeotp/",
            "/data/data/com.azure.authenticator/",
            
            # 系统级存储
            "/data/system/",
            "/data/misc/",
        ]
        
        found_locations = []
        
        for location in search_locations:
            print(f"\n📁 检查位置: {location}")
            
            # 检查目录是否存在
            check_result = self.execute_command_safe(f"ls -la {location} 2>/dev/null")
            if check_result and "No such file" not in check_result:
                print(f"   ✅ 位置存在")
                found_locations.append(location)
                
                # 递归搜索所有文件
                files_result = self.execute_command_safe(f"find {location} -type f 2>/dev/null")
                if files_result:
                    files = [f.strip() for f in files_result.split('\n') if f.strip()]
                    print(f"   📊 发现 {len(files)} 个文件")
                    
                    # 搜索每个文件中的TOTP密钥
                    self.search_files_for_totp(files[:100])  # 限制文件数量
            else:
                print(f"   ❌ 位置不存在或无权限")
        
        return found_locations
    
    def search_files_for_totp(self, file_list):
        """在文件列表中搜索TOTP密钥"""
        for file_path in file_list:
            try:
                file_name = os.path.basename(file_path)
                
                # 跳过明显的二进制文件
                if any(ext in file_name.lower() for ext in ['.so', '.dex', '.apk', '.png', '.jpg', '.gif', '.mp4']):
                    continue
                
                print(f"      📄 搜索文件: {file_name}")
                
                # 读取文件内容
                content = self.execute_command_safe(f"cat '{file_path}' 2>/dev/null")
                if content and len(content) > 10:
                    # 搜索TOTP密钥
                    found_keys = self.extract_totp_keys_from_content(content, file_name)
                    if found_keys:
                        print(f"         🎉 在 {file_name} 中找到 {len(found_keys)} 个TOTP密钥!")
                        for key_info in found_keys:
                            print(f"            🔑 {key_info['key']} ({key_info['length']}字符)")
            
            except Exception as e:
                continue
    
    def extract_totp_keys_from_content(self, content, source_file):
        """从内容中提取TOTP密钥"""
        found_keys = []
        
        # 标准Base32 TOTP密钥模式 (严格匹配)
        totp_patterns = [
            # 26字符 - 最常见的标准长度
            (r'\b[A-Z2-7]{26}\b', 'TOTP_26_STANDARD'),
            # 32字符 - 另一种常见长度
            (r'\b[A-Z2-7]{32}\b', 'TOTP_32_STANDARD'),
            # 其他可能的长度
            (r'\b[A-Z2-7]{20}\b', 'TOTP_20_STANDARD'),
            (r'\b[A-Z2-7]{24}\b', 'TOTP_24_STANDARD'),
            (r'\b[A-Z2-7]{16}\b', 'TOTP_16_STANDARD'),
        ]
        
        for pattern, key_type in totp_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if self.is_valid_totp_key(match):
                    key_info = {
                        'key': match,
                        'length': len(match),
                        'type': key_type,
                        'source': source_file,
                        'formatted': self.format_totp_key(match),
                        'validation': self.validate_totp_key_detailed(match)
                    }
                    found_keys.append(key_info)
        
        return found_keys
    
    def is_valid_totp_key(self, key):
        """验证是否为有效的TOTP密钥"""
        try:
            # 1. 检查字符集 (Base32: A-Z, 2-7)
            valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
            if not set(key.upper()).issubset(valid_chars):
                return False
            
            # 2. 检查长度
            if len(key) < 16 or len(key) > 64:
                return False
            
            # 3. 排除明显的英文单词组合
            common_words = [
                'FACEBOOK', 'GOOGLE', 'TWITTER', 'INSTAGRAM', 'ANDROID', 'SYSTEM',
                'AMBIENT', 'DIFFUSE', 'STATIC', 'ENVIRONMENT', 'TEXTURE', 'ROTATION',
                'FACTOR', 'SHADING', 'PARAMS', 'FAVORITE', 'MESSENGER', 'CONTACT'
            ]
            
            key_upper = key.upper()
            for word in common_words:
                if word in key_upper:
                    return False
            
            # 4. 检查是否包含连续重复字符 (真正的密钥应该是随机的)
            for i in range(len(key) - 3):
                if key[i] == key[i+1] == key[i+2] == key[i+3]:
                    return False
            
            # 5. 尝试Base32解码
            padded_key = key + '=' * (8 - len(key) % 8) % 8
            decoded = base64.b32decode(padded_key)
            
            # 6. 检查解码后的长度 (TOTP密钥通常是10-20字节)
            if len(decoded) < 10 or len(decoded) > 32:
                return False
            
            # 7. 检查解码后的熵值 (随机性)
            entropy = self.calculate_entropy(decoded)
            if entropy < 3.0:  # 熵值太低说明不够随机
                return False
            
            return True
            
        except Exception:
            return False
    
    def calculate_entropy(self, data):
        """计算数据的熵值"""
        try:
            from collections import Counter
            import math
            
            # 计算字节频率
            byte_counts = Counter(data)
            data_len = len(data)
            
            # 计算熵值
            entropy = 0
            for count in byte_counts.values():
                p = count / data_len
                if p > 0:
                    entropy -= p * math.log2(p)
            
            return entropy
        except:
            return 0
    
    def format_totp_key(self, key):
        """格式化TOTP密钥"""
        # 每4个字符一组，用空格分隔
        return ' '.join([key[i:i+4] for i in range(0, len(key), 4)])
    
    def validate_totp_key_detailed(self, key):
        """详细验证TOTP密钥"""
        validation = {
            'is_valid': False,
            'decoded_length': 0,
            'entropy': 0,
            'hex_decoded': '',
            'character_distribution': {}
        }
        
        try:
            # Base32解码
            padded_key = key + '=' * (8 - len(key) % 8) % 8
            decoded = base64.b32decode(padded_key)
            
            validation['is_valid'] = True
            validation['decoded_length'] = len(decoded)
            validation['entropy'] = self.calculate_entropy(decoded)
            validation['hex_decoded'] = decoded.hex()
            
            # 字符分布统计
            from collections import Counter
            char_counts = Counter(key)
            validation['character_distribution'] = dict(char_counts)
            
        except Exception as e:
            validation['error'] = str(e)
        
        return validation
    
    def search_encrypted_data(self):
        """搜索可能加密的数据"""
        print("\n🔐 搜索可能加密的TOTP数据...")
        print("=" * 60)
        
        # 搜索可能包含加密TOTP数据的文件
        encrypted_search_commands = [
            # 搜索包含Base64编码的文件
            "find /data/data/com.facebook.katana/ -type f -exec grep -l '[A-Za-z0-9+/]\\{20,\\}=' {} \\; 2>/dev/null",
            
            # 搜索包含十六进制数据的文件
            "find /data/data/com.facebook.katana/ -type f -exec grep -l '[a-fA-F0-9]\\{32,\\}' {} \\; 2>/dev/null",
            
            # 搜索数据库文件
            "find /data/data/com.facebook.katana/ -name '*.db' -o -name '*.sqlite' -o -name '*.sqlite3' 2>/dev/null",
            
            # 搜索包含"secret"或"key"的文件
            "find /data/data/com.facebook.katana/ -type f -exec grep -l -i 'secret\\|key\\|totp\\|otp' {} \\; 2>/dev/null",
        ]
        
        for cmd in encrypted_search_commands:
            print(f"\n🔍 执行搜索: {cmd.split('find')[1].split('-exec')[0]}...")
            result = self.execute_command_safe(cmd)
            
            if result:
                files = [f.strip() for f in result.split('\n') if f.strip()]
                print(f"   找到 {len(files)} 个可能的文件")
                
                for file_path in files[:10]:  # 限制文件数量
                    print(f"      📄 {os.path.basename(file_path)}")
                    
                    # 尝试提取内容
                    content = self.execute_command_safe(f"cat '{file_path}' 2>/dev/null")
                    if content:
                        # 尝试解码可能的加密数据
                        self.try_decode_encrypted_data(content, os.path.basename(file_path))
    
    def try_decode_encrypted_data(self, content, source_file):
        """尝试解码加密数据"""
        # 搜索Base64编码的数据
        base64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
        base64_matches = re.findall(base64_pattern, content)
        
        for match in base64_matches:
            try:
                decoded = base64.b64decode(match)
                decoded_str = decoded.decode('utf-8', errors='ignore')
                
                # 在解码后的数据中搜索TOTP密钥
                found_keys = self.extract_totp_keys_from_content(decoded_str, f"{source_file}(base64)")
                if found_keys:
                    print(f"         🎉 在Base64解码数据中找到TOTP密钥!")
                    for key_info in found_keys:
                        print(f"            🔑 {key_info['key']}")
            except:
                continue
        
        # 搜索十六进制编码的数据
        hex_pattern = r'[a-fA-F0-9]{32,}'
        hex_matches = re.findall(hex_pattern, content)
        
        for match in hex_matches:
            try:
                decoded = binascii.unhexlify(match)
                decoded_str = decoded.decode('utf-8', errors='ignore')
                
                # 在解码后的数据中搜索TOTP密钥
                found_keys = self.extract_totp_keys_from_content(decoded_str, f"{source_file}(hex)")
                if found_keys:
                    print(f"         🎉 在十六进制解码数据中找到TOTP密钥!")
                    for key_info in found_keys:
                        print(f"            🔑 {key_info['key']}")
            except:
                continue
    
    def save_search_results(self, all_found_keys):
        """保存搜索结果"""
        try:
            # 创建结果目录
            os.makedirs('facebook_2fa_results', exist_ok=True)
            
            # 整理数据
            result_data = {
                'search_time': datetime.now().isoformat(),
                'emulator_index': self.emulator_index,
                'target_pattern': 'Z34OJGIQ5SOTPXV2SR2AZI3IXM (26字符Base32)',
                'found_totp_keys': all_found_keys,
                'summary': {
                    'total_keys': len(all_found_keys),
                    'by_length': {},
                    'by_source': {}
                }
            }
            
            # 统计分析
            for key_info in all_found_keys:
                length = str(key_info['length'])
                source = key_info['source']
                
                result_data['summary']['by_length'][length] = result_data['summary']['by_length'].get(length, 0) + 1
                result_data['summary']['by_source'][source] = result_data['summary']['by_source'].get(source, 0) + 1
            
            # 保存到文件
            filename = f"facebook_2fa_results/deep_totp_search_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ 搜索结果已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return None
    
    def run_deep_search(self):
        """运行深度搜索"""
        print("🔍 深度TOTP密钥搜索器")
        print("专门搜索类似 Z34OJGIQ5SOTPXV2SR2AZI3IXM 的26字符TOTP密钥")
        print("=" * 60)
        
        try:
            all_found_keys = []
            
            # 1. 搜索所有Facebook位置
            found_locations = self.search_all_facebook_locations()
            
            # 2. 搜索加密数据
            self.search_encrypted_data()
            
            # 3. 显示搜索结果
            if all_found_keys:
                print(f"\n🎉 找到 {len(all_found_keys)} 个TOTP密钥!")
                for i, key_info in enumerate(all_found_keys, 1):
                    print(f"\n🔑 密钥 #{i}:")
                    print(f"   密钥: {key_info['key']}")
                    print(f"   长度: {key_info['length']} 字符")
                    print(f"   格式化: {key_info['formatted']}")
                    print(f"   来源: {key_info['source']}")
                    print(f"   熵值: {key_info['validation']['entropy']:.2f}")
            else:
                print(f"\n❌ 未找到标准TOTP密钥")
                print(f"💡 可能的原因:")
                print(f"   1. TOTP密钥被加密存储")
                print(f"   2. 存储在其他应用中")
                print(f"   3. 使用了非标准格式")
                print(f"   4. Facebook账号未启用验证器应用2FA")
            
            # 4. 保存结果
            result_file = self.save_search_results(all_found_keys)
            
            if result_file:
                print(f"\n🎉 深度TOTP搜索完成!")
                return True
            else:
                print(f"\n❌ 保存结果失败")
                return False
                
        except Exception as e:
            print(f"❌ 搜索流程失败: {e}")
            return False

def main():
    """主函数"""
    print("🔍 深度TOTP密钥搜索器")
    print("=" * 40)
    
    try:
        # 创建搜索器实例
        searcher = DeepTOTPSearcher(emulator_index=11)
        
        # 运行深度搜索
        success = searcher.run_deep_search()
        
        if success:
            print("\n🎉 深度TOTP搜索成功！")
            print("📁 请查看facebook_2fa_results目录中的结果文件")
        else:
            print("\n❌ 深度TOTP搜索失败")
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
