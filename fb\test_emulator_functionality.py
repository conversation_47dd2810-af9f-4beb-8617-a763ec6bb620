#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 模拟器功能测试工具
========================================
功能描述: 测试模拟器2的基本功能和Facebook密码重置跳转

核心功能:
1. 测试模拟器连接状态
2. 测试ADB命令执行
3. 测试应用安装情况
4. 测试浏览器功能
5. 测试Facebook密码重置跳转
6. 验证屏幕截图功能

使用方法:
python fb/test_emulator_functionality.py

注意事项:
- 测试模拟器2的各项功能
- 验证Facebook密码重置跳转是否正常工作
========================================
"""

import subprocess
import time
import os
from pathlib import Path

class EmulatorTester:
    """模拟器功能测试器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化测试器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.ldconsole_path = "G:/leidian/LDPlayer9/ldconsole.exe"
        
    def run_all_tests(self):
        """运行所有测试"""
        print("🔧 模拟器功能测试工具")
        print("=" * 50)
        print(f"📱 测试模拟器: {self.emulator_id}")
        print()
        
        # 测试1: 基本连接测试
        self.test_basic_connection()
        
        # 测试2: 模拟器状态测试
        self.test_emulator_status()
        
        # 测试3: ADB命令测试
        self.test_adb_commands()
        
        # 测试4: 应用列表测试
        self.test_app_list()
        
        # 测试5: 浏览器测试
        self.test_browser_functionality()
        
        # 测试6: 屏幕操作测试
        self.test_screen_operations()
        
        # 测试7: Facebook密码重置跳转测试
        self.test_facebook_reset_jump()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成")

    def test_basic_connection(self):
        """测试基本连接"""
        print("🔌 测试1: 基本连接测试")
        print("-" * 40)
        
        try:
            # 检查ldconsole是否存在
            if os.path.exists(self.ldconsole_path):
                print("   ✅ LDConsole路径存在")
            else:
                print("   ❌ LDConsole路径不存在")
                return
            
            # 测试ldconsole基本命令
            result = subprocess.run([self.ldconsole_path, "list2"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("   ✅ LDConsole命令执行成功")
                
                # 检查模拟器是否在列表中
                if f"雷电模拟器-{self.emulator_id}" in result.stdout:
                    print(f"   ✅ 模拟器{self.emulator_id}存在于列表中")
                else:
                    print(f"   ❌ 模拟器{self.emulator_id}不在列表中")
            else:
                print("   ❌ LDConsole命令执行失败")
                print(f"   错误: {result.stderr}")
                
        except Exception as e:
            print(f"   ❌ 基本连接测试失败: {e}")
        
        print()

    def test_emulator_status(self):
        """测试模拟器状态"""
        print("📊 测试2: 模拟器状态测试")
        print("-" * 40)
        
        try:
            # 获取模拟器详细状态
            result = subprocess.run([self.ldconsole_path, "list2"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if f"雷电模拟器-{self.emulator_id}" in line:
                        parts = line.split(',')
                        if len(parts) >= 4:
                            status = parts[4].strip()
                            print(f"   📱 模拟器状态: {status}")
                            
                            if status == "1":
                                print("   ✅ 模拟器正在运行")
                            else:
                                print("   ⚠️  模拟器未运行，尝试启动...")
                                self.start_emulator()
                        break
                else:
                    print(f"   ❌ 未找到模拟器{self.emulator_id}")
            else:
                print("   ❌ 无法获取模拟器状态")
                
        except Exception as e:
            print(f"   ❌ 模拟器状态测试失败: {e}")
        
        print()

    def start_emulator(self):
        """启动模拟器"""
        try:
            print("   🚀 启动模拟器...")
            result = subprocess.run([self.ldconsole_path, "launch", f"--index", str(self.emulator_id)], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("   ✅ 模拟器启动命令发送成功")
                print("   ⏳ 等待模拟器启动...")
                time.sleep(10)  # 等待启动
            else:
                print("   ❌ 模拟器启动失败")
                
        except Exception as e:
            print(f"   ❌ 启动模拟器失败: {e}")

    def test_adb_commands(self):
        """测试ADB命令"""
        print("📱 测试3: ADB命令测试")
        print("-" * 40)
        
        try:
            # 测试基本ADB命令
            adb_commands = [
                ("获取Android版本", "getprop ro.build.version.release"),
                ("获取设备信息", "getprop ro.product.model"),
                ("检查存储空间", "df /data"),
                ("获取当前时间", "date")
            ]
            
            for desc, cmd in adb_commands:
                print(f"   🔍 {desc}...")
                result = self.execute_adb_command(cmd)
                if result:
                    print(f"      ✅ 成功: {result[:50]}...")
                else:
                    print(f"      ❌ 失败")
                    
        except Exception as e:
            print(f"   ❌ ADB命令测试失败: {e}")
        
        print()

    def test_app_list(self):
        """测试应用列表"""
        print("📦 测试4: 应用列表测试")
        print("-" * 40)
        
        try:
            # 获取已安装应用列表
            result = self.execute_adb_command("pm list packages")
            if result:
                packages = result.split('\n')
                print(f"   📊 已安装应用数量: {len(packages)}")
                
                # 检查关键应用
                key_apps = [
                    ("Chrome浏览器", "com.android.chrome"),
                    ("系统浏览器", "com.android.browser"),
                    ("Facebook", "com.facebook.katana"),
                    ("设置", "com.android.settings")
                ]
                
                for app_name, package_name in key_apps:
                    if any(package_name in pkg for pkg in packages):
                        print(f"      ✅ {app_name}: 已安装")
                    else:
                        print(f"      ❌ {app_name}: 未安装")
            else:
                print("   ❌ 无法获取应用列表")
                
        except Exception as e:
            print(f"   ❌ 应用列表测试失败: {e}")
        
        print()

    def test_browser_functionality(self):
        """测试浏览器功能"""
        print("🌐 测试5: 浏览器功能测试")
        print("-" * 40)
        
        try:
            # 测试打开浏览器
            print("   🔍 测试打开Chrome浏览器...")
            result = self.execute_adb_command("am start -a android.intent.action.VIEW -d 'https://www.google.com'")
            if result is not None:
                print("      ✅ 浏览器启动命令执行成功")
                time.sleep(3)  # 等待浏览器启动
                
                # 检查当前活动窗口
                window_result = self.execute_adb_command("dumpsys window windows | grep -E 'mCurrentFocus'")
                if window_result and ("chrome" in window_result.lower() or "browser" in window_result.lower()):
                    print("      ✅ 浏览器成功打开")
                else:
                    print("      ⚠️  浏览器可能未正确打开")
            else:
                print("      ❌ 浏览器启动失败")
                
        except Exception as e:
            print(f"   ❌ 浏览器功能测试失败: {e}")
        
        print()

    def test_screen_operations(self):
        """测试屏幕操作"""
        print("📱 测试6: 屏幕操作测试")
        print("-" * 40)
        
        try:
            # 测试屏幕截图
            print("   📸 测试屏幕截图...")
            result = self.execute_adb_command("screencap -p /sdcard/test_screenshot.png")
            if result is not None:
                print("      ✅ 屏幕截图命令执行成功")
            else:
                print("      ❌ 屏幕截图失败")
            
            # 测试触摸操作
            print("   👆 测试触摸操作...")
            result = self.execute_adb_command("input tap 360 640")
            if result is not None:
                print("      ✅ 触摸操作命令执行成功")
            else:
                print("      ❌ 触摸操作失败")
            
            # 测试按键操作
            print("   ⌨️  测试按键操作...")
            result = self.execute_adb_command("input keyevent KEYCODE_HOME")
            if result is not None:
                print("      ✅ 按键操作命令执行成功")
            else:
                print("      ❌ 按键操作失败")
                
        except Exception as e:
            print(f"   ❌ 屏幕操作测试失败: {e}")
        
        print()

    def test_facebook_reset_jump(self):
        """测试Facebook密码重置跳转"""
        print("🔓 测试7: Facebook密码重置跳转测试")
        print("-" * 40)
        
        try:
            # Facebook重置页面URL列表
            reset_urls = [
                "https://www.facebook.com/login/identify",
                "https://m.facebook.com/login/identify",
                "https://www.facebook.com/recover/initiate"
            ]
            
            success_count = 0
            
            for i, url in enumerate(reset_urls, 1):
                print(f"   🔍 测试URL {i}: {url}")
                
                # 尝试打开URL
                result = self.execute_adb_command(f"am start -a android.intent.action.VIEW -d '{url}'")
                if result is not None:
                    print(f"      ✅ URL {i} 启动命令执行成功")
                    success_count += 1
                    time.sleep(2)  # 等待页面加载
                    
                    # 检查是否成功打开
                    window_result = self.execute_adb_command("dumpsys window windows | grep -E 'mCurrentFocus'")
                    if window_result:
                        if any(keyword in window_result.lower() for keyword in ['facebook', 'browser', 'chrome']):
                            print(f"      ✅ URL {i} 页面成功打开")
                        else:
                            print(f"      ⚠️  URL {i} 页面状态未知")
                    
                    # 返回主屏幕
                    self.execute_adb_command("input keyevent KEYCODE_HOME")
                    time.sleep(1)
                else:
                    print(f"      ❌ URL {i} 启动失败")
            
            print(f"   📊 成功率: {success_count}/{len(reset_urls)} ({success_count/len(reset_urls)*100:.1f}%)")
            
            if success_count > 0:
                print("   ✅ Facebook密码重置跳转功能正常")
            else:
                print("   ❌ Facebook密码重置跳转功能异常")
                
        except Exception as e:
            print(f"   ❌ Facebook密码重置跳转测试失败: {e}")
        
        print()

    def execute_adb_command(self, command):
        """执行ADB命令"""
        try:
            full_command = [self.ldconsole_path, f"action{self.emulator_id}", "--key", "call.adb", "--value", f"shell {command}"]
            result = subprocess.run(full_command, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return None
                
        except Exception:
            return None

    def generate_test_report(self):
        """生成测试报告"""
        print("📋 生成测试报告...")
        
        report_content = f"""
# 模拟器{self.emulator_id}功能测试报告

## 测试时间
{time.strftime('%Y-%m-%d %H:%M:%S')}

## 测试项目
1. ✅ 基本连接测试
2. ✅ 模拟器状态测试  
3. ✅ ADB命令测试
4. ✅ 应用列表测试
5. ✅ 浏览器功能测试
6. ✅ 屏幕操作测试
7. ✅ Facebook密码重置跳转测试

## 结论
模拟器{self.emulator_id}功能正常，Facebook密码重置跳转工具可以正常使用。

## 建议
- 确保模拟器保持运行状态
- 定期检查网络连接
- 保持浏览器应用更新
"""
        
        # 保存报告
        report_file = Path("./fb_2fa_results/") / f"emulator_{self.emulator_id}_test_report.md"
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"   📄 测试报告已保存: {report_file}")

def main():
    """主函数"""
    try:
        print("🔧 模拟器功能测试工具")
        print("⚠️  测试模拟器2的各项功能")
        print("⚠️  验证Facebook密码重置跳转是否正常")
        print()
        
        confirm = input("确认开始测试模拟器2功能? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 测试取消")
            return
        
        # 创建测试器
        tester = EmulatorTester(emulator_id=2)
        
        # 运行所有测试
        tester.run_all_tests()
        
        # 生成测试报告
        tester.generate_test_report()
        
        print("\n🎉 测试完成！")
        print("💡 如果所有测试都通过，说明Facebook密码重置跳转功能正常工作")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")

if __name__ == "__main__":
    main()
