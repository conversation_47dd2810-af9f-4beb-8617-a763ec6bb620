#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 绕过短信验证设置TOTP 2FA工具
========================================
功能描述: 绕过Facebook短信验证，直接设置TOTP认证应用

核心策略:
1. 直接修改Facebook应用的本地数据
2. 模拟已通过短信验证的状态
3. 强制启用TOTP认证应用功能
4. 绕过服务器端验证检查
5. 直接生成TOTP密钥

使用方法:
python fb/bypass_sms_2fa_setup.py [emulator_id]

注意事项:
- 需要root权限
- 会修改Facebook应用数据
- 仅用于合法的账户恢复目的
========================================
"""

import asyncio
import sys
import re
import json
import base64
import tempfile
import struct
import hashlib
import time
import secrets
import string
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class BypassSMS2FASetup:
    """绕过短信验证设置TOTP 2FA"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化绕过工具
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.temp_dir = Path(tempfile.gettempdir()) / f"bypass_sms_2fa_{emulator_id}"
        self.temp_dir.mkdir(exist_ok=True)
        
        # Facebook应用信息
        self.facebook_package = "com.facebook.katana"
        self.facebook_data_path = f"/data/data/{self.facebook_package}"
        
        log_info(f"[绕过SMS] 绕过SMS 2FA设置工具初始化 - 模拟器{emulator_id}", component="BypassSMS")

    async def run_bypass_setup(self):
        """运行绕过设置流程"""
        try:
            log_info(f"[绕过SMS] 开始绕过SMS验证设置TOTP", component="BypassSMS")
            
            print("🔓 绕过短信验证设置TOTP 2FA工具")
            print("=" * 50)
            print("⚠️  绕过Facebook短信验证限制")
            print("⚠️  直接设置TOTP认证应用")
            print("⚠️  需要root权限")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：验证环境
            if not await self._verify_environment():
                return
            
            # 第二步：备份原始数据
            await self._backup_original_data()
            
            # 第三步：停止Facebook应用
            await self._stop_facebook_app()
            
            # 第四步：修改应用数据绕过SMS验证
            await self._bypass_sms_verification()
            
            # 第五步：注入TOTP设置数据
            await self._inject_totp_setup_data()
            
            # 第六步：生成测试TOTP密钥
            test_secret = await self._generate_test_totp_secret()
            
            # 第七步：重启Facebook应用
            await self._restart_facebook_app()
            
            # 第八步：验证绕过效果
            await self._verify_bypass_result(test_secret)
            
            # 第九步：提取生成的TOTP密钥
            extracted_secrets = await self._extract_generated_secrets()
            
            # 第十步：显示结果和指导
            await self._display_bypass_results(extracted_secrets, test_secret)
            
            log_info(f"[绕过SMS] 绕过SMS验证设置完成", component="BypassSMS")
            
        except Exception as e:
            log_error(f"[绕过SMS] 绕过设置失败: {e}", component="BypassSMS")
        finally:
            await self._cleanup()

    async def _verify_environment(self) -> bool:
        """验证环境"""
        try:
            print("🔧 验证环境...")
            print("-" * 40)
            
            # 检查root权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'whoami'")
            if not success or "root" not in result:
                print("❌ Root权限不可用")
                return False
            
            print("✅ Root权限可用")
            
            # 检查Facebook应用
            success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages | grep {self.facebook_package}")
            if not success or self.facebook_package not in result:
                print("❌ Facebook应用未安装")
                return False
            
            print("✅ Facebook应用已安装")
            
            # 检查应用数据目录
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'ls -la {self.facebook_data_path}'")
            if not success or "No such file" in result:
                print("❌ Facebook应用数据目录不存在")
                return False
            
            print("✅ Facebook应用数据目录存在")
            
            print("✅ 环境验证完成")
            print()
            return True
            
        except Exception as e:
            log_error(f"[绕过SMS] 环境验证失败: {e}", component="BypassSMS")
            return False

    async def _backup_original_data(self):
        """备份原始数据"""
        try:
            print("💾 备份原始数据...")
            print("-" * 40)
            
            backup_dir = "/sdcard/facebook_backup"
            self.task.ld.execute_ld(self.emulator_id, f"mkdir -p {backup_dir}")
            
            # 备份关键目录
            backup_paths = [
                f"{self.facebook_data_path}/databases",
                f"{self.facebook_data_path}/shared_prefs",
                f"{self.facebook_data_path}/files"
            ]
            
            for path in backup_paths:
                path_name = path.split('/')[-1]
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'cp -r {path} {backup_dir}/{path_name}_backup 2>/dev/null'")
                if success:
                    print(f"✅ 备份完成: {path_name}")
                else:
                    print(f"⚠️  备份失败: {path_name}")
            
            print("✅ 数据备份完成")
            print()
            
        except Exception as e:
            log_error(f"[绕过SMS] 数据备份失败: {e}", component="BypassSMS")

    async def _stop_facebook_app(self):
        """停止Facebook应用"""
        try:
            print("🛑 停止Facebook应用...")
            print("-" * 40)
            
            # 强制停止应用
            success, result = self.task.ld.execute_ld(self.emulator_id, f"am force-stop {self.facebook_package}")
            if success:
                print("✅ Facebook应用已停止")
            
            # 等待进程完全停止
            await asyncio.sleep(3)
            
            # 验证是否已停止
            success, result = self.task.ld.execute_ld(self.emulator_id, f"ps | grep {self.facebook_package}")
            if not success or not result.strip():
                print("✅ 确认应用已完全停止")
            else:
                print("⚠️  应用可能仍在运行")
            
            print()
            
        except Exception as e:
            log_error(f"[绕过SMS] 停止应用失败: {e}", component="BypassSMS")

    async def _bypass_sms_verification(self):
        """绕过SMS验证"""
        try:
            print("🔓 绕过SMS验证...")
            print("-" * 40)
            
            # 方法1: 修改SharedPreferences
            await self._modify_shared_preferences()
            
            # 方法2: 修改数据库
            await self._modify_databases()
            
            # 方法3: 注入绕过标志
            await self._inject_bypass_flags()
            
            print("✅ SMS验证绕过完成")
            print()
            
        except Exception as e:
            log_error(f"[绕过SMS] SMS验证绕过失败: {e}", component="BypassSMS")

    async def _modify_shared_preferences(self):
        """修改SharedPreferences"""
        try:
            print("📋 修改SharedPreferences...")
            
            prefs_dir = f"{self.facebook_data_path}/shared_prefs"
            
            # 创建绕过SMS验证的配置
            bypass_config = '''<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<map>
    <boolean name="sms_verification_bypassed" value="true" />
    <boolean name="two_factor_setup_allowed" value="true" />
    <boolean name="totp_setup_enabled" value="true" />
    <string name="verification_status">verified</string>
    <long name="last_verification_time" value="{}" />
    <boolean name="phone_verification_required" value="false" />
    <boolean name="authenticator_app_enabled" value="true" />
</map>'''.format(int(time.time() * 1000))
            
            # 写入绕过配置文件
            config_file = f"{prefs_dir}/security_bypass.xml"
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'echo \"{bypass_config}\" > {config_file}'")
            if success:
                print("   ✅ 绕过配置文件已创建")
            
            # 修改现有的安全配置文件
            security_files = [
                "security_preferences.xml",
                "two_factor_preferences.xml",
                "account_preferences.xml"
            ]
            
            for file_name in security_files:
                file_path = f"{prefs_dir}/{file_name}"
                
                # 检查文件是否存在
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'ls -la {file_path}'")
                if success and "No such file" not in result:
                    print(f"   🔍 修改现有文件: {file_name}")
                    
                    # 读取现有内容
                    success2, content = self.task.ld.execute_ld(self.emulator_id, f"su -c 'cat {file_path}'")
                    if success2 and content.strip():
                        # 在现有内容中注入绕过标志
                        modified_content = content.replace('</map>', 
                            '    <boolean name="sms_verification_bypassed" value="true" />\n'
                            '    <boolean name="totp_setup_enabled" value="true" />\n'
                            '</map>')
                        
                        # 写回修改后的内容
                        success3, result3 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'echo \"{modified_content}\" > {file_path}'")
                        if success3:
                            print(f"      ✅ {file_name} 修改成功")
                else:
                    print(f"   ⚠️  {file_name} 不存在，跳过")
            
        except Exception as e:
            log_error(f"[绕过SMS] SharedPreferences修改失败: {e}", component="BypassSMS")

    async def _modify_databases(self):
        """修改数据库"""
        try:
            print("📊 修改数据库...")
            
            db_dir = f"{self.facebook_data_path}/databases"
            
            # 查找所有数据库文件
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {db_dir} -name \"*.db\" 2>/dev/null'")
            if success and result.strip():
                db_files = result.strip().split('\n')
                print(f"   📊 找到 {len(db_files)} 个数据库文件")
                
                for db_file in db_files:
                    if db_file.strip():
                        db_name = db_file.split('/')[-1]
                        print(f"   🔍 修改数据库: {db_name}")
                        
                        # 尝试在数据库中插入绕过记录
                        bypass_sql_commands = [
                            "INSERT OR REPLACE INTO security_settings (key, value) VALUES ('sms_verification_bypassed', 'true');",
                            "INSERT OR REPLACE INTO user_preferences (key, value) VALUES ('totp_setup_enabled', 'true');",
                            "INSERT OR REPLACE INTO verification_status (status, timestamp) VALUES ('verified', {});".format(int(time.time())),
                            "UPDATE security_settings SET value='true' WHERE key='two_factor_enabled';",
                            "UPDATE user_preferences SET value='false' WHERE key='phone_verification_required';"
                        ]
                        
                        for sql_cmd in bypass_sql_commands:
                            success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'sqlite3 {db_file} \"{sql_cmd}\" 2>/dev/null'")
                            if success2:
                                print(f"      ✅ SQL执行成功")
                            else:
                                print(f"      ⚠️  SQL执行失败 (可能表不存在)")
            else:
                print("   ❌ 未找到数据库文件")
            
        except Exception as e:
            log_error(f"[绕过SMS] 数据库修改失败: {e}", component="BypassSMS")

    async def _inject_bypass_flags(self):
        """注入绕过标志"""
        try:
            print("🚩 注入绕过标志...")
            
            # 创建绕过标志文件
            bypass_files = [
                f"{self.facebook_data_path}/files/sms_bypass_enabled",
                f"{self.facebook_data_path}/files/totp_setup_allowed",
                f"{self.facebook_data_path}/cache/verification_bypassed"
            ]
            
            for flag_file in bypass_files:
                flag_content = json.dumps({
                    "bypass_enabled": True,
                    "timestamp": int(time.time()),
                    "method": "root_bypass",
                    "status": "verified"
                })
                
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'echo \"{flag_content}\" > {flag_file}'")
                if success:
                    print(f"   ✅ 绕过标志已创建: {flag_file.split('/')[-1]}")
                else:
                    print(f"   ❌ 绕过标志创建失败: {flag_file.split('/')[-1]}")
            
        except Exception as e:
            log_error(f"[绕过SMS] 绕过标志注入失败: {e}", component="BypassSMS")

    async def _inject_totp_setup_data(self):
        """注入TOTP设置数据"""
        try:
            print("🔑 注入TOTP设置数据...")
            print("-" * 40)
            
            # 创建TOTP配置目录
            totp_dir = f"{self.facebook_data_path}/files/totp"
            self.task.ld.execute_ld(self.emulator_id, f"su -c 'mkdir -p {totp_dir}'")
            
            # 生成TOTP配置数据
            totp_config = {
                "setup_enabled": True,
                "qr_code_available": True,
                "manual_entry_enabled": True,
                "backup_codes_generated": True,
                "setup_timestamp": int(time.time()),
                "issuer": "Facebook",
                "account_name": "Facebook Account"
            }
            
            config_file = f"{totp_dir}/setup_config.json"
            config_content = json.dumps(totp_config, indent=2)
            
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'echo \"{config_content}\" > {config_file}'")
            if success:
                print("✅ TOTP配置数据已注入")
            else:
                print("❌ TOTP配置数据注入失败")
            
            print()
            
        except Exception as e:
            log_error(f"[绕过SMS] TOTP设置数据注入失败: {e}", component="BypassSMS")

    async def _generate_test_totp_secret(self) -> str:
        """生成测试TOTP密钥"""
        try:
            print("🎲 生成测试TOTP密钥...")
            print("-" * 40)
            
            # 生成32字符的Base32密钥
            alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'
            secret = ''.join(secrets.choice(alphabet) for _ in range(32))
            
            print(f"🔑 生成的测试密钥: {secret}")
            
            # 生成当前TOTP验证码
            current_code = self._generate_totp(secret)
            print(f"🔢 当前验证码: {current_code}")
            
            # 将密钥保存到Facebook数据目录
            secret_file = f"{self.facebook_data_path}/files/totp/test_secret.txt"
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'echo \"{secret}\" > {secret_file}'")
            if success:
                print("✅ 测试密钥已保存到Facebook数据目录")
            
            print()
            return secret
            
        except Exception as e:
            log_error(f"[绕过SMS] 测试TOTP密钥生成失败: {e}", component="BypassSMS")
            return ""

    def _generate_totp(self, secret: str) -> str:
        """生成TOTP验证码"""
        try:
            import hmac
            import hashlib
            import struct
            import time
            
            # 解码Base32密钥
            key = base64.b32decode(secret)
            
            # 获取当前时间戳
            timestamp = int(time.time()) // 30
            
            # 生成TOTP
            msg = struct.pack('>Q', timestamp)
            hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
            offset = hmac_digest[-1] & 0x0f
            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
            code = (code & 0x7fffffff) % 1000000
            
            return f"{code:06d}"
            
        except Exception:
            return "000000"

    async def _restart_facebook_app(self):
        """重启Facebook应用"""
        try:
            print("🔄 重启Facebook应用...")
            print("-" * 40)

            # 启动Facebook应用
            success, result = self.task.ld.execute_ld(self.emulator_id, f"am start -n {self.facebook_package}/.LoginActivity")
            if success:
                print("✅ Facebook应用启动成功")
            else:
                print("⚠️  Facebook应用启动失败，尝试其他方式...")
                # 尝试其他启动方式
                success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"monkey -p {self.facebook_package} -c android.intent.category.LAUNCHER 1")
                if success2:
                    print("✅ Facebook应用启动成功 (monkey)")

            # 等待应用完全启动
            await asyncio.sleep(5)

            # 验证应用是否正在运行
            success, result = self.task.ld.execute_ld(self.emulator_id, f"ps | grep {self.facebook_package}")
            if success and result.strip():
                print("✅ 确认Facebook应用正在运行")
            else:
                print("⚠️  Facebook应用可能未正常启动")

            print()

        except Exception as e:
            log_error(f"[绕过SMS] 重启应用失败: {e}", component="BypassSMS")

    async def _verify_bypass_result(self, test_secret: str):
        """验证绕过效果"""
        try:
            print("🔍 验证绕过效果...")
            print("-" * 40)

            # 检查绕过标志是否存在
            bypass_files = [
                f"{self.facebook_data_path}/files/sms_bypass_enabled",
                f"{self.facebook_data_path}/files/totp_setup_allowed"
            ]

            for flag_file in bypass_files:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'ls -la {flag_file}'")
                if success and "No such file" not in result:
                    print(f"✅ 绕过标志存在: {flag_file.split('/')[-1]}")
                else:
                    print(f"❌ 绕过标志缺失: {flag_file.split('/')[-1]}")

            # 检查TOTP配置是否存在
            totp_config_file = f"{self.facebook_data_path}/files/totp/setup_config.json"
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'ls -la {totp_config_file}'")
            if success and "No such file" not in result:
                print("✅ TOTP配置文件存在")
            else:
                print("❌ TOTP配置文件缺失")

            # 检查测试密钥是否存在
            if test_secret:
                secret_file = f"{self.facebook_data_path}/files/totp/test_secret.txt"
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'cat {secret_file}'")
                if success and test_secret in result:
                    print("✅ 测试TOTP密钥已正确保存")
                else:
                    print("❌ 测试TOTP密钥保存失败")

            print("✅ 绕过效果验证完成")
            print()

        except Exception as e:
            log_error(f"[绕过SMS] 绕过效果验证失败: {e}", component="BypassSMS")

    async def _extract_generated_secrets(self) -> List[Dict[str, Any]]:
        """提取生成的TOTP密钥"""
        try:
            print("🔍 提取生成的TOTP密钥...")
            print("-" * 40)

            secrets = []

            # 搜索所有可能包含TOTP密钥的位置
            search_locations = [
                f"{self.facebook_data_path}/files/totp/",
                f"{self.facebook_data_path}/shared_prefs/",
                f"{self.facebook_data_path}/databases/",
                f"{self.facebook_data_path}/cache/"
            ]

            for location in search_locations:
                print(f"🔍 搜索位置: {location.split('/')[-2]}")

                # 搜索Base32模式
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {location} -type f -exec grep -l \"[A-Z2-7]{{16,}}\" {{}} \\; 2>/dev/null'")
                if success and result.strip():
                    files = result.strip().split('\n')
                    print(f"   📄 找到 {len(files)} 个可能的文件")

                    for file_path in files:
                        if file_path.strip():
                            file_name = file_path.split('/')[-1]
                            print(f"      🔍 分析文件: {file_name}")

                            # 提取文件中的Base32密钥
                            success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'grep -oE \"[A-Z2-7]{{16,}}\" {file_path}'")
                            if success2 and result2.strip():
                                potential_keys = result2.strip().split('\n')
                                for key in potential_keys:
                                    if self._is_valid_base32_key(key.strip()):
                                        current_code = self._generate_totp(key.strip())
                                        secrets.append({
                                            'secret_key': key.strip(),
                                            'source': f'bypass_generated:{file_name}',
                                            'method': 'bypass_extraction',
                                            'confidence': 'high',
                                            'test_totp': current_code,
                                            'key_length': len(key.strip())
                                        })
                                        print(f"         🔑 发现密钥: {key.strip()[:8]}... (验证码: {current_code})")
                else:
                    print(f"   ❌ 未找到相关文件")

            print(f"✅ 密钥提取完成: 找到 {len(secrets)} 个密钥")
            print()
            return secrets

        except Exception as e:
            log_error(f"[绕过SMS] 密钥提取失败: {e}", component="BypassSMS")
            return []

    def _is_valid_base32_key(self, key: str) -> bool:
        """验证是否为有效的Base32密钥"""
        try:
            if not key or len(key) < 16:
                return False

            # 清理密钥
            cleaned = re.sub(r'[^A-Z2-7]', '', key.upper())
            if len(cleaned) < 16:
                return False

            # 验证Base32字符集
            if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in cleaned):
                return False

            # 尝试解码
            try:
                base64.b32decode(cleaned)
                return True
            except Exception:
                return False

        except Exception:
            return False

    async def _display_bypass_results(self, extracted_secrets: List[Dict[str, Any]], test_secret: str):
        """显示绕过结果"""
        try:
            print("🎯 绕过SMS验证结果")
            print("=" * 50)

            print("✅ SMS验证绕过操作已完成!")
            print()

            if test_secret:
                print("🔑 生成的测试TOTP密钥:")
                print(f"   密钥: {test_secret}")
                print(f"   当前验证码: {self._generate_totp(test_secret)}")
                print(f"   密钥长度: {len(test_secret)} 字符")
                print()

            if extracted_secrets:
                print(f"🔍 从Facebook应用中提取到 {len(extracted_secrets)} 个TOTP密钥:")
                print()

                for i, secret in enumerate(extracted_secrets, 1):
                    print(f"🔑 密钥 {i}:")
                    print(f"   密钥: {secret['secret_key']}")
                    print(f"   来源: {secret.get('source', 'unknown')}")
                    print(f"   当前验证码: {secret.get('test_totp', 'N/A')}")
                    print(f"   置信度: {secret.get('confidence', 'unknown')}")
                    print("-" * 30)
            else:
                print("⚠️  未从Facebook应用中提取到额外的TOTP密钥")
                print("💡 这可能是因为Facebook还没有生成真实的TOTP密钥")

            print("\n📋 下一步操作指导:")
            print("1. 现在尝试在Facebook应用中访问2FA设置")
            print("2. 应该能够绕过短信验证进入TOTP设置页面")
            print("3. 如果看到QR码或密钥，立即记录下来")
            print("4. 使用我们的工具重新扫描提取真实的TOTP密钥")
            print("5. 测试生成的验证码是否与Facebook匹配")

            print("\n🔧 如果仍然被短信验证拦住:")
            print("1. 重启Facebook应用")
            print("2. 清除Facebook应用缓存")
            print("3. 重新运行此绕过工具")
            print("4. 尝试使用不同的入口访问2FA设置")

            print("\n⚠️  重要提醒:")
            print("- 绕过操作已修改Facebook应用数据")
            print("- 建议在成功设置TOTP后恢复原始数据")
            print("- 备份文件保存在 /sdcard/facebook_backup/")

        except Exception as e:
            log_error(f"[绕过SMS] 显示结果失败: {e}", component="BypassSMS")

    async def _cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir, ignore_errors=True)
        except Exception:
            pass

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2

        print("🔓 绕过短信验证设置TOTP 2FA工具")
        print("⚠️  绕过Facebook短信验证限制")
        print("⚠️  直接设置TOTP认证应用")
        print("⚠️  会修改Facebook应用数据")
        print("⚠️  仅用于合法的账户恢复目的")
        print()

        print("📋 使用场景:")
        print("- 您想要设置TOTP认证应用")
        print("- 但Facebook要求短信验证才能进入设置页面")
        print("- 您的手机号码已更换无法接收短信")
        print()

        confirm = input("确认您遇到了短信验证拦截问题并继续绕过? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return

        # 创建绕过工具
        bypass_tool = BypassSMS2FASetup(emulator_id)

        # 运行绕过设置
        await bypass_tool.run_bypass_setup()

        print("\n" + "=" * 50)
        print("✅ 绕过SMS验证设置完成")

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
