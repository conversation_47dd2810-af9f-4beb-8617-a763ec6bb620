#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 模拟器2 Facebook密码重置工具
========================================
功能描述: 专门针对模拟器2的Facebook密码重置跳转工具

核心功能:
1. 直接在模拟器2中打开Facebook密码重置页面
2. 提供详细的操作指导
3. 确保在正确的模拟器中执行

使用方法:
python fb/emulator2_facebook_reset.py

注意事项:
- 专门针对模拟器2
- 直接跳转到Facebook密码重置页面
========================================
"""

import subprocess
import time

class Emulator2FacebookReset:
    """模拟器2 Facebook密码重置工具"""
    
    def __init__(self):
        """初始化工具"""
        self.emulator_id = 2
        self.ldconsole_path = "G:/leidian/LDPlayer9/ldconsole.exe"
        
        # Facebook密码重置URL
        self.reset_urls = [
            "https://www.facebook.com/login/identify",
            "https://m.facebook.com/login/identify",
            "https://mbasic.facebook.com/login/identify",
            "https://www.facebook.com/recover/initiate",
            "https://m.facebook.com/recover/initiate"
        ]

    def run_reset_jump(self):
        """运行密码重置跳转"""
        print("🔓 模拟器2 Facebook密码重置工具")
        print("=" * 50)
        print(f"📱 目标模拟器: 模拟器{self.emulator_id}")
        print()
        
        # 第一步：检查模拟器状态
        if not self.check_emulator_status():
            print("❌ 模拟器2状态异常，请检查模拟器")
            return
        
        # 第二步：尝试跳转到Facebook密码重置页面
        success = self.jump_to_facebook_reset()
        
        if success:
            print("✅ 成功在模拟器2中打开Facebook密码重置页面!")
            self.show_operation_guide()
        else:
            print("❌ 自动跳转失败")
            self.show_manual_guide()

    def check_emulator_status(self):
        """检查模拟器2状态"""
        print("🔍 检查模拟器2状态...")
        
        try:
            # 获取模拟器列表
            result = subprocess.run([self.ldconsole_path, "list2"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                print("   ❌ 无法获取模拟器列表")
                return False
            
            # 查找模拟器2
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if f"雷电模拟器-{self.emulator_id}" in line:
                    parts = line.split(',')
                    if len(parts) >= 5:
                        status = parts[4].strip()
                        if status == "1":
                            print("   ✅ 模拟器2正在运行")
                            return True
                        else:
                            print("   ⚠️  模拟器2未运行，尝试启动...")
                            return self.start_emulator()
            
            print("   ❌ 未找到模拟器2")
            return False
            
        except Exception as e:
            print(f"   ❌ 检查模拟器状态失败: {e}")
            return False

    def start_emulator(self):
        """启动模拟器2"""
        try:
            print("   🚀 启动模拟器2...")
            result = subprocess.run([self.ldconsole_path, "launch", "--index", str(self.emulator_id)], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("   ⏳ 等待模拟器2启动...")
                time.sleep(15)  # 等待启动
                
                # 再次检查状态
                return self.check_emulator_running()
            else:
                print("   ❌ 启动命令失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 启动模拟器失败: {e}")
            return False

    def check_emulator_running(self):
        """检查模拟器是否正在运行"""
        try:
            result = subprocess.run([self.ldconsole_path, "list2"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if f"雷电模拟器-{self.emulator_id}" in line:
                        parts = line.split(',')
                        if len(parts) >= 5:
                            status = parts[4].strip()
                            return status == "1"
            return False
            
        except Exception:
            return False

    def jump_to_facebook_reset(self):
        """跳转到Facebook密码重置页面"""
        print("\n🚀 在模拟器2中打开Facebook密码重置页面...")
        
        success_count = 0
        
        for i, url in enumerate(self.reset_urls, 1):
            print(f"   🔍 尝试URL {i}: {url}")
            
            try:
                # 使用Intent打开URL
                cmd = [self.ldconsole_path, f"action{self.emulator_id}", "--key", "call.adb", 
                       "--value", f"shell am start -a android.intent.action.VIEW -d '{url}'"]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    print(f"      ✅ URL {i} 启动成功")
                    success_count += 1
                    time.sleep(3)  # 等待页面加载
                    
                    # 检查是否成功打开
                    if self.verify_page_opened():
                        print(f"      🎉 成功打开Facebook页面!")
                        return True
                else:
                    print(f"      ❌ URL {i} 启动失败")
                    
            except Exception as e:
                print(f"      ❌ URL {i} 执行异常: {e}")
        
        print(f"\n📊 尝试结果: {success_count}/{len(self.reset_urls)} 个URL启动成功")
        return success_count > 0

    def verify_page_opened(self):
        """验证页面是否成功打开"""
        try:
            # 检查当前焦点窗口
            cmd = [self.ldconsole_path, f"action{self.emulator_id}", "--key", "call.adb", 
                   "--value", "shell dumpsys window windows | grep -E 'mCurrentFocus'"]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                focus_info = result.stdout.lower()
                # 检查是否包含浏览器或Facebook相关关键词
                keywords = ['facebook', 'browser', 'chrome', 'webview']
                return any(keyword in focus_info for keyword in keywords)
            
            return False
            
        except Exception:
            return False

    def show_operation_guide(self):
        """显示操作指导"""
        print("\n📋 Facebook密码重置操作指导")
        print("=" * 50)
        print("🎉 Facebook密码重置页面已在模拟器2中打开!")
        print()
        print("📱 请在模拟器2中按照以下步骤操作:")
        print()
        print("1️⃣  输入您的信息:")
        print("   - 在输入框中输入您的邮箱地址")
        print("   - 不要输入手机号码(因为已更换)")
        print("   - 点击'搜索'或'查找账户'")
        print()
        print("2️⃣  选择恢复方式:")
        print("   - 选择'通过邮箱重置密码'")
        print("   - 或选择'通过Facebook通知发送验证码'")
        print("   - 避免选择'通过短信验证'")
        print("   - 如果只有短信选项，点击'尝试其他方式'")
        print()
        print("3️⃣  完成验证:")
        print("   - 检查您的邮箱收件箱")
        print("   - 查看垃圾邮件文件夹")
        print("   - 点击Facebook发送的重置链接")
        print("   - 或确认Facebook应用通知")
        print()
        print("4️⃣  设置新密码:")
        print("   - 输入新的强密码")
        print("   - 确认新密码")
        print("   - 点击'重置密码'或'保存'")
        print()
        print("5️⃣  重新设置2FA:")
        print("   - 登录后立即进入安全设置")
        print("   - 重新设置双重验证")
        print("   - 选择认证器应用而不是短信")
        print("   - 保存备份代码到安全位置")
        print()
        print("💡 重要提示:")
        print("- 如果没有收到重置邮件，等待几分钟后重试")
        print("- 确保邮箱地址输入正确")
        print("- 可以尝试多个您可能使用过的邮箱")
        print("- 重置成功后立即更新安全设置")

    def show_manual_guide(self):
        """显示手动操作指导"""
        print("\n🔧 手动操作指导")
        print("=" * 50)
        print("如果自动跳转失败，请在模拟器2中手动操作:")
        print()
        print("📱 方法1: 手动打开浏览器")
        print("1. 在模拟器2中打开任意浏览器")
        print("2. 访问以下任一网址:")
        for i, url in enumerate(self.reset_urls[:3], 1):
            print(f"   {i}. {url}")
        print()
        print("📱 方法2: 使用Facebook应用")
        print("1. 在模拟器2中打开Facebook应用")
        print("2. 在登录页面点击'忘记密码?'")
        print("3. 输入邮箱地址(不要输入手机号)")
        print("4. 选择通过邮箱或Facebook通知重置")
        print()
        print("💡 关键提示:")
        print("- 确保在模拟器2中操作")
        print("- 使用邮箱而不是手机号")
        print("- 选择邮箱或Facebook通知验证方式")

def main():
    """主函数"""
    try:
        print("🔓 模拟器2 Facebook密码重置工具")
        print("⚠️  专门针对模拟器2")
        print("⚠️  直接跳转到Facebook密码重置页面")
        print("⚠️  绕过2FA验证")
        print()
        
        confirm = input("确认在模拟器2中打开Facebook密码重置页面? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return
        
        # 创建重置工具
        reset_tool = Emulator2FacebookReset()
        
        # 运行密码重置跳转
        reset_tool.run_reset_jump()
        
        print("\n" + "=" * 50)
        print("✅ 模拟器2 Facebook密码重置工具执行完成")
        print("💡 请在模拟器2中按照指导完成密码重置")
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
