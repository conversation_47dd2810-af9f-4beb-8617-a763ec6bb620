#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 模拟器2 Facebook密码重置工具
========================================
功能描述: 专门针对模拟器2的Facebook密码重置跳转工具

核心功能:
1. 直接在模拟器2中打开Facebook密码重置页面
2. 提供详细的操作指导
3. 确保在正确的模拟器中执行

使用方法:
python fb/emulator2_facebook_reset.py

注意事项:
- 专门针对模拟器2
- 直接跳转到Facebook密码重置页面
========================================
"""

import subprocess
import time

class Emulator2FacebookReset:
    """模拟器2 Facebook密码重置工具"""
    
    def __init__(self):
        """初始化工具"""
        self.emulator_id = 2
        self.ldconsole_path = "G:/leidian/LDPlayer9/ldconsole.exe"
        
        # Facebook密码重置URL
        self.reset_urls = [
            "https://www.facebook.com/login/identify",
            "https://m.facebook.com/login/identify",
            "https://mbasic.facebook.com/login/identify",
            "https://www.facebook.com/recover/initiate",
            "https://m.facebook.com/recover/initiate"
        ]

    def run_reset_jump(self):
        """运行密码重置跳转"""
        print("🔓 模拟器2 Facebook密码重置工具")
        print("=" * 50)
        print(f"📱 目标模拟器: 模拟器{self.emulator_id}")
        print()
        
        # 第一步：检查模拟器状态
        if not self.check_emulator_status():
            print("❌ 模拟器2状态异常，请检查模拟器")
            return
        
        # 第二步：尝试跳转到Facebook密码重置页面
        success = self.jump_to_facebook_reset()
        
        if success:
            print("✅ 成功在模拟器2中打开Facebook密码重置页面!")
            self.show_operation_guide()
        else:
            print("❌ 自动跳转失败")
            self.show_manual_guide()

    def check_emulator_status(self):
        """检查模拟器2状态"""
        print("🔍 检查模拟器2状态...")
        
        try:
            # 获取模拟器列表
            result = subprocess.run([self.ldconsole_path, "list2"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                print("   ❌ 无法获取模拟器列表")
                return False
            
            # 查找模拟器2
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if f"雷电模拟器-{self.emulator_id}" in line:
                    parts = line.split(',')
                    if len(parts) >= 5:
                        status = parts[4].strip()
                        if status == "1":
                            print("   ✅ 模拟器2正在运行")
                            return True
                        else:
                            print("   ⚠️  模拟器2未运行，尝试启动...")
                            return self.start_emulator()
            
            print("   ❌ 未找到模拟器2")
            return False
            
        except Exception as e:
            print(f"   ❌ 检查模拟器状态失败: {e}")
            return False

    def start_emulator(self):
        """启动模拟器2"""
        try:
            print("   🚀 启动模拟器2...")
            result = subprocess.run([self.ldconsole_path, "launch", "--index", str(self.emulator_id)], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("   ⏳ 等待模拟器2启动...")
                time.sleep(15)  # 等待启动
                
                # 再次检查状态
                return self.check_emulator_running()
            else:
                print("   ❌ 启动命令失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 启动模拟器失败: {e}")
            return False

    def check_emulator_running(self):
        """检查模拟器是否正在运行"""
        try:
            result = subprocess.run([self.ldconsole_path, "list2"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if f"雷电模拟器-{self.emulator_id}" in line:
                        parts = line.split(',')
                        if len(parts) >= 5:
                            status = parts[4].strip()
                            return status == "1"
            return False
            
        except Exception:
            return False

    def jump_to_facebook_reset(self):
        """跳转到Facebook密码重置页面 - 参考Instagram成功实现"""
        print("\n🚀 在模拟器2中打开Facebook密码重置页面...")

        # 方法1: 尝试Facebook深度链接（类似Instagram）
        facebook_deep_links = [
            "fb://login/identify",
            "fb://recover/initiate",
            "facebook://login/identify"
        ]

        print("   📱 方法1: 尝试Facebook深度链接...")
        for i, deep_link in enumerate(facebook_deep_links, 1):
            print(f"      🔍 尝试深度链接 {i}: {deep_link}")

            try:
                cmd = [self.ldconsole_path, f"action{self.emulator_id}", "--key", "call.adb",
                       "--value", f"shell am start -a android.intent.action.VIEW -d '{deep_link}'"]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)

                if result.returncode == 0:
                    print(f"         ✅ 深度链接 {i} 启动成功")
                    time.sleep(5)  # 等待应用启动

                    # 检查是否成功打开Facebook应用
                    if self.check_facebook_app_opened():
                        print(f"         🎉 成功打开Facebook应用!")
                        return True
                else:
                    print(f"         ❌ 深度链接 {i} 启动失败")

            except Exception as e:
                print(f"         ❌ 深度链接 {i} 执行异常: {e}")

        # 方法2: 直接启动Facebook应用然后导航
        print("   📱 方法2: 直接启动Facebook应用...")
        if self.launch_facebook_app():
            print("      ✅ Facebook应用启动成功")
            time.sleep(3)

            # 尝试导航到登录页面
            if self.navigate_to_login_page():
                print("      🎉 成功导航到登录页面!")
                return True

        # 方法3: 使用浏览器打开（参考Instagram的备用方案）
        print("   🌐 方法3: 使用浏览器打开...")
        return self.open_in_browser()

    def check_facebook_app_opened(self):
        """检查Facebook应用是否成功打开"""
        try:
            # 检查当前Activity
            cmd = [self.ldconsole_path, f"action{self.emulator_id}", "--key", "call.adb",
                   "--value", "shell dumpsys window windows | grep -E 'mCurrentFocus'"]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                focus_info = result.stdout.lower()
                # 检查是否包含Facebook相关关键词
                facebook_keywords = ['facebook', 'katana', 'com.facebook']
                return any(keyword in focus_info for keyword in facebook_keywords)

            return False

        except Exception:
            return False

    def launch_facebook_app(self):
        """直接启动Facebook应用"""
        try:
            facebook_packages = [
                "com.facebook.katana",  # Facebook主应用
                "com.facebook.lite"     # Facebook Lite
            ]

            for package in facebook_packages:
                print(f"      🔍 尝试启动: {package}")

                cmd = [self.ldconsole_path, f"action{self.emulator_id}", "--key", "call.adb",
                       "--value", f"shell monkey -p {package} -c android.intent.category.LAUNCHER 1"]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)

                if result.returncode == 0:
                    print(f"         ✅ {package} 启动命令成功")
                    time.sleep(3)

                    if self.check_facebook_app_opened():
                        print(f"         🎉 {package} 成功打开!")
                        return True
                else:
                    print(f"         ❌ {package} 启动失败")

            return False

        except Exception as e:
            print(f"      ❌ 启动Facebook应用异常: {e}")
            return False

    def navigate_to_login_page(self):
        """在Facebook应用中导航到登录页面"""
        try:
            print("      🧭 导航到登录页面...")

            # 等待应用完全加载
            time.sleep(2)

            # 尝试点击可能的登录相关按钮
            navigation_actions = [
                # 点击可能的"登录"按钮位置
                ("点击登录按钮", "shell input tap 360 600"),
                ("点击忘记密码", "shell input tap 360 650"),
                ("点击其他登录选项", "shell input tap 360 700"),

                # 尝试滑动寻找选项
                ("向上滑动", "shell input swipe 360 800 360 400"),
                ("向下滑动", "shell input swipe 360 400 360 800"),

                # 尝试菜单按钮
                ("点击菜单", "shell input keyevent KEYCODE_MENU"),
                ("点击返回", "shell input keyevent KEYCODE_BACK")
            ]

            for desc, action in navigation_actions:
                print(f"         🔍 {desc}...")

                cmd = [self.ldconsole_path, f"action{self.emulator_id}", "--key", "call.adb", "--value", action]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    print(f"            ✅ {desc} 执行成功")
                    time.sleep(1)

                    # 检查是否到达了登录相关页面
                    if self.check_login_page_elements():
                        print(f"            🎉 成功到达登录页面!")
                        return True
                else:
                    print(f"            ❌ {desc} 执行失败")

            return False

        except Exception as e:
            print(f"      ❌ 导航异常: {e}")
            return False

    def check_login_page_elements(self):
        """检查是否在登录页面"""
        try:
            # 检查页面内容是否包含登录相关元素
            cmd = [self.ldconsole_path, f"action{self.emulator_id}", "--key", "call.adb",
                   "--value", "shell uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                ui_content = result.stdout.lower()
                # 检查登录相关关键词
                login_keywords = ['登录', 'login', '密码', 'password', '忘记', 'forgot', '重置', 'reset']
                return any(keyword in ui_content for keyword in login_keywords)

            return False

        except Exception:
            return False

    def open_in_browser(self):
        """使用浏览器打开Facebook重置页面"""
        try:
            success_count = 0

            for i, url in enumerate(self.reset_urls[:3], 1):  # 只尝试前3个URL
                print(f"      🔍 浏览器打开URL {i}: {url}")

                cmd = [self.ldconsole_path, f"action{self.emulator_id}", "--key", "call.adb",
                       "--value", f"shell am start -a android.intent.action.VIEW -d '{url}'"]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)

                if result.returncode == 0:
                    print(f"         ✅ URL {i} 启动成功")
                    success_count += 1
                    time.sleep(3)

                    # 检查是否成功打开浏览器
                    if self.verify_browser_opened():
                        print(f"         🎉 成功在浏览器中打开!")
                        return True
                else:
                    print(f"         ❌ URL {i} 启动失败")

            print(f"      📊 浏览器尝试结果: {success_count}/{len(self.reset_urls[:3])} 个URL启动成功")
            return success_count > 0

        except Exception as e:
            print(f"      ❌ 浏览器打开异常: {e}")
            return False

    def verify_browser_opened(self):
        """验证浏览器是否成功打开"""
        try:
            cmd = [self.ldconsole_path, f"action{self.emulator_id}", "--key", "call.adb",
                   "--value", "shell dumpsys window windows | grep -E 'mCurrentFocus'"]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                focus_info = result.stdout.lower()
                # 检查是否包含浏览器相关关键词
                browser_keywords = ['browser', 'chrome', 'webview', 'facebook']
                return any(keyword in focus_info for keyword in browser_keywords)

            return False

        except Exception:
            return False



    def show_operation_guide(self):
        """显示操作指导"""
        print("\n📋 Facebook密码重置操作指导")
        print("=" * 50)
        print("🎉 Facebook密码重置页面已在模拟器2中打开!")
        print()
        print("📱 请在模拟器2中按照以下步骤操作:")
        print()
        print("1️⃣  输入您的信息:")
        print("   - 在输入框中输入您的邮箱地址")
        print("   - 不要输入手机号码(因为已更换)")
        print("   - 点击'搜索'或'查找账户'")
        print()
        print("2️⃣  选择恢复方式:")
        print("   - 选择'通过邮箱重置密码'")
        print("   - 或选择'通过Facebook通知发送验证码'")
        print("   - 避免选择'通过短信验证'")
        print("   - 如果只有短信选项，点击'尝试其他方式'")
        print()
        print("3️⃣  完成验证:")
        print("   - 检查您的邮箱收件箱")
        print("   - 查看垃圾邮件文件夹")
        print("   - 点击Facebook发送的重置链接")
        print("   - 或确认Facebook应用通知")
        print()
        print("4️⃣  设置新密码:")
        print("   - 输入新的强密码")
        print("   - 确认新密码")
        print("   - 点击'重置密码'或'保存'")
        print()
        print("5️⃣  重新设置2FA:")
        print("   - 登录后立即进入安全设置")
        print("   - 重新设置双重验证")
        print("   - 选择认证器应用而不是短信")
        print("   - 保存备份代码到安全位置")
        print()
        print("💡 重要提示:")
        print("- 如果没有收到重置邮件，等待几分钟后重试")
        print("- 确保邮箱地址输入正确")
        print("- 可以尝试多个您可能使用过的邮箱")
        print("- 重置成功后立即更新安全设置")

    def show_manual_guide(self):
        """显示手动操作指导"""
        print("\n🔧 手动操作指导")
        print("=" * 50)
        print("如果自动跳转失败，请在模拟器2中手动操作:")
        print()
        print("📱 方法1: 手动打开浏览器")
        print("1. 在模拟器2中打开任意浏览器")
        print("2. 访问以下任一网址:")
        for i, url in enumerate(self.reset_urls[:3], 1):
            print(f"   {i}. {url}")
        print()
        print("📱 方法2: 使用Facebook应用")
        print("1. 在模拟器2中打开Facebook应用")
        print("2. 在登录页面点击'忘记密码?'")
        print("3. 输入邮箱地址(不要输入手机号)")
        print("4. 选择通过邮箱或Facebook通知重置")
        print()
        print("💡 关键提示:")
        print("- 确保在模拟器2中操作")
        print("- 使用邮箱而不是手机号")
        print("- 选择邮箱或Facebook通知验证方式")

def main():
    """主函数"""
    try:
        print("🔓 模拟器2 Facebook密码重置工具")
        print("⚠️  专门针对模拟器2")
        print("⚠️  直接跳转到Facebook密码重置页面")
        print("⚠️  绕过2FA验证")
        print()
        
        confirm = input("确认在模拟器2中打开Facebook密码重置页面? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return
        
        # 创建重置工具
        reset_tool = Emulator2FacebookReset()
        
        # 运行密码重置跳转
        reset_tool.run_reset_jump()
        
        print("\n" + "=" * 50)
        print("✅ 模拟器2 Facebook密码重置工具执行完成")
        print("💡 请在模拟器2中按照指导完成密码重置")
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
