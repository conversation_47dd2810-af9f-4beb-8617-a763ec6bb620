#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 自动化2FA密钥提取工具
========================================
功能描述: 全自动的2FA密钥提取工具，无需用户交互

核心策略:
1. 自动检测和配置root权限
2. 自动安装Google Authenticator (如果需要)
3. 自动创建测试账户
4. 使用所有可能的方法提取密钥
5. 自动验证和保存结果

使用方法:
python fb/auto_2fa_extractor.py [emulator_id]

注意事项:
- 完全自动化，无需用户交互
- 适用于雷电模拟器
- 仅用于合法的账户恢复目的
========================================
"""

import asyncio
import sys
import re
import json
import base64
import tempfile
import struct
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class AutoTwoFactorExtractor:
    """自动化2FA密钥提取器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化自动提取器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.temp_dir = Path(tempfile.gettempdir()) / f"auto_extract_{emulator_id}"
        self.temp_dir.mkdir(exist_ok=True)
        
        log_info(f"[自动提取] 自动2FA提取器初始化 - 模拟器{emulator_id}", component="AutoExtractor")

    async def run_auto_extraction(self):
        """运行自动提取流程"""
        try:
            log_info(f"[自动提取] 开始自动2FA提取", component="AutoExtractor")
            
            print("🔓 自动化2FA密钥提取工具")
            print("=" * 50)
            print("⚠️  完全自动化，无需用户交互")
            print("⚠️  将尝试所有可能的提取方法")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：自动配置环境
            await self._auto_configure_environment()
            
            # 第二步：扫描现有认证器应用
            installed_apps = await self._scan_existing_authenticators()
            
            # 第三步：如果没有认证器，尝试安装
            if not installed_apps:
                await self._auto_install_authenticator()
                installed_apps = await self._scan_existing_authenticators()
            
            # 第四步：创建测试数据
            await self._create_test_data()
            
            # 第五步：使用所有方法提取密钥
            all_secrets = await self._extract_with_all_methods()
            
            # 第六步：验证和保存结果
            verified_secrets = await self._verify_and_save_results(all_secrets)
            
            # 第七步：显示最终结果
            await self._display_final_results(verified_secrets)
            
            log_info(f"[自动提取] 自动2FA提取完成", component="AutoExtractor")
            
        except Exception as e:
            log_error(f"[自动提取] 自动提取失败: {e}", component="AutoExtractor")
        finally:
            await self._cleanup()

    async def _auto_configure_environment(self):
        """自动配置环境"""
        try:
            print("🔧 自动配置环境...")
            print("-" * 40)
            
            # 检查基本连接
            success, result = self.task.ld.execute_ld(self.emulator_id, "whoami")
            if success:
                print(f"✅ 连接成功，当前用户: {result.strip()}")
            else:
                print("❌ 无法连接到模拟器")
                return
            
            # 检查root权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'whoami'")
            if success and "root" in result:
                print("✅ Root权限可用")
            else:
                print("❌ Root权限不可用，尝试启用...")
                
                # 尝试启用adb root
                success, result = self.task.ld.execute_ld(self.emulator_id, "adb root")
                if success:
                    print("✅ adb root权限已启用")
                    await asyncio.sleep(2)
                else:
                    print("⚠️  adb root权限启用失败")
            
            # 尝试重新挂载系统分区
            remount_commands = [
                "su -c 'mount -o remount,rw /system'",
                "su -c 'mount -o remount,rw /data'",
                "su -c 'chmod 755 /data/data'"
            ]
            
            for cmd in remount_commands:
                success, result = self.task.ld.execute_ld(self.emulator_id, cmd)
                if success:
                    print(f"✅ {cmd.split()[-1]} 配置成功")
                else:
                    print(f"⚠️  {cmd.split()[-1]} 配置失败")
            
            print("✅ 环境配置完成")
            print()
            
        except Exception as e:
            log_error(f"[自动提取] 环境配置失败: {e}", component="AutoExtractor")

    async def _scan_existing_authenticators(self) -> List[str]:
        """扫描现有认证器应用"""
        try:
            print("📱 扫描现有认证器应用...")
            print("-" * 40)
            
            # 常见认证器应用包名
            authenticator_packages = [
                "com.google.android.apps.authenticator2",  # Google Authenticator
                "com.azure.authenticator",                 # Microsoft Authenticator
                "com.authy.authy",                        # Authy
                "org.fedorahosted.freeotp",               # FreeOTP
                "org.liberty.android.freeotpplus",        # FreeOTP+
                "com.beemdevelopment.aegis",              # Aegis
                "org.shadowice.flocke.andotp",            # AndOTP
            ]
            
            installed_apps = []
            
            for package in authenticator_packages:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages | grep {package}")
                if success and package in result:
                    app_name = package.split('.')[-1]
                    print(f"✅ 发现应用: {app_name} ({package})")
                    installed_apps.append(package)
                else:
                    print(f"❌ 未安装: {package.split('.')[-1]}")
            
            print(f"✅ 扫描完成: 找到 {len(installed_apps)} 个认证器应用")
            print()
            return installed_apps
            
        except Exception as e:
            log_error(f"[自动提取] 扫描认证器应用失败: {e}", component="AutoExtractor")
            return []

    async def _auto_install_authenticator(self):
        """自动安装认证器应用"""
        try:
            print("📥 自动安装Google Authenticator...")
            print("-" * 40)
            
            # 尝试通过Play Store安装
            install_cmd = "am start -a android.intent.action.VIEW -d 'market://details?id=com.google.android.apps.authenticator2'"
            success, result = self.task.ld.execute_ld(self.emulator_id, install_cmd)
            if success:
                print("✅ 已打开Play Store安装页面")
                print("💡 等待10秒让用户有机会安装...")
                await asyncio.sleep(10)
            else:
                print("❌ 无法打开Play Store")
            
            # 检查是否安装成功
            success, result = self.task.ld.execute_ld(self.emulator_id, "pm list packages | grep com.google.android.apps.authenticator2")
            if success and "com.google.android.apps.authenticator2" in result:
                print("✅ Google Authenticator安装成功")
            else:
                print("⚠️  Google Authenticator未安装，继续使用其他方法")
            
            print()
            
        except Exception as e:
            log_error(f"[自动提取] 自动安装认证器失败: {e}", component="AutoExtractor")

    async def _create_test_data(self):
        """创建测试数据"""
        try:
            print("🔑 创建测试数据...")
            print("-" * 40)
            
            # 创建测试密钥文件
            test_secrets = [
                "JBSWY3DPEHPK3PXP",
                "KNRZ2OB5CK7L3THMQHACQRSTG6OB7VDF",
                "MFRGG2LTEBZGKZJAMRQXIYJAO5UXI2BAAAQGC3TEEDX3XPY",
                "GEZDGNBVGY3TQOJQGEZDGNBVGY3TQOJQ"
            ]
            
            # 将测试密钥写入文件
            test_file = "/sdcard/test_2fa_secrets.txt"
            test_content = "\n".join(test_secrets)
            
            success, result = self.task.ld.execute_ld(self.emulator_id, f"echo '{test_content}' > {test_file}")
            if success:
                print(f"✅ 测试数据文件创建成功: {test_file}")
                
                # 显示测试密钥和对应的验证码
                for i, secret in enumerate(test_secrets, 1):
                    current_code = self._generate_totp(secret)
                    print(f"   {i}. 密钥: {secret[:8]}... 验证码: {current_code}")
            else:
                print("❌ 测试数据文件创建失败")
            
            print("✅ 测试数据创建完成")
            print()
            
        except Exception as e:
            log_error(f"[自动提取] 创建测试数据失败: {e}", component="AutoExtractor")

    async def _extract_with_all_methods(self) -> List[Dict[str, Any]]:
        """使用所有方法提取密钥"""
        try:
            print("🔍 使用所有方法提取密钥...")
            print("-" * 40)
            
            all_secrets = []
            
            # 方法列表
            methods = [
                ("直接数据库访问", self._method_direct_database),
                ("应用数据目录扫描", self._method_app_data_scan),
                ("文件系统全局搜索", self._method_filesystem_search),
                ("进程内存分析", self._method_process_memory),
                ("系统服务分析", self._method_system_services),
                ("日志分析", self._method_log_analysis),
                ("备份文件分析", self._method_backup_analysis),
                ("字符串提取", self._method_strings_extraction)
            ]
            
            for method_name, method_func in methods:
                print(f"🔧 {method_name}...")
                try:
                    secrets = await method_func()
                    all_secrets.extend(secrets)
                    print(f"   结果: 找到 {len(secrets)} 个密钥")
                except Exception as e:
                    print(f"   ❌ 方法失败: {e}")
                print()
            
            print(f"✅ 所有方法完成: 总共找到 {len(all_secrets)} 个可能的密钥")
            print()
            return all_secrets
            
        except Exception as e:
            log_error(f"[自动提取] 全方法提取失败: {e}", component="AutoExtractor")
            return []

    async def _method_direct_database(self) -> List[Dict[str, Any]]:
        """直接数据库访问方法"""
        secrets = []
        
        # Google Authenticator数据库路径
        db_paths = [
            "/data/data/com.google.android.apps.authenticator2/databases/accounts.db",
            "/data/data/com.azure.authenticator/databases/accounts.db",
            "/data/data/com.authy.authy/databases/accounts.db"
        ]
        
        for db_path in db_paths:
            # 尝试直接访问数据库
            commands = [
                f"su -c 'sqlite3 {db_path} \"SELECT * FROM accounts\"'",
                f"su -c 'strings {db_path} | grep -E \"[A-Z2-7]{{16,}}\"'",
                f"su -c 'cat {db_path} | hexdump -C | grep -E \"[A-Z2-7]{{16,}}\"'"
            ]
            
            for cmd in commands:
                success, result = self.task.ld.execute_ld(self.emulator_id, cmd)
                if success and result.strip():
                    # 搜索Base32模式
                    base32_matches = re.findall(r'[A-Z2-7]{16,}', result)
                    for match in base32_matches:
                        if self._is_valid_base32_key(match):
                            secrets.append({
                                'secret_key': match,
                                'source': f'direct_db:{db_path}',
                                'method': 'direct_database_access',
                                'confidence': 'high'
                            })
                    break
        
        return secrets

    async def _method_app_data_scan(self) -> List[Dict[str, Any]]:
        """应用数据目录扫描方法"""
        secrets = []
        
        # 扫描应用数据目录
        app_packages = [
            "com.google.android.apps.authenticator2",
            "com.azure.authenticator",
            "com.authy.authy",
            "com.facebook.katana"  # Facebook应用
        ]
        
        for package in app_packages:
            data_path = f"/data/data/{package}"
            
            # 搜索所有文件中的Base32模式
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {data_path} -type f -exec grep -l \"[A-Z2-7]{{16,}}\" {{}} \\; 2>/dev/null'")
            if success and result.strip():
                files = result.strip().split('\n')
                for file_path in files:
                    if file_path.strip():
                        # 提取文件中的Base32密钥
                        success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'grep -oE \"[A-Z2-7]{{16,}}\" {file_path}'")
                        if success2 and result2.strip():
                            potential_keys = result2.strip().split('\n')
                            for key in potential_keys:
                                if self._is_valid_base32_key(key.strip()):
                                    secrets.append({
                                        'secret_key': key.strip(),
                                        'source': f'app_data:{file_path}',
                                        'method': 'app_data_scan',
                                        'confidence': 'high'
                                    })
        
        return secrets

    async def _method_filesystem_search(self) -> List[Dict[str, Any]]:
        """文件系统全局搜索方法"""
        secrets = []
        
        # 搜索整个文件系统中的Base32模式
        search_paths = [
            "/sdcard/",
            "/storage/emulated/0/",
            "/data/local/tmp/",
            "/cache/",
            "/tmp/"
        ]
        
        for path in search_paths:
            success, result = self.task.ld.execute_ld(self.emulator_id, f"find {path} -type f -exec grep -l '[A-Z2-7]{{16,}}' {{}} \\; 2>/dev/null | head -10")
            if success and result.strip():
                files = result.strip().split('\n')
                for file_path in files:
                    if file_path.strip():
                        # 从文件中提取Base32密钥
                        success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"grep -oE '[A-Z2-7]{{16,}}' '{file_path}' | head -5")
                        if success2 and result2.strip():
                            potential_keys = result2.strip().split('\n')
                            for key in potential_keys:
                                if self._is_valid_base32_key(key.strip()):
                                    secrets.append({
                                        'secret_key': key.strip(),
                                        'source': f'filesystem:{file_path}',
                                        'method': 'filesystem_search',
                                        'confidence': 'medium'
                                    })
        
        return secrets

    async def _method_process_memory(self) -> List[Dict[str, Any]]:
        """进程内存分析方法"""
        secrets = []
        
        # 获取认证器相关进程
        success, result = self.task.ld.execute_ld(self.emulator_id, "ps | grep -E '(authenticator|facebook|auth)'")
        if success and result.strip():
            processes = result.strip().split('\n')
            for process in processes:
                parts = process.split()
                if len(parts) > 1:
                    pid = parts[1]
                    
                    # 分析进程环境变量
                    success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"cat /proc/{pid}/environ 2>/dev/null | tr '\\0' '\\n' | grep -E '[A-Z2-7]{{16,}}'")
                    if success2 and result2.strip():
                        env_keys = result2.strip().split('\n')
                        for key in env_keys:
                            if self._is_valid_base32_key(key.strip()):
                                secrets.append({
                                    'secret_key': key.strip(),
                                    'source': f'process_memory:{pid}',
                                    'method': 'process_memory_analysis',
                                    'confidence': 'high'
                                })
        
        return secrets
