#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 自动查找Facebook模拟器并跳转密码重置
========================================
功能描述: 自动查找安装了Facebook的模拟器，并跳转到密码重置页面

核心功能:
1. 扫描所有雷电模拟器
2. 检测哪个模拟器安装了Facebook
3. 自动跳转到正确的模拟器
4. 打开Facebook密码重置页面

使用方法:
python fb/auto_find_facebook_emulator.py

注意事项:
- 自动检测所有模拟器
- 找到有Facebook的模拟器
- 直接跳转到密码重置页面
========================================
"""

import subprocess
import time
import os
from pathlib import Path

class AutoFacebookFinder:
    """自动Facebook模拟器查找器"""
    
    def __init__(self):
        """初始化查找器"""
        self.ldconsole_path = "G:/leidian/LDPlayer9/ldconsole.exe"
        self.facebook_emulator_id = None
        
        # Facebook密码重置URL
        self.reset_urls = [
            "https://www.facebook.com/login/identify",
            "https://m.facebook.com/login/identify", 
            "https://mbasic.facebook.com/login/identify"
        ]

    def find_and_jump(self):
        """查找Facebook模拟器并跳转"""
        print("🔍 自动查找Facebook模拟器并跳转密码重置")
        print("=" * 50)
        
        # 第一步：查找所有模拟器
        all_emulators = self.get_all_emulators()
        if not all_emulators:
            print("❌ 未找到任何模拟器")
            return
        
        print(f"📱 找到 {len(all_emulators)} 个模拟器")
        
        # 第二步：检测哪个模拟器有Facebook
        facebook_emulator = self.find_facebook_emulator(all_emulators)
        if not facebook_emulator:
            print("❌ 未找到安装Facebook的模拟器")
            self.show_manual_instructions()
            return
        
        print(f"🎯 找到Facebook模拟器: {facebook_emulator}")
        self.facebook_emulator_id = facebook_emulator
        
        # 第三步：跳转到密码重置页面
        success = self.jump_to_reset_page()
        if success:
            print("✅ 成功跳转到Facebook密码重置页面!")
            self.show_reset_instructions()
        else:
            print("❌ 跳转失败，显示手动方法")
            self.show_manual_instructions()

    def get_all_emulators(self):
        """获取所有模拟器列表"""
        try:
            print("🔍 扫描所有雷电模拟器...")
            
            result = subprocess.run([self.ldconsole_path, "list2"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                print("❌ 无法获取模拟器列表")
                return []
            
            emulators = []
            lines = result.stdout.strip().split('\n')
            
            for line in lines:
                if "雷电模拟器-" in line:
                    parts = line.split(',')
                    if len(parts) >= 1:
                        # 提取模拟器ID
                        emulator_info = parts[0]
                        if "雷电模拟器-" in emulator_info:
                            emulator_id = emulator_info.split('-')[1]
                            try:
                                emulator_id = int(emulator_id)
                                emulators.append(emulator_id)
                                print(f"   📱 发现模拟器: {emulator_id}")
                            except ValueError:
                                continue
            
            return emulators
            
        except Exception as e:
            print(f"❌ 扫描模拟器失败: {e}")
            return []

    def find_facebook_emulator(self, emulators):
        """查找安装了Facebook的模拟器"""
        print("\n🔍 检测Facebook应用...")
        
        for emulator_id in emulators:
            print(f"   🔍 检查模拟器 {emulator_id}...")
            
            # 检查模拟器是否运行
            if not self.is_emulator_running(emulator_id):
                print(f"      ⚠️  模拟器 {emulator_id} 未运行，尝试启动...")
                if not self.start_emulator(emulator_id):
                    print(f"      ❌ 模拟器 {emulator_id} 启动失败")
                    continue
            
            # 检查是否有Facebook应用
            if self.has_facebook_app(emulator_id):
                print(f"      ✅ 模拟器 {emulator_id} 安装了Facebook!")
                return emulator_id
            else:
                print(f"      ❌ 模拟器 {emulator_id} 未安装Facebook")
        
        return None

    def is_emulator_running(self, emulator_id):
        """检查模拟器是否运行"""
        try:
            result = subprocess.run([self.ldconsole_path, "list2"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if f"雷电模拟器-{emulator_id}" in line:
                        parts = line.split(',')
                        if len(parts) >= 5:
                            status = parts[4].strip()
                            return status == "1"
            return False
            
        except Exception:
            return False

    def start_emulator(self, emulator_id):
        """启动模拟器"""
        try:
            print(f"      🚀 启动模拟器 {emulator_id}...")
            result = subprocess.run([self.ldconsole_path, "launch", f"--index", str(emulator_id)], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"      ⏳ 等待模拟器 {emulator_id} 启动...")
                time.sleep(15)  # 等待启动
                return self.is_emulator_running(emulator_id)
            return False
            
        except Exception:
            return False

    def has_facebook_app(self, emulator_id):
        """检查是否安装了Facebook应用"""
        try:
            # 检查Facebook应用包
            facebook_packages = [
                "com.facebook.katana",
                "com.facebook.lite"
            ]
            
            for package in facebook_packages:
                cmd = [self.ldconsole_path, f"action{emulator_id}", "--key", "call.adb", 
                       "--value", f"shell pm list packages | grep {package}"]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0 and package in result.stdout:
                    return True
            
            return False
            
        except Exception:
            return False

    def jump_to_reset_page(self):
        """跳转到密码重置页面"""
        print(f"\n🚀 在模拟器 {self.facebook_emulator_id} 中打开Facebook密码重置页面...")
        
        success_count = 0
        
        for i, url in enumerate(self.reset_urls, 1):
            print(f"   🔍 尝试URL {i}: {url}")
            
            try:
                cmd = [self.ldconsole_path, f"action{self.facebook_emulator_id}", "--key", "call.adb", 
                       "--value", f"shell am start -a android.intent.action.VIEW -d '{url}'"]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    print(f"      ✅ URL {i} 启动成功")
                    success_count += 1
                    time.sleep(3)  # 等待页面加载
                    
                    # 检查是否成功打开
                    if self.check_page_opened():
                        print(f"      🎉 成功打开Facebook页面!")
                        return True
                else:
                    print(f"      ❌ URL {i} 启动失败")
                    
            except Exception as e:
                print(f"      ❌ URL {i} 执行异常: {e}")
        
        return success_count > 0

    def check_page_opened(self):
        """检查页面是否成功打开"""
        try:
            cmd = [self.ldconsole_path, f"action{self.facebook_emulator_id}", "--key", "call.adb", 
                   "--value", "shell dumpsys window windows | grep -E 'mCurrentFocus'"]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                focus_info = result.stdout.lower()
                return any(keyword in focus_info for keyword in ['facebook', 'browser', 'chrome'])
            
            return False
            
        except Exception:
            return False

    def show_reset_instructions(self):
        """显示密码重置指导"""
        print("\n📋 Facebook密码重置操作指导")
        print("=" * 50)
        print("🎉 Facebook密码重置页面已在模拟器中打开!")
        print()
        print("📱 请在模拟器中按照以下步骤操作:")
        print()
        print("1️⃣  输入您的信息:")
        print("   - 在输入框中输入您的邮箱地址")
        print("   - 不要输入手机号码(因为已更换)")
        print("   - 点击'搜索'或'查找账户'")
        print()
        print("2️⃣  选择恢复方式:")
        print("   - 选择'通过邮箱重置密码'")
        print("   - 或选择'通过Facebook通知发送验证码'")
        print("   - 避免选择'通过短信验证'")
        print()
        print("3️⃣  完成验证:")
        print("   - 检查您的邮箱收件箱")
        print("   - 或查看Facebook应用通知")
        print("   - 点击重置链接或确认通知")
        print()
        print("4️⃣  设置新密码:")
        print("   - 输入新的强密码")
        print("   - 确认新密码")
        print("   - 完成重置")
        print()
        print("💡 重要提示:")
        print("- 重置成功后立即重新设置2FA")
        print("- 选择认证器应用而不是短信")
        print("- 保存备份代码到安全位置")

    def show_manual_instructions(self):
        """显示手动操作指导"""
        print("\n🔧 手动操作指导")
        print("=" * 50)
        print("如果自动跳转失败，请手动操作:")
        print()
        print("1. 在有Facebook的模拟器中打开浏览器")
        print("2. 访问: https://www.facebook.com/login/identify")
        print("3. 输入您的邮箱地址")
        print("4. 选择通过邮箱或Facebook通知重置密码")
        print("5. 按照页面指导完成重置")

def main():
    """主函数"""
    try:
        print("🔍 自动查找Facebook模拟器并跳转密码重置")
        print("⚠️  自动检测所有模拟器")
        print("⚠️  找到有Facebook的模拟器")
        print("⚠️  直接跳转到密码重置页面")
        print()
        
        confirm = input("确认开始自动查找并跳转? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return
        
        # 创建自动查找器
        finder = AutoFacebookFinder()
        
        # 执行查找和跳转
        finder.find_and_jump()
        
        print("\n" + "=" * 50)
        print("✅ 自动查找和跳转完成")
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
