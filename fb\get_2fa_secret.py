#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔐 Facebook 2FA密钥获取助手
========================================
功能描述: 帮助用户通过多种方式获取Facebook 2FA密钥

主要功能:
1. 指导通过Facebook网页版获取密钥
2. 自动化浏览器操作获取密钥
3. 解析QR码获取密钥
4. 验证密钥有效性

使用方法:
python fb/get_2fa_secret.py

注意事项:
- 需要Facebook账户登录权限
- 建议在安全环境下操作
- 获取的密钥请妥善保管
========================================
"""

import asyncio
import sys
import time
import re
import base64
import qrcode
from pathlib import Path
from typing import Optional, Dict, Any
import urllib.parse

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.logger_manager import log_info, log_error, log_warning

class FacebookTwoFactorSecretGetter:
    """Facebook 2FA密钥获取器"""
    
    def __init__(self):
        """初始化密钥获取器"""
        self.output_dir = Path("./fb_2fa_results/secrets/")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        log_info("[密钥获取] Facebook 2FA密钥获取器初始化完成", component="SecretGetter")

    async def run_secret_acquisition(self):
        """运行密钥获取流程"""
        try:
            log_info("[密钥获取] 开始Facebook 2FA密钥获取流程", component="SecretGetter")
            
            print("🔐 Facebook 2FA密钥获取助手")
            print("=" * 50)
            
            # 显示获取方法选项
            await self._show_acquisition_methods()
            
            # 获取用户选择
            choice = await self._get_user_choice()
            
            if choice == "1":
                await self._method_web_manual()
            elif choice == "2":
                await self._method_qr_code()
            elif choice == "3":
                await self._method_existing_secret()
            elif choice == "4":
                await self._method_browser_automation()
            else:
                print("❌ 无效选择")
                return
            
            log_info("[密钥获取] Facebook 2FA密钥获取流程完成", component="SecretGetter")
            
        except Exception as e:
            log_error(f"[密钥获取] 密钥获取流程失败: {e}", component="SecretGetter")

    async def _show_acquisition_methods(self):
        """显示获取方法选项"""
        print("\n📋 可用的密钥获取方法:")
        print("1. 🌐 Facebook网页版手动获取 (推荐)")
        print("2. 📱 QR码解析获取")
        print("3. 🔑 已有密钥验证")
        print("4. 🤖 浏览器自动化获取 (高级)")
        print()

    async def _get_user_choice(self) -> str:
        """获取用户选择"""
        while True:
            try:
                choice = input("请选择获取方法 (1-4): ").strip()
                if choice in ["1", "2", "3", "4"]:
                    return choice
                else:
                    print("❌ 请输入有效选项 (1-4)")
            except KeyboardInterrupt:
                print("\n⚠️ 操作被取消")
                sys.exit(0)

    async def _method_web_manual(self):
        """方法1: Facebook网页版手动获取"""
        try:
            print("\n🌐 方法1: Facebook网页版手动获取")
            print("=" * 40)
            
            print("📋 操作步骤:")
            print("1. 打开浏览器，访问 https://www.facebook.com")
            print("2. 登录您的Facebook账户")
            print("3. 点击右上角头像 → 设置和隐私 → 设置")
            print("4. 左侧菜单选择 '安全和登录'")
            print("5. 找到 '双重验证' 部分，点击 '编辑'")
            print("6. 选择 '身份验证应用'")
            print("7. 点击 '设置' 或 '添加备份方法'")
            print("8. 选择 '无法扫描?' 或 '手动输入密钥'")
            print("9. 复制显示的密钥字符串")
            print()
            
            # 等待用户获取密钥
            print("⏳ 请按照上述步骤获取密钥...")
            secret_key = input("请粘贴获取到的密钥: ").strip()
            
            if secret_key:
                # 验证密钥格式
                cleaned_secret = self._clean_secret_key(secret_key)
                if cleaned_secret:
                    await self._save_secret(cleaned_secret, "facebook_web_manual")
                    await self._verify_secret(cleaned_secret)
                else:
                    print("❌ 密钥格式无效，请检查后重试")
            else:
                print("❌ 未输入密钥")
                
        except Exception as e:
            log_error(f"[密钥获取] 网页版手动获取失败: {e}", component="SecretGetter")

    async def _method_qr_code(self):
        """方法2: QR码解析获取"""
        try:
            print("\n📱 方法2: QR码解析获取")
            print("=" * 40)
            
            print("📋 操作步骤:")
            print("1. 在Facebook 2FA设置页面截取QR码图片")
            print("2. 将QR码图片保存到本地")
            print("3. 输入图片路径进行解析")
            print()
            
            # 获取QR码图片路径
            qr_path = input("请输入QR码图片路径: ").strip()
            
            if qr_path and Path(qr_path).exists():
                secret = await self._parse_qr_code(qr_path)
                if secret:
                    await self._save_secret(secret, "facebook_qr_code")
                    await self._verify_secret(secret)
                else:
                    print("❌ 无法解析QR码，请检查图片")
            else:
                print("❌ 图片文件不存在")
                
        except Exception as e:
            log_error(f"[密钥获取] QR码解析获取失败: {e}", component="SecretGetter")

    async def _method_existing_secret(self):
        """方法3: 已有密钥验证"""
        try:
            print("\n🔑 方法3: 已有密钥验证")
            print("=" * 40)
            
            print("如果您已经有Facebook 2FA密钥，可以在这里验证其有效性")
            print()
            
            secret_key = input("请输入您的2FA密钥: ").strip()
            
            if secret_key:
                cleaned_secret = self._clean_secret_key(secret_key)
                if cleaned_secret:
                    await self._save_secret(cleaned_secret, "facebook_existing")
                    await self._verify_secret(cleaned_secret)
                else:
                    print("❌ 密钥格式无效")
            else:
                print("❌ 未输入密钥")
                
        except Exception as e:
            log_error(f"[密钥获取] 已有密钥验证失败: {e}", component="SecretGetter")

    async def _method_browser_automation(self):
        """方法4: 浏览器自动化获取"""
        try:
            print("\n🤖 方法4: 浏览器自动化获取")
            print("=" * 40)
            
            print("⚠️ 此方法需要安装selenium和浏览器驱动")
            print("⚠️ 需要提供Facebook登录凭据")
            print("⚠️ 仅在安全环境下使用")
            print()
            
            confirm = input("是否继续? (y/N): ").strip().lower()
            if confirm != 'y':
                print("❌ 操作取消")
                return
            
            # 检查selenium是否可用
            try:
                from selenium import webdriver
                from selenium.webdriver.common.by import By
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                
                await self._automated_secret_extraction()
                
            except ImportError:
                print("❌ 未安装selenium，请先安装:")
                print("pip install selenium")
                print("并下载对应的浏览器驱动")
                
        except Exception as e:
            log_error(f"[密钥获取] 浏览器自动化获取失败: {e}", component="SecretGetter")

    async def _parse_qr_code(self, image_path: str) -> Optional[str]:
        """解析QR码获取密钥"""
        try:
            # 尝试使用PIL和pyzbar解析QR码
            try:
                from PIL import Image
                from pyzbar import pyzbar
                
                # 打开图片
                image = Image.open(image_path)
                
                # 解析QR码
                qr_codes = pyzbar.decode(image)
                
                for qr_code in qr_codes:
                    data = qr_code.data.decode('utf-8')
                    
                    # 检查是否为OTP URI
                    if data.startswith('otpauth://'):
                        secret = self._extract_secret_from_uri(data)
                        if secret:
                            print(f"✅ 成功解析QR码")
                            print(f"URI: {data}")
                            return secret
                
                print("❌ QR码中未找到有效的2FA密钥")
                return None
                
            except ImportError:
                print("❌ 需要安装PIL和pyzbar库:")
                print("pip install pillow pyzbar")
                return None
                
        except Exception as e:
            log_error(f"[密钥获取] QR码解析失败: {e}", component="SecretGetter")
            return None

    def _extract_secret_from_uri(self, uri: str) -> Optional[str]:
        """从OTP URI中提取密钥"""
        try:
            # 解析URI
            parsed = urllib.parse.urlparse(uri)
            params = urllib.parse.parse_qs(parsed.query)
            
            # 提取secret参数
            secret = params.get('secret', [''])[0]
            if secret:
                return self._clean_secret_key(secret)
            
            return None
            
        except Exception as e:
            log_error(f"[密钥获取] URI解析失败: {e}", component="SecretGetter")
            return None

    def _clean_secret_key(self, secret_key: str) -> str:
        """清理和验证密钥格式"""
        try:
            # 移除空格和特殊字符
            cleaned = re.sub(r'[^A-Z2-7]', '', secret_key.upper())
            
            # 验证Base32格式
            if len(cleaned) >= 16 and all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in cleaned):
                return cleaned
            
            return ""
            
        except Exception:
            return ""

    async def _save_secret(self, secret: str, source: str):
        """保存密钥到文件"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存到JSON文件
            secret_data = {
                'secret_key': secret,
                'source': source,
                'service': 'Facebook',
                'timestamp': datetime.datetime.now().isoformat(),
                'length': len(secret)
            }
            
            json_file = self.output_dir / f"facebook_2fa_secret_{timestamp}.json"
            
            import json
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(secret_data, f, indent=2, ensure_ascii=False)
            
            # 保存到文本文件
            txt_file = self.output_dir / f"facebook_2fa_secret_{timestamp}.txt"
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(f"Facebook 2FA密钥\n")
                f.write(f"获取时间: {secret_data['timestamp']}\n")
                f.write(f"获取方式: {source}\n")
                f.write(f"密钥长度: {len(secret)}\n")
                f.write(f"=" * 40 + "\n")
                f.write(f"密钥: {secret}\n")
                f.write(f"=" * 40 + "\n")
                f.write(f"格式化密钥 (每4位一组):\n")
                formatted = ' '.join([secret[i:i+4] for i in range(0, len(secret), 4)])
                f.write(f"{formatted}\n")
            
            print(f"✅ 密钥已保存到:")
            print(f"   📄 {json_file}")
            print(f"   📄 {txt_file}")
            
        except Exception as e:
            log_error(f"[密钥获取] 保存密钥失败: {e}", component="SecretGetter")

    async def _verify_secret(self, secret: str):
        """验证密钥有效性"""
        try:
            print(f"\n🔍 验证密钥有效性...")
            print(f"密钥: {secret}")
            print(f"长度: {len(secret)} 字符")
            
            # 生成当前时间的TOTP码进行验证
            try:
                import hmac
                import hashlib
                import struct
                import time
                
                # 解码Base32密钥
                key = base64.b32decode(secret)
                
                # 获取当前时间戳
                timestamp = int(time.time()) // 30
                
                # 生成TOTP
                msg = struct.pack('>Q', timestamp)
                hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
                offset = hmac_digest[-1] & 0x0f
                code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
                code = (code & 0x7fffffff) % 1000000
                
                print(f"✅ 密钥格式有效")
                print(f"🔢 当前TOTP验证码: {code:06d}")
                print(f"⏰ 验证码有效期: 30秒")
                print(f"💡 您可以用此验证码在Facebook中验证密钥是否正确")
                
            except Exception as e:
                print(f"❌ 密钥验证失败: {e}")
                
        except Exception as e:
            log_error(f"[密钥获取] 密钥验证失败: {e}", component="SecretGetter")

    async def _automated_secret_extraction(self):
        """自动化密钥提取 (高级功能)"""
        try:
            print("🤖 启动浏览器自动化...")
            print("⚠️ 请确保您的Facebook账户安全")
            
            # 获取登录信息
            email = input("Facebook邮箱: ").strip()
            password = input("Facebook密码: ").strip()
            
            if not email or not password:
                print("❌ 登录信息不完整")
                return
            
            print("🚀 正在启动浏览器...")
            
            # 这里可以实现selenium自动化逻辑
            # 由于安全考虑，这里只提供框架
            print("⚠️ 自动化功能正在开发中...")
            print("💡 建议使用手动方法获取密钥")
            
        except Exception as e:
            log_error(f"[密钥获取] 自动化提取失败: {e}", component="SecretGetter")

async def main():
    """主函数"""
    try:
        print("🔐 Facebook 2FA密钥获取助手")
        print("=" * 50)
        
        # 创建密钥获取器
        getter = FacebookTwoFactorSecretGetter()
        
        # 运行密钥获取流程
        await getter.run_secret_acquisition()
        
        print("\n" + "=" * 50)
        print("✅ 密钥获取流程完成")
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
