{"status": "completed", "test_type": "real_follow_flow_test", "emulator_id": 2, "test_time": "2025-07-27 18:59:59", "duration": "2.23秒", "summary": {"total_stages": 4, "success_count": 3, "failed_count": 1, "success_rate": "75.0%"}, "stage_results": {"stage1": {"name": "环境验证与应用检测", "result": true, "message": "⚡ 快速模式跳过", "timestamp": "18:59:57"}, "stage2": {"name": "V2Ray节点连接", "result": true, "message": "⚡ 快速模式跳过", "timestamp": "18:59:57"}, "stage3": {"name": "Instagram应用启动", "result": true, "message": "⚡ 快速模式跳过", "timestamp": "18:59:57"}, "stage4": {"name": "关注粉丝业务流程", "result": false, "message": "失败", "follow_count": 0, "duration": 0.0, "timestamp": "18:59:57"}}, "real_config_used": {"emulator_id": 2, "direct_follow_count": 2, "fans_follow_count": 2, "min_followers": 1, "switch_delay_min": 1500, "switch_delay_max": 2000, "follow_delay_min": 500, "follow_delay_max": 1000, "skip_verified": true, "skip_private": true, "all_regions": false, "japan": true, "korea": false, "thailand": false, "follow_users_path": "guanzhu.txt"}, "final_stats": {"follow_count": 0, "target_regions": []}}