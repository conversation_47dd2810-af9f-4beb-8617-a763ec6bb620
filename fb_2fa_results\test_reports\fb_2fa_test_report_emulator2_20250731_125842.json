{"emulator_id": 2, "test_time": "2025-07-31T12:58:42.847470", "total_tests": 4, "successful_tests": 1, "test_results": [{"test_name": "基础连接测试", "success": false, "details": {"adb_connection": false, "facebook_installed": false, "authenticator_installed": false}}, {"test_name": "提取器功能测试", "success": false, "details": {"secrets_found": 0, "extraction_method": "", "error_message": "未找到任何2FA密钥"}}, {"test_name": "任务执行器测试", "success": false, "details": {"execution_time": 0.2540006637573242, "error_message": "环境检测失败", "extraction_result": {}}}, {"test_name": "数据格式验证测试", "success": true, "details": {"key_cleaning_results": [{"original": "JBSWY3DPEHPK3PXP", "cleaned": "JBSWY3DPEHPK3PXP", "valid": true}, {"original": "jbswy3dpehpk3pxp", "cleaned": "JBSWY3DPEHPK3PXP", "valid": true}, {"original": "JBSWY3DP EHPK3PXP", "cleaned": "JBSWY3DPEHPK3PXP", "valid": true}, {"original": "invalid_key_123", "cleaned": "", "valid": false}, {"original": "", "cleaned": "", "valid": false}], "uri_parsing_success": true, "parsed_secret": {"service_name": "Facebook", "account": "<EMAIL>", "secret_key": "JBSWY3DPEHPK3PXP", "issuer": "Facebook", "algorithm": "SHA1", "digits": 6, "period": 30, "counter": 0, "type": "TOTP"}}}]}