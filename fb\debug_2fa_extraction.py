#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Facebook 2FA提取调试脚本 - 详细调试版本
========================================
功能描述: 提供详细的调试信息，帮助分析2FA数据提取过程

主要功能:
1. 详细的应用检测和数据目录扫描
2. 文件内容预览和分析
3. 权限检查和访问测试
4. 实时调试输出

使用方法:
python fb/debug_2fa_extraction.py [emulator_id]

注意事项:
- 提供详细的调试信息
- 帮助分析为什么没有找到2FA数据
- 显示所有扫描的文件和目录
========================================
"""

import asyncio
import sys
import time
import re
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning
from core.simple_config import get_config_manager

class FacebookTwoFactorDebugger:
    """Facebook 2FA提取调试器 - 详细分析版本"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化调试器
        
        Args:
            emulator_id: 测试用的模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        
        log_info(f"[调试] Facebook 2FA提取调试器初始化 - 模拟器{emulator_id}", component="FB2FADebugger")

    async def run_detailed_debug(self):
        """运行详细调试分析"""
        try:
            log_info(f"[调试] 开始详细调试分析", component="FB2FADebugger")
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            # 调试1: 基础环境检查
            await self._debug_basic_environment()
            
            # 调试2: 应用检测详情
            await self._debug_app_detection()
            
            # 调试3: 数据目录扫描
            await self._debug_data_directories()
            
            # 调试4: 权限检查
            await self._debug_permissions()
            
            # 调试5: 文件内容分析
            await self._debug_file_contents()
            
            log_info(f"[调试] 详细调试分析完成", component="FB2FADebugger")
            
        except Exception as e:
            log_error(f"[调试] 调试分析失败: {e}", component="FB2FADebugger")

    async def _debug_basic_environment(self):
        """调试1: 基础环境检查"""
        try:
            log_info(f"[调试1] ========== 基础环境检查 ==========", component="FB2FADebugger")
            
            # 检查雷电API
            if self.task.ld:
                log_info(f"[调试1] ✅ 雷电API初始化成功", component="FB2FADebugger")
                log_info(f"[调试1] 模拟器路径: {getattr(self.task.ld, 'base_path', 'Unknown')}", component="FB2FADebugger")
                log_info(f"[调试1] 共享路径: {getattr(self.task.ld, 'share_path', 'Unknown')}", component="FB2FADebugger")
            else:
                log_error(f"[调试1] ❌ 雷电API初始化失败", component="FB2FADebugger")
                return
            
            # 检查模拟器状态
            is_running, is_android, info = self.task.ld.is_running(self.emulator_id)
            log_info(f"[调试1] 模拟器运行状态: {is_running}", component="FB2FADebugger")
            log_info(f"[调试1] Android系统状态: {is_android}", component="FB2FADebugger")
            log_info(f"[调试1] 模拟器信息: {info}", component="FB2FADebugger")
            
            # 测试ADB连接
            success, result = self.task.ld.execute_ld(self.emulator_id, "echo 'debug_test'")
            log_info(f"[调试1] ADB连接测试: {'成功' if success and 'debug_test' in result else '失败'}", component="FB2FADebugger")
            if success:
                log_info(f"[调试1] ADB响应: {result.strip()}", component="FB2FADebugger")
            
            # 获取Android版本信息
            success, android_version = self.task.ld.execute_ld(self.emulator_id, "getprop ro.build.version.release")
            if success:
                log_info(f"[调试1] Android版本: {android_version.strip()}", component="FB2FADebugger")
            
            # 获取设备信息
            success, device_info = self.task.ld.execute_ld(self.emulator_id, "getprop ro.product.model")
            if success:
                log_info(f"[调试1] 设备型号: {device_info.strip()}", component="FB2FADebugger")
            
            log_info(f"[调试1] ==========================================", component="FB2FADebugger")
            
        except Exception as e:
            log_error(f"[调试1] 基础环境检查失败: {e}", component="FB2FADebugger")

    async def _debug_app_detection(self):
        """调试2: 应用检测详情"""
        try:
            log_info(f"[调试2] ========== 应用检测详情 ==========", component="FB2FADebugger")
            
            # 检测的应用包名
            target_packages = [
                "com.facebook.katana",           # Facebook主应用
                "com.facebook.orca",             # Messenger
                "com.facebook.mlite",            # Facebook Lite
                "com.google.android.apps.authenticator2",  # Google Authenticator
                "com.microsoft.msa.authenticator",         # Microsoft Authenticator
                "com.authy.authy",                         # Authy
                "org.fedorahosted.freeotp",               # FreeOTP
            ]
            
            installed_apps = []
            
            for package in target_packages:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages | grep {package}")
                is_installed = success and package in result
                
                status = "✅ 已安装" if is_installed else "❌ 未安装"
                log_info(f"[调试2] {package}: {status}", component="FB2FADebugger")
                
                if is_installed:
                    installed_apps.append(package)
                    
                    # 获取应用版本信息
                    success, version_info = self.task.ld.execute_ld(self.emulator_id, f"dumpsys package {package} | grep versionName")
                    if success and version_info.strip():
                        log_info(f"[调试2]   版本信息: {version_info.strip()}", component="FB2FADebugger")
                    
                    # 检查应用权限
                    success, permissions = self.task.ld.execute_ld(self.emulator_id, f"dumpsys package {package} | grep permission")
                    if success and permissions.strip():
                        perm_lines = permissions.strip().split('\n')[:3]  # 只显示前3个权限
                        for perm in perm_lines:
                            if perm.strip():
                                log_info(f"[调试2]   权限: {perm.strip()}", component="FB2FADebugger")
            
            log_info(f"[调试2] 总计发现 {len(installed_apps)} 个相关应用", component="FB2FADebugger")
            log_info(f"[调试2] ==========================================", component="FB2FADebugger")
            
        except Exception as e:
            log_error(f"[调试2] 应用检测失败: {e}", component="FB2FADebugger")

    async def _debug_data_directories(self):
        """调试3: 数据目录扫描"""
        try:
            log_info(f"[调试3] ========== 数据目录扫描 ==========", component="FB2FADebugger")
            
            # 要扫描的应用
            apps_to_scan = [
                "com.facebook.katana",
                "com.google.android.apps.authenticator2",
            ]
            
            for package in apps_to_scan:
                log_info(f"[调试3] 扫描应用: {package}", component="FB2FADebugger")
                
                # 检查应用是否安装
                success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages | grep {package}")
                if not success or package not in result:
                    log_info(f"[调试3]   ❌ 应用未安装，跳过", component="FB2FADebugger")
                    continue
                
                # 扫描数据目录
                data_paths = [
                    f"/data/data/{package}/databases/",
                    f"/data/data/{package}/shared_prefs/",
                    f"/data/data/{package}/files/",
                    f"/data/data/{package}/cache/",
                ]
                
                for data_path in data_paths:
                    log_info(f"[调试3]   扫描目录: {data_path}", component="FB2FADebugger")
                    
                    success, ls_result = self.task.ld.execute_ld(self.emulator_id, f"ls -la {data_path}")
                    if success and "No such file" not in ls_result:
                        lines = ls_result.strip().split('\n')
                        file_count = 0
                        
                        for line in lines:
                            if not line.strip() or line.startswith('total'):
                                continue
                            
                            parts = line.split()
                            if len(parts) >= 9:
                                filename = parts[-1]
                                file_size = parts[4] if len(parts) > 4 else '0'
                                permissions = parts[0] if len(parts) > 0 else ''
                                
                                # 检查是否为潜在的2FA文件
                                is_potential = self._is_potential_2fa_file(filename)
                                potential_mark = "🎯" if is_potential else "  "
                                
                                log_info(f"[调试3]     {potential_mark} {filename} ({file_size} bytes, {permissions})", component="FB2FADebugger")
                                file_count += 1
                        
                        log_info(f"[调试3]   找到 {file_count} 个文件", component="FB2FADebugger")
                    else:
                        log_info(f"[调试3]   ❌ 无法访问或目录不存在", component="FB2FADebugger")
                
                log_info(f"[调试3] ------------------------------------------", component="FB2FADebugger")
            
            log_info(f"[调试3] ==========================================", component="FB2FADebugger")
            
        except Exception as e:
            log_error(f"[调试3] 数据目录扫描失败: {e}", component="FB2FADebugger")

    async def _debug_permissions(self):
        """调试4: 权限检查"""
        try:
            log_info(f"[调试4] ========== 权限检查 ==========", component="FB2FADebugger")
            
            # 检查root权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'echo root_available'")
            has_root = success and "root_available" in result
            log_info(f"[调试4] Root权限: {'✅ 可用' if has_root else '❌ 不可用'}", component="FB2FADebugger")
            
            # 检查文件系统权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "ls -la /data/data/")
            if success:
                log_info(f"[调试4] /data/data/ 目录访问: ✅ 成功", component="FB2FADebugger")
                lines = result.strip().split('\n')
                app_count = len([line for line in lines if 'com.' in line])
                log_info(f"[调试4] 发现 {app_count} 个应用数据目录", component="FB2FADebugger")
            else:
                log_info(f"[调试4] /data/data/ 目录访问: ❌ 失败", component="FB2FADebugger")
            
            # 检查特定应用目录权限
            test_packages = ["com.facebook.katana", "com.google.android.apps.authenticator2"]
            for package in test_packages:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"ls -la /data/data/{package}/")
                if success and "No such file" not in result:
                    log_info(f"[调试4] {package} 数据目录: ✅ 可访问", component="FB2FADebugger")
                else:
                    log_info(f"[调试4] {package} 数据目录: ❌ 不可访问", component="FB2FADebugger")
            
            log_info(f"[调试4] ==========================================", component="FB2FADebugger")
            
        except Exception as e:
            log_error(f"[调试4] 权限检查失败: {e}", component="FB2FADebugger")

    async def _debug_file_contents(self):
        """调试5: 文件内容分析"""
        try:
            log_info(f"[调试5] ========== 文件内容分析 ==========", component="FB2FADebugger")
            
            # 查找潜在的2FA文件
            potential_files = []
            
            # 扫描Facebook应用
            package = "com.facebook.katana"
            success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages | grep {package}")
            if success and package in result:
                data_paths = [
                    f"/data/data/{package}/databases/",
                    f"/data/data/{package}/shared_prefs/",
                ]
                
                for data_path in data_paths:
                    success, ls_result = self.task.ld.execute_ld(self.emulator_id, f"ls -la {data_path}")
                    if success and "No such file" not in ls_result:
                        lines = ls_result.strip().split('\n')
                        
                        for line in lines:
                            if not line.strip() or line.startswith('total'):
                                continue
                            
                            parts = line.split()
                            if len(parts) >= 9:
                                filename = parts[-1]
                                if self._is_potential_2fa_file(filename):
                                    file_path = f"{data_path}/{filename}"
                                    potential_files.append(file_path)
            
            log_info(f"[调试5] 找到 {len(potential_files)} 个潜在的2FA文件", component="FB2FADebugger")
            
            # 分析文件内容
            for file_path in potential_files[:5]:  # 只分析前5个文件
                log_info(f"[调试5] 分析文件: {file_path}", component="FB2FADebugger")
                
                success, content = self.task.ld.execute_ld(self.emulator_id, f"head -20 {file_path}")
                if success and content.strip():
                    # 查找可能的密钥模式
                    base32_matches = re.findall(r'[A-Z2-7]{16,}', content)
                    if base32_matches:
                        log_info(f"[调试5]   🎯 发现可能的Base32密钥: {len(base32_matches)}个", component="FB2FADebugger")
                        for i, match in enumerate(base32_matches[:3]):  # 只显示前3个
                            log_info(f"[调试5]     {i+1}. {match[:8]}...{match[-8:]} (长度: {len(match)})", component="FB2FADebugger")
                    
                    # 查找其他关键词
                    keywords = ['secret', 'token', 'auth', '2fa', 'totp', 'facebook']
                    found_keywords = []
                    for keyword in keywords:
                        if keyword.lower() in content.lower():
                            found_keywords.append(keyword)
                    
                    if found_keywords:
                        log_info(f"[调试5]   🔍 发现关键词: {', '.join(found_keywords)}", component="FB2FADebugger")
                    
                    # 显示文件内容预览
                    preview_lines = content.strip().split('\n')[:3]
                    for i, line in enumerate(preview_lines):
                        if line.strip():
                            log_info(f"[调试5]   内容预览{i+1}: {line.strip()[:50]}...", component="FB2FADebugger")
                else:
                    log_info(f"[调试5]   ❌ 无法读取文件内容", component="FB2FADebugger")
            
            log_info(f"[调试5] ==========================================", component="FB2FADebugger")
            
        except Exception as e:
            log_error(f"[调试5] 文件内容分析失败: {e}", component="FB2FADebugger")

    def _is_potential_2fa_file(self, filename: str) -> bool:
        """判断文件是否可能包含2FA数据"""
        potential_keywords = [
            'auth', '2fa', 'totp', 'secret', 'account', 'token', 'key', 'otp'
        ]
        
        filename_lower = filename.lower()
        return any(keyword in filename_lower for keyword in potential_keywords)

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2
        
        print(f"🔍 开始Facebook 2FA提取详细调试 - 模拟器{emulator_id}")
        print("=" * 60)
        
        # 创建调试器
        debugger = FacebookTwoFactorDebugger(emulator_id)
        
        # 运行详细调试
        await debugger.run_detailed_debug()
        
        print("=" * 60)
        print("✅ Facebook 2FA提取详细调试完成")
        
    except KeyboardInterrupt:
        print("\n⚠️  调试被用户中断")
    except Exception as e:
        print(f"❌ 调试执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
