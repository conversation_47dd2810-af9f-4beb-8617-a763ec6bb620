#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 Facebook 2FA密钥专用提取工具
========================================
功能描述: 专门从Facebook应用中提取2FA密钥

核心策略:
1. 直接访问Facebook应用数据目录 (/data/data/com.facebook.katana/)
2. 扫描所有数据库文件 (databases/*.db)
3. 分析SharedPreferences文件 (shared_prefs/*.xml)
4. 搜索缓存和临时文件 (cache/, files/)
5. 使用多种方法提取Base32密钥

Facebook应用包名: com.facebook.katana
数据目录: /data/data/com.facebook.katana/

使用方法:
python fb/facebook_2fa_extractor.py [emulator_id]

注意事项:
- 需要root权限
- 专门针对Facebook应用
- 仅用于合法的账户恢复目的
========================================
"""

import asyncio
import sys
import re
import json
import base64
import tempfile
import struct
import hashlib
import sqlite3
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class FacebookTwoFactorExtractor:
    """Facebook 2FA密钥专用提取器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化Facebook 2FA提取器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.temp_dir = Path(tempfile.gettempdir()) / f"fb_2fa_extract_{emulator_id}"
        self.temp_dir.mkdir(exist_ok=True)
        
        # Facebook应用信息
        self.facebook_package = "com.facebook.katana"
        self.facebook_data_path = f"/data/data/{self.facebook_package}"
        
        # 可能存储2FA密钥的位置
        self.target_locations = [
            "databases/",
            "shared_prefs/",
            "files/",
            "cache/",
            "app_webview/",
            "code_cache/",
            "no_backup/"
        ]
        
        log_info(f"[Facebook 2FA] Facebook 2FA提取器初始化 - 模拟器{emulator_id}", component="FacebookExtractor")

    async def run_facebook_extraction(self):
        """运行Facebook 2FA提取流程"""
        try:
            log_info(f"[Facebook 2FA] 开始Facebook 2FA提取", component="FacebookExtractor")
            
            print("🔓 Facebook 2FA密钥专用提取工具")
            print("=" * 50)
            print("⚠️  专门从Facebook应用中提取2FA密钥")
            print("⚠️  需要root权限访问应用数据")
            print("⚠️  仅用于合法的账户恢复目的")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：验证root权限和Facebook应用
            if not await self._verify_prerequisites():
                return
            
            # 第二步：扫描Facebook应用数据目录
            await self._scan_facebook_data_directory()
            
            # 第三步：使用多种方法提取2FA密钥
            all_secrets = await self._extract_facebook_2fa_secrets()
            
            # 第四步：验证和保存结果
            verified_secrets = await self._verify_and_save_results(all_secrets)
            
            # 第五步：显示最终结果
            await self._display_final_results(verified_secrets)
            
            log_info(f"[Facebook 2FA] Facebook 2FA提取完成", component="FacebookExtractor")
            
        except Exception as e:
            log_error(f"[Facebook 2FA] Facebook 2FA提取失败: {e}", component="FacebookExtractor")
        finally:
            await self._cleanup()

    async def _verify_prerequisites(self) -> bool:
        """验证前提条件"""
        try:
            print("🔧 验证前提条件...")
            print("-" * 40)
            
            # 检查root权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'whoami'")
            if not success or "root" not in result:
                print("❌ Root权限不可用")
                print("💡 请确保雷电模拟器已开启root权限")
                return False
            
            print("✅ Root权限可用")
            
            # 检查Facebook应用是否已安装
            success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages | grep {self.facebook_package}")
            if not success or self.facebook_package not in result:
                print("❌ Facebook应用未安装")
                print("💡 请先安装Facebook应用")
                return False
            
            print("✅ Facebook应用已安装")
            
            # 检查Facebook应用数据目录是否存在
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'ls -la {self.facebook_data_path}'")
            if not success or "No such file" in result:
                print("❌ Facebook应用数据目录不存在")
                print("💡 请先运行Facebook应用并登录")
                return False
            
            print("✅ Facebook应用数据目录存在")
            
            # 尝试设置权限
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'chmod -R 755 {self.facebook_data_path}'")
            if success:
                print("✅ 数据目录权限设置成功")
            else:
                print("⚠️  数据目录权限设置失败，但继续尝试")
            
            print("✅ 前提条件验证完成")
            print()
            return True
            
        except Exception as e:
            log_error(f"[Facebook 2FA] 前提条件验证失败: {e}", component="FacebookExtractor")
            return False

    async def _scan_facebook_data_directory(self):
        """扫描Facebook应用数据目录"""
        try:
            print("📁 扫描Facebook应用数据目录...")
            print("-" * 40)
            
            # 显示数据目录结构
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {self.facebook_data_path} -type d | head -20'")
            if success and result.strip():
                directories = result.strip().split('\n')
                print(f"📊 找到 {len(directories)} 个子目录:")
                for directory in directories[:10]:  # 只显示前10个
                    dir_name = directory.replace(self.facebook_data_path, "").lstrip('/')
                    if dir_name:
                        print(f"   📁 {dir_name}")
                if len(directories) > 10:
                    print(f"   ... 还有 {len(directories) - 10} 个目录")
            
            # 扫描每个目标位置
            for location in self.target_locations:
                full_path = f"{self.facebook_data_path}/{location}"
                print(f"\n🔍 扫描位置: {location}")
                
                # 检查目录是否存在
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'ls -la {full_path}'")
                if success and "No such file" not in result:
                    print(f"   ✅ 目录存在: {full_path}")
                    
                    # 列出文件
                    success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {full_path} -type f | head -10'")
                    if success2 and result2.strip():
                        files = result2.strip().split('\n')
                        print(f"   📄 找到 {len(files)} 个文件:")
                        for file_path in files[:5]:  # 只显示前5个
                            file_name = file_path.split('/')[-1]
                            print(f"      📄 {file_name}")
                        if len(files) > 5:
                            print(f"      ... 还有 {len(files) - 5} 个文件")
                    else:
                        print(f"   📄 目录为空")
                else:
                    print(f"   ❌ 目录不存在: {full_path}")
            
            print("\n✅ 数据目录扫描完成")
            print()
            
        except Exception as e:
            log_error(f"[Facebook 2FA] 数据目录扫描失败: {e}", component="FacebookExtractor")

    async def _extract_facebook_2fa_secrets(self) -> List[Dict[str, Any]]:
        """提取Facebook 2FA密钥"""
        try:
            print("🔑 提取Facebook 2FA密钥...")
            print("-" * 40)
            
            all_secrets = []
            
            # 方法1：数据库文件分析
            print("📊 方法1: 数据库文件分析")
            secrets1 = await self._extract_from_databases()
            all_secrets.extend(secrets1)
            print(f"   结果: 找到 {len(secrets1)} 个密钥")
            
            # 方法2：SharedPreferences文件分析
            print("📋 方法2: SharedPreferences文件分析")
            secrets2 = await self._extract_from_shared_prefs()
            all_secrets.extend(secrets2)
            print(f"   结果: 找到 {len(secrets2)} 个密钥")
            
            # 方法3：缓存和临时文件分析
            print("🗂️  方法3: 缓存和临时文件分析")
            secrets3 = await self._extract_from_cache_files()
            all_secrets.extend(secrets3)
            print(f"   结果: 找到 {len(secrets3)} 个密钥")
            
            # 方法4：全局字符串搜索
            print("🔤 方法4: 全局字符串搜索")
            secrets4 = await self._extract_from_global_search()
            all_secrets.extend(secrets4)
            print(f"   结果: 找到 {len(secrets4)} 个密钥")
            
            # 方法5：二进制文件分析
            print("🔬 方法5: 二进制文件分析")
            secrets5 = await self._extract_from_binary_files()
            all_secrets.extend(secrets5)
            print(f"   结果: 找到 {len(secrets5)} 个密钥")
            
            print(f"\n✅ Facebook 2FA密钥提取完成: 总共找到 {len(all_secrets)} 个可能的密钥")
            print()
            return all_secrets
            
        except Exception as e:
            log_error(f"[Facebook 2FA] 密钥提取失败: {e}", component="FacebookExtractor")
            return []

    async def _extract_from_databases(self) -> List[Dict[str, Any]]:
        """从数据库文件提取密钥"""
        try:
            secrets = []
            db_path = f"{self.facebook_data_path}/databases"
            
            # 查找所有数据库文件
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {db_path} -name \"*.db\" 2>/dev/null'")
            if success and result.strip():
                db_files = result.strip().split('\n')
                print(f"   📊 找到 {len(db_files)} 个数据库文件")
                
                for db_file in db_files:
                    if db_file.strip():
                        print(f"   🔍 分析数据库: {db_file.split('/')[-1]}")
                        
                        # 方法1: 使用sqlite3查询
                        success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'sqlite3 {db_file} \".tables\"'")
                        if success2 and result2.strip():
                            tables = result2.strip().split()
                            print(f"      📋 找到 {len(tables)} 个表")
                            
                            for table in tables:
                                if any(keyword in table.lower() for keyword in ['auth', '2fa', 'totp', 'secret', 'token', 'key']):
                                    print(f"      🎯 分析可疑表: {table}")
                                    
                                    # 查询表内容
                                    success3, result3 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'sqlite3 {db_file} \"SELECT * FROM {table}\"'")
                                    if success3 and result3.strip():
                                        # 搜索Base32模式
                                        base32_matches = re.findall(r'[A-Z2-7]{16,}', result3)
                                        for match in base32_matches:
                                            if self._is_valid_base32_key(match):
                                                secrets.append({
                                                    'secret_key': match,
                                                    'source': f'database:{db_file.split("/")[-1]}:{table}',
                                                    'method': 'database_query',
                                                    'confidence': 'high'
                                                })
                                                print(f"      🔑 发现密钥: {match[:8]}...")
                        
                        # 方法2: 使用strings命令
                        success4, result4 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'strings {db_file} | grep -E \"[A-Z2-7]{{16,}}\"'")
                        if success4 and result4.strip():
                            potential_keys = result4.strip().split('\n')
                            for key in potential_keys:
                                if self._is_valid_base32_key(key.strip()):
                                    secrets.append({
                                        'secret_key': key.strip(),
                                        'source': f'database_strings:{db_file.split("/")[-1]}',
                                        'method': 'database_strings',
                                        'confidence': 'medium'
                                    })
                                    print(f"      🔑 发现密钥: {key.strip()[:8]}...")
            else:
                print("   ❌ 未找到数据库文件")
            
            return secrets
            
        except Exception as e:
            log_error(f"[Facebook 2FA] 数据库提取失败: {e}", component="FacebookExtractor")
            return []

    async def _extract_from_shared_prefs(self) -> List[Dict[str, Any]]:
        """从SharedPreferences文件提取密钥"""
        try:
            secrets = []
            prefs_path = f"{self.facebook_data_path}/shared_prefs"
            
            # 查找所有XML文件
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {prefs_path} -name \"*.xml\" 2>/dev/null'")
            if success and result.strip():
                xml_files = result.strip().split('\n')
                print(f"   📋 找到 {len(xml_files)} 个SharedPreferences文件")
                
                for xml_file in xml_files:
                    if xml_file.strip():
                        file_name = xml_file.split('/')[-1]
                        print(f"   🔍 分析文件: {file_name}")
                        
                        # 读取XML文件内容
                        success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'cat {xml_file}'")
                        if success2 and result2.strip():
                            xml_content = result2.strip()
                            
                            # 搜索Base32模式
                            base32_matches = re.findall(r'[A-Z2-7]{16,}', xml_content)
                            for match in base32_matches:
                                if self._is_valid_base32_key(match):
                                    secrets.append({
                                        'secret_key': match,
                                        'source': f'shared_prefs:{file_name}',
                                        'method': 'shared_preferences',
                                        'confidence': 'high'
                                    })
                                    print(f"      🔑 发现密钥: {match[:8]}...")
                            
                            # 搜索可能的加密密钥
                            if any(keyword in xml_content.lower() for keyword in ['auth', '2fa', 'totp', 'secret', 'token']):
                                print(f"      🎯 发现可疑内容关键词")
                                # 可以进一步分析这个文件
            else:
                print("   ❌ 未找到SharedPreferences文件")
            
            return secrets
            
        except Exception as e:
            log_error(f"[Facebook 2FA] SharedPreferences提取失败: {e}", component="FacebookExtractor")
            return []

    async def _extract_from_cache_files(self) -> List[Dict[str, Any]]:
        """从缓存和临时文件提取密钥"""
        try:
            secrets = []
            
            cache_paths = [
                f"{self.facebook_data_path}/cache",
                f"{self.facebook_data_path}/files",
                f"{self.facebook_data_path}/code_cache",
                f"{self.facebook_data_path}/no_backup"
            ]
            
            for cache_path in cache_paths:
                if "cache" in cache_path or "files" in cache_path:
                    print(f"   🔍 搜索路径: {cache_path.split('/')[-1]}")
                    
                    # 搜索所有文件中的Base32模式
                    success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {cache_path} -type f -exec grep -l \"[A-Z2-7]{{16,}}\" {{}} \\; 2>/dev/null'")
                    if success and result.strip():
                        files = result.strip().split('\n')
                        print(f"      📄 找到 {len(files)} 个可能包含密钥的文件")
                        
                        for file_path in files[:10]:  # 限制处理前10个文件
                            if file_path.strip():
                                # 提取文件中的Base32密钥
                                success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'grep -oE \"[A-Z2-7]{{16,}}\" {file_path}'")
                                if success2 and result2.strip():
                                    potential_keys = result2.strip().split('\n')
                                    for key in potential_keys:
                                        if self._is_valid_base32_key(key.strip()):
                                            secrets.append({
                                                'secret_key': key.strip(),
                                                'source': f'cache:{file_path.split("/")[-1]}',
                                                'method': 'cache_file_analysis',
                                                'confidence': 'medium'
                                            })
                                            print(f"      🔑 发现密钥: {key.strip()[:8]}...")
                    else:
                        print(f"      ❌ 未找到相关文件")
            
            return secrets
            
        except Exception as e:
            log_error(f"[Facebook 2FA] 缓存文件提取失败: {e}", component="FacebookExtractor")
            return []

    async def _extract_from_global_search(self) -> List[Dict[str, Any]]:
        """全局字符串搜索"""
        try:
            secrets = []

            print(f"   🔍 在整个Facebook数据目录中搜索Base32模式...")

            # 在整个Facebook数据目录中搜索Base32模式
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {self.facebook_data_path} -type f -exec grep -l \"[A-Z2-7]{{16,}}\" {{}} \\; 2>/dev/null'")
            if success and result.strip():
                files = result.strip().split('\n')
                print(f"   📄 找到 {len(files)} 个可能包含密钥的文件")

                for file_path in files[:20]:  # 限制处理前20个文件
                    if file_path.strip():
                        file_name = file_path.split('/')[-1]
                        print(f"      🔍 分析文件: {file_name}")

                        # 提取文件中的所有Base32模式
                        success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'grep -oE \"[A-Z2-7]{{16,}}\" {file_path} | head -10'")
                        if success2 and result2.strip():
                            potential_keys = result2.strip().split('\n')
                            for key in potential_keys:
                                if self._is_valid_base32_key(key.strip()):
                                    secrets.append({
                                        'secret_key': key.strip(),
                                        'source': f'global_search:{file_name}',
                                        'method': 'global_string_search',
                                        'confidence': 'medium'
                                    })
                                    print(f"         🔑 发现密钥: {key.strip()[:8]}...")
            else:
                print("   ❌ 全局搜索未找到相关文件")

            return secrets

        except Exception as e:
            log_error(f"[Facebook 2FA] 全局搜索失败: {e}", component="FacebookExtractor")
            return []

    async def _extract_from_binary_files(self) -> List[Dict[str, Any]]:
        """从二进制文件提取密钥"""
        try:
            secrets = []

            print(f"   🔍 搜索二进制文件...")

            # 查找可能的二进制文件
            binary_extensions = ['*.so', '*.dex', '*.jar', '*.apk']

            for extension in binary_extensions:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {self.facebook_data_path} -name \"{extension}\" 2>/dev/null'")
                if success and result.strip():
                    binary_files = result.strip().split('\n')
                    print(f"      📄 找到 {len(binary_files)} 个 {extension} 文件")

                    for binary_file in binary_files[:5]:  # 限制处理前5个文件
                        if binary_file.strip():
                            file_name = binary_file.split('/')[-1]
                            print(f"         🔍 分析二进制文件: {file_name}")

                            # 使用strings命令提取可读字符串
                            success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'strings {binary_file} | grep -E \"[A-Z2-7]{{16,}}\" | head -5'")
                            if success2 and result2.strip():
                                potential_keys = result2.strip().split('\n')
                                for key in potential_keys:
                                    if self._is_valid_base32_key(key.strip()):
                                        secrets.append({
                                            'secret_key': key.strip(),
                                            'source': f'binary:{file_name}',
                                            'method': 'binary_strings_extraction',
                                            'confidence': 'low'
                                        })
                                        print(f"            🔑 发现密钥: {key.strip()[:8]}...")

            return secrets

        except Exception as e:
            log_error(f"[Facebook 2FA] 二进制文件提取失败: {e}", component="FacebookExtractor")
            return []

    def _is_valid_base32_key(self, key: str) -> bool:
        """验证是否为有效的Base32密钥"""
        try:
            if not key or len(key) < 16:
                return False

            # 清理密钥
            cleaned = re.sub(r'[^A-Z2-7]', '', key.upper())
            if len(cleaned) < 16:
                return False

            # 验证Base32字符集
            if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in cleaned):
                return False

            # 尝试解码
            try:
                base64.b32decode(cleaned)
                return True
            except Exception:
                return False

        except Exception:
            return False

    def _generate_totp(self, secret: str) -> str:
        """生成TOTP验证码"""
        try:
            import hmac
            import hashlib
            import struct
            import time

            # 解码Base32密钥
            key = base64.b32decode(secret)

            # 获取当前时间戳
            timestamp = int(time.time()) // 30

            # 生成TOTP
            msg = struct.pack('>Q', timestamp)
            hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
            offset = hmac_digest[-1] & 0x0f
            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
            code = (code & 0x7fffffff) % 1000000

            return f"{code:06d}"

        except Exception as e:
            log_error(f"[Facebook 2FA] TOTP生成失败: {e}", component="FacebookExtractor")
            return "000000"

    async def _verify_and_save_results(self, all_secrets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证和保存结果"""
        try:
            print("🔍 验证提取的密钥...")
            print("-" * 40)

            # 去重
            unique_secrets = []
            seen_keys = set()

            for secret in all_secrets:
                key = secret.get('secret_key', '')
                if key and key not in seen_keys:
                    seen_keys.add(key)

                    # 验证密钥并生成测试TOTP
                    if self._is_valid_base32_key(key):
                        try:
                            current_code = self._generate_totp(key)

                            secret['test_totp'] = current_code
                            secret['verified'] = True
                            secret['key_length'] = len(key)

                            print(f"✅ 验证密钥: {key[:8]}...")
                            print(f"   来源: {secret.get('source', 'unknown')}")
                            print(f"   方法: {secret.get('method', 'unknown')}")
                            print(f"   当前验证码: {current_code}")
                            print(f"   置信度: {secret.get('confidence', 'unknown')}")
                            print()

                        except Exception as e:
                            secret['verified'] = False
                            secret['error'] = str(e)
                            print(f"❌ 密钥验证失败: {key[:8]}... - {e}")

                        unique_secrets.append(secret)

            # 按置信度排序
            confidence_order = {'high': 3, 'medium': 2, 'low': 1}
            unique_secrets.sort(key=lambda x: confidence_order.get(x.get('confidence', 'low'), 0), reverse=True)

            # 保存结果
            if unique_secrets:
                await self._save_extraction_results(unique_secrets)

            print(f"✅ 验证完成: {len(unique_secrets)} 个有效密钥")
            print()
            return unique_secrets

        except Exception as e:
            log_error(f"[Facebook 2FA] 验证结果失败: {e}", component="FacebookExtractor")
            return []

    async def _save_extraction_results(self, secrets: List[Dict[str, Any]]):
        """保存提取结果"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存到JSON文件
            output_dir = Path("./fb_2fa_results/facebook_extraction/")
            output_dir.mkdir(parents=True, exist_ok=True)

            json_file = output_dir / f"facebook_2fa_secrets_{self.emulator_id}_{timestamp}.json"

            result_data = {
                'emulator_id': self.emulator_id,
                'extraction_time': datetime.datetime.now().isoformat(),
                'extraction_method': 'facebook_specific_extraction',
                'facebook_package': self.facebook_package,
                'total_secrets': len(secrets),
                'secrets': secrets
            }

            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)

            # 保存为可导入格式
            import_file = output_dir / f"facebook_2fa_import_{self.emulator_id}_{timestamp}.txt"

            with open(import_file, 'w', encoding='utf-8') as f:
                f.write("# Facebook 2FA密钥提取结果\n")
                f.write(f"# 提取时间: {result_data['extraction_time']}\n")
                f.write(f"# 模拟器ID: {self.emulator_id}\n")
                f.write("# " + "=" * 50 + "\n\n")

                for i, secret in enumerate(secrets, 1):
                    f.write(f"密钥 {i}:\n")
                    f.write(f"密钥: {secret['secret_key']}\n")
                    f.write(f"来源: {secret.get('source', 'unknown')}\n")
                    f.write(f"提取方法: {secret.get('method', 'unknown')}\n")
                    f.write(f"当前验证码: {secret.get('test_totp', 'N/A')}\n")
                    f.write(f"置信度: {secret.get('confidence', 'unknown')}\n")
                    f.write(f"密钥长度: {secret.get('key_length', 0)} 字符\n")
                    f.write("-" * 30 + "\n")

            print(f"💾 结果已保存:")
            print(f"   📄 详细结果: {json_file}")
            print(f"   📄 导入文件: {import_file}")
            print()

        except Exception as e:
            log_error(f"[Facebook 2FA] 保存结果失败: {e}", component="FacebookExtractor")

    async def _display_final_results(self, secrets: List[Dict[str, Any]]):
        """显示最终结果"""
        try:
            print("🎯 Facebook 2FA密钥提取结果")
            print("=" * 50)

            if not secrets:
                print("❌ 未找到任何Facebook 2FA密钥")
                print("\n💡 可能的原因:")
                print("1. Facebook账户未启用2FA功能")
                print("2. 2FA密钥存储在我们未扫描到的位置")
                print("3. 2FA密钥被加密存储")
                print("4. 需要先在Facebook中进行2FA相关操作")
                print("\n🔧 建议:")
                print("1. 确保Facebook应用已登录")
                print("2. 在Facebook中访问安全设置")
                print("3. 尝试设置或重新设置2FA")
                print("4. 重新运行提取工具")
                return

            print(f"✅ 成功找到 {len(secrets)} 个Facebook 2FA密钥:")
            print()

            for i, secret in enumerate(secrets, 1):
                confidence = secret.get('confidence', 'unknown').upper()
                print(f"🔑 密钥 {i} (置信度: {confidence}):")
                print(f"   密钥: {secret['secret_key']}")
                print(f"   来源: {secret.get('source', 'unknown')}")
                print(f"   提取方法: {secret.get('method', 'unknown')}")
                print(f"   当前验证码: {secret.get('test_totp', 'N/A')}")
                print(f"   密钥长度: {secret.get('key_length', 0)} 字符")

                if secret.get('verified'):
                    print(f"   ✅ 验证状态: 有效")
                else:
                    print(f"   ❌ 验证状态: 无效")
                    if secret.get('error'):
                        print(f"   ⚠️  错误: {secret['error']}")

                print("-" * 30)

            print("\n🎉 Facebook 2FA密钥提取成功!")
            print("\n📋 使用指导:")
            print("1. 将提取的密钥添加到认证器应用中")
            print("2. 验证生成的验证码是否与Facebook要求的一致")
            print("3. 如果验证成功，说明成功提取了真实的2FA密钥")
            print("4. 建议立即更新Facebook的2FA设置")
            print("5. 妥善保管这些密钥，以防再次丢失")

        except Exception as e:
            log_error(f"[Facebook 2FA] 显示结果失败: {e}", component="FacebookExtractor")

    async def _cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir, ignore_errors=True)
        except Exception:
            pass

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2

        print("🔓 Facebook 2FA密钥专用提取工具")
        print("⚠️  专门从Facebook应用中提取2FA密钥")
        print("⚠️  需要root权限访问应用数据")
        print("⚠️  仅用于合法的账户恢复目的")
        print()

        confirm = input("确认继续Facebook 2FA提取? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return

        # 创建Facebook 2FA提取器
        extractor = FacebookTwoFactorExtractor(emulator_id)

        # 运行Facebook 2FA提取
        await extractor.run_facebook_extraction()

        print("\n" + "=" * 50)
        print("✅ Facebook 2FA密钥提取完成")

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
