#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔢 Facebook 2FA密钥测试工具
========================================
功能描述: 测试2FA密钥并生成验证码

主要功能:
1. 验证2FA密钥格式
2. 生成TOTP验证码
3. 模拟密钥存储到模拟器
4. 测试提取功能

使用方法:
python fb/test_2fa_secret.py

注意事项:
- 用于测试2FA提取功能
- 可以使用示例密钥进行测试
========================================
"""

import asyncio
import sys
import time
import hmac
import hashlib
import struct
import base64
import json
from pathlib import Path
from typing import Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class TwoFactorSecretTester:
    """2FA密钥测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.output_dir = Path("./fb_2fa_results/test_secrets/")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 示例密钥 (仅用于测试)
        self.example_secrets = [
            "JBSWY3DPEHPK3PXP",  # 标准测试密钥
            "GEZDGNBVGY3TQOJQGEZDGNBVGY3TQOJQ",  # 长密钥
            "MFRGG2LTMVZXIZLBMFRGG2LTMVZXIZLB",  # 另一个测试密钥
        ]
        
        log_info("[密钥测试] 2FA密钥测试器初始化完成", component="SecretTester")

    async def run_secret_test(self):
        """运行密钥测试"""
        try:
            print("🔢 Facebook 2FA密钥测试工具")
            print("=" * 50)
            
            # 显示测试选项
            await self._show_test_options()
            
            # 获取用户选择
            choice = await self._get_user_choice()
            
            if choice == "1":
                await self._test_example_secrets()
            elif choice == "2":
                await self._test_custom_secret()
            elif choice == "3":
                await self._simulate_secret_in_emulator()
            elif choice == "4":
                await self._test_extraction_function()
            else:
                print("❌ 无效选择")
                return
            
        except Exception as e:
            log_error(f"[密钥测试] 密钥测试失败: {e}", component="SecretTester")

    async def _show_test_options(self):
        """显示测试选项"""
        print("\n📋 测试选项:")
        print("1. 🧪 测试示例密钥")
        print("2. 🔑 测试自定义密钥")
        print("3. 📱 模拟密钥到模拟器")
        print("4. 🔍 测试提取功能")
        print()

    async def _get_user_choice(self) -> str:
        """获取用户选择"""
        while True:
            try:
                choice = input("请选择测试选项 (1-4): ").strip()
                if choice in ["1", "2", "3", "4"]:
                    return choice
                else:
                    print("❌ 请输入有效选项 (1-4)")
            except KeyboardInterrupt:
                print("\n⚠️ 操作被取消")
                sys.exit(0)

    async def _test_example_secrets(self):
        """测试示例密钥"""
        try:
            print("\n🧪 测试示例密钥")
            print("=" * 40)
            
            for i, secret in enumerate(self.example_secrets, 1):
                print(f"\n📋 测试密钥 {i}:")
                print(f"密钥: {secret}")
                
                # 验证密钥
                is_valid = await self._validate_secret(secret)
                if is_valid:
                    # 生成验证码
                    totp_code = self._generate_totp(secret)
                    print(f"✅ 密钥有效")
                    print(f"🔢 当前验证码: {totp_code:06d}")
                    
                    # 保存测试密钥
                    await self._save_test_secret(secret, f"example_{i}")
                else:
                    print(f"❌ 密钥无效")
                
                print("-" * 30)
            
        except Exception as e:
            log_error(f"[密钥测试] 示例密钥测试失败: {e}", component="SecretTester")

    async def _test_custom_secret(self):
        """测试自定义密钥"""
        try:
            print("\n🔑 测试自定义密钥")
            print("=" * 40)
            
            secret = input("请输入您的2FA密钥: ").strip()
            
            if not secret:
                print("❌ 未输入密钥")
                return
            
            # 清理密钥格式
            cleaned_secret = self._clean_secret_key(secret)
            if not cleaned_secret:
                print("❌ 密钥格式无效")
                return
            
            print(f"原始密钥: {secret}")
            print(f"清理后密钥: {cleaned_secret}")
            
            # 验证密钥
            is_valid = await self._validate_secret(cleaned_secret)
            if is_valid:
                # 生成验证码
                totp_code = self._generate_totp(cleaned_secret)
                print(f"✅ 密钥有效")
                print(f"🔢 当前验证码: {totp_code:06d}")
                
                # 保存测试密钥
                await self._save_test_secret(cleaned_secret, "custom")
                
                # 询问是否要持续显示验证码
                show_continuous = input("\n是否要持续显示验证码? (y/N): ").strip().lower()
                if show_continuous == 'y':
                    await self._show_continuous_totp(cleaned_secret)
            else:
                print(f"❌ 密钥无效")
            
        except Exception as e:
            log_error(f"[密钥测试] 自定义密钥测试失败: {e}", component="SecretTester")

    async def _simulate_secret_in_emulator(self):
        """模拟密钥到模拟器"""
        try:
            print("\n📱 模拟密钥到模拟器")
            print("=" * 40)
            
            # 获取模拟器ID
            emulator_id = input("请输入模拟器ID (默认2): ").strip()
            if not emulator_id:
                emulator_id = "2"
            
            try:
                emulator_id = int(emulator_id)
            except ValueError:
                print("❌ 模拟器ID必须是数字")
                return
            
            # 选择密钥
            print("\n选择要模拟的密钥:")
            print("1. 使用示例密钥")
            print("2. 输入自定义密钥")
            
            choice = input("请选择 (1-2): ").strip()
            
            if choice == "1":
                secret = self.example_secrets[0]  # 使用第一个示例密钥
                print(f"使用示例密钥: {secret}")
            elif choice == "2":
                secret = input("请输入密钥: ").strip()
                secret = self._clean_secret_key(secret)
                if not secret:
                    print("❌ 密钥格式无效")
                    return
            else:
                print("❌ 无效选择")
                return
            
            # 模拟写入密钥到模拟器
            await self._write_secret_to_emulator(emulator_id, secret)
            
        except Exception as e:
            log_error(f"[密钥测试] 模拟密钥到模拟器失败: {e}", component="SecretTester")

    async def _test_extraction_function(self):
        """测试提取功能"""
        try:
            print("\n🔍 测试提取功能")
            print("=" * 40)
            
            # 获取模拟器ID
            emulator_id = input("请输入模拟器ID (默认2): ").strip()
            if not emulator_id:
                emulator_id = "2"
            
            try:
                emulator_id = int(emulator_id)
            except ValueError:
                print("❌ 模拟器ID必须是数字")
                return
            
            print(f"🔍 测试模拟器{emulator_id}的2FA提取功能...")
            
            # 创建任务执行器
            task = FacebookTwoFactorTask(emulator_id)
            
            # 执行提取
            result = await task.execute()
            
            # 显示结果
            print(f"\n📊 提取结果:")
            print(f"成功: {result.get('success', False)}")
            print(f"执行时间: {result.get('execution_time', 0):.2f}秒")
            
            if result.get('success'):
                extraction_result = result.get('extraction_result', {})
                secrets_count = len(extraction_result.get('secrets', []))
                print(f"找到密钥数量: {secrets_count}")
                
                if secrets_count > 0:
                    print(f"密钥详情:")
                    for i, secret in enumerate(extraction_result['secrets'], 1):
                        print(f"  {i}. {secret.get('service_name', 'Unknown')} - {secret.get('secret_key', '')[:8]}...")
            else:
                print(f"错误信息: {result.get('error_message', '')}")
            
        except Exception as e:
            log_error(f"[密钥测试] 提取功能测试失败: {e}", component="SecretTester")

    async def _validate_secret(self, secret: str) -> bool:
        """验证密钥格式"""
        try:
            # 检查Base32格式
            if len(secret) < 16:
                return False
            
            if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in secret):
                return False
            
            # 尝试解码
            try:
                base64.b32decode(secret)
                return True
            except Exception:
                return False
                
        except Exception:
            return False

    def _generate_totp(self, secret: str, timestamp: Optional[int] = None) -> int:
        """生成TOTP验证码"""
        try:
            # 解码Base32密钥
            key = base64.b32decode(secret)
            
            # 获取时间戳
            if timestamp is None:
                timestamp = int(time.time()) // 30
            
            # 生成TOTP
            msg = struct.pack('>Q', timestamp)
            hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
            offset = hmac_digest[-1] & 0x0f
            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
            code = (code & 0x7fffffff) % 1000000
            
            return code
            
        except Exception as e:
            log_error(f"[密钥测试] TOTP生成失败: {e}", component="SecretTester")
            return 0

    def _clean_secret_key(self, secret_key: str) -> str:
        """清理密钥格式"""
        try:
            import re
            # 移除空格和特殊字符
            cleaned = re.sub(r'[^A-Z2-7]', '', secret_key.upper())
            
            # 验证Base32格式
            if len(cleaned) >= 16 and all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in cleaned):
                return cleaned
            
            return ""
            
        except Exception:
            return ""

    async def _save_test_secret(self, secret: str, source: str):
        """保存测试密钥"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            
            secret_data = {
                'secret_key': secret,
                'source': f"test_{source}",
                'service': 'Facebook',
                'timestamp': datetime.datetime.now().isoformat(),
                'length': len(secret),
                'current_totp': self._generate_totp(secret)
            }
            
            json_file = self.output_dir / f"test_secret_{source}_{timestamp}.json"
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(secret_data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 测试密钥已保存: {json_file}")
            
        except Exception as e:
            log_error(f"[密钥测试] 保存测试密钥失败: {e}", component="SecretTester")

    async def _show_continuous_totp(self, secret: str):
        """持续显示TOTP验证码"""
        try:
            print("\n🔢 持续显示验证码 (按Ctrl+C停止):")
            print("=" * 40)
            
            while True:
                current_time = int(time.time())
                time_in_period = current_time % 30
                remaining_time = 30 - time_in_period
                
                totp_code = self._generate_totp(secret)
                
                print(f"\r🔢 验证码: {totp_code:06d} | ⏰ 剩余: {remaining_time:02d}秒", end="", flush=True)
                
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            print("\n⚠️ 停止显示验证码")
        except Exception as e:
            log_error(f"[密钥测试] 持续显示验证码失败: {e}", component="SecretTester")

    async def _write_secret_to_emulator(self, emulator_id: int, secret: str):
        """写入密钥到模拟器 (模拟)"""
        try:
            print(f"📱 正在模拟写入密钥到模拟器{emulator_id}...")
            
            # 创建模拟的Facebook数据文件
            task = FacebookTwoFactorTask(emulator_id)
            
            if not task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 创建模拟的2FA数据文件
            mock_data = {
                'service_name': 'Facebook',
                'account': '<EMAIL>',
                'secret_key': secret,
                'type': 'TOTP'
            }
            
            # 写入到临时文件
            temp_file = f"/sdcard/facebook_2fa_test.json"
            json_content = json.dumps(mock_data)
            
            success, result = task.ld.execute_ld(emulator_id, f"echo '{json_content}' > {temp_file}")
            
            if success:
                print(f"✅ 模拟密钥已写入模拟器")
                print(f"📄 文件位置: {temp_file}")
                print(f"🔑 密钥: {secret}")
                print(f"🔢 当前验证码: {self._generate_totp(secret):06d}")
                
                # 验证文件是否写入成功
                success2, content = task.ld.execute_ld(emulator_id, f"cat {temp_file}")
                if success2 and secret in content:
                    print(f"✅ 验证成功: 密钥已正确写入")
                else:
                    print(f"⚠️ 验证失败: 无法读取写入的密钥")
            else:
                print(f"❌ 写入失败: {result}")
            
        except Exception as e:
            log_error(f"[密钥测试] 写入密钥到模拟器失败: {e}", component="SecretTester")

async def main():
    """主函数"""
    try:
        # 创建测试器
        tester = TwoFactorSecretTester()
        
        # 运行测试
        await tester.run_secret_test()
        
        print("\n" + "=" * 50)
        print("✅ 密钥测试完成")
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
