#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Facebook 2FA提取功能测试脚本
========================================
功能描述: 测试Facebook双因子认证密钥提取功能的完整流程

主要功能:
1. 测试FB 2FA提取器的各种方法
2. 验证数据解析和格式转换
3. 测试任务执行器的完整流程
4. 生成测试报告和结果验证

使用方法:
python fb/test_fb_2fa_extraction.py [emulator_id]

注意事项:
- 需要模拟器正在运行
- 建议在有Facebook应用或认证器应用的模拟器上测试
- 测试过程中会创建临时文件，测试完成后会自动清理
========================================
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from fb.fb_2fa_extractor import FacebookTwoFactorExtractor
from core.logger_manager import log_info, log_error, log_warning

class FacebookTwoFactorTestSuite:
    """Facebook 2FA提取功能测试套件"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化测试套件
        
        Args:
            emulator_id: 测试用的模拟器ID
        """
        self.emulator_id = emulator_id
        self.test_results = []
        
        log_info(f"[测试] Facebook 2FA提取功能测试套件初始化 - 模拟器{emulator_id}", component="FB2FATestSuite")

    async def run_all_tests(self):
        """运行所有测试"""
        try:
            log_info(f"[测试] 开始运行Facebook 2FA提取功能完整测试", component="FB2FATestSuite")
            
            # 测试1: 基础连接测试
            await self._test_basic_connection()
            
            # 测试2: 提取器功能测试
            await self._test_extractor_functionality()
            
            # 测试3: 任务执行器测试
            await self._test_task_executor()
            
            # 测试4: 数据格式验证测试
            await self._test_data_format_validation()
            
            # 生成测试报告
            self._generate_test_report()
            
            log_info(f"[测试] Facebook 2FA提取功能测试完成", component="FB2FATestSuite")
            
        except Exception as e:
            log_error(f"[测试] 测试执行失败: {e}", component="FB2FATestSuite")

    async def _test_basic_connection(self):
        """测试1: 基础连接测试"""
        try:
            log_info(f"[测试1] 开始基础连接测试", component="FB2FATestSuite")
            
            # 创建提取器实例
            extractor = FacebookTwoFactorExtractor(self.emulator_id)
            
            # 测试ADB连接
            result = extractor.ld.adb(self.emulator_id, "echo 'connection_test'")
            connection_ok = "connection_test" in result
            
            # 测试应用检查
            fb_installed = extractor._is_app_installed("com.facebook.katana")
            auth_installed = extractor._is_app_installed("com.google.android.apps.authenticator2")
            
            test_result = {
                'test_name': '基础连接测试',
                'success': connection_ok,
                'details': {
                    'adb_connection': connection_ok,
                    'facebook_installed': fb_installed,
                    'authenticator_installed': auth_installed
                }
            }
            
            self.test_results.append(test_result)
            
            log_info(f"[测试1] 基础连接测试完成 - {'成功' if connection_ok else '失败'}", component="FB2FATestSuite")
            log_info(f"[测试1] Facebook应用: {'已安装' if fb_installed else '未安装'}", component="FB2FATestSuite")
            log_info(f"[测试1] 认证器应用: {'已安装' if auth_installed else '未安装'}", component="FB2FATestSuite")
            
            # 清理
            extractor.cleanup()
            
        except Exception as e:
            log_error(f"[测试1] 基础连接测试失败: {e}", component="FB2FATestSuite")
            self.test_results.append({
                'test_name': '基础连接测试',
                'success': False,
                'error': str(e)
            })

    async def _test_extractor_functionality(self):
        """测试2: 提取器功能测试"""
        try:
            log_info(f"[测试2] 开始提取器功能测试", component="FB2FATestSuite")
            
            # 创建提取器实例
            extractor = FacebookTwoFactorExtractor(self.emulator_id)
            
            # 执行提取
            extraction_result = extractor.extract_2fa_secrets()
            
            test_result = {
                'test_name': '提取器功能测试',
                'success': extraction_result.success,
                'details': {
                    'secrets_found': len(extraction_result.secrets),
                    'extraction_method': extraction_result.extraction_method,
                    'error_message': extraction_result.error_message
                }
            }
            
            self.test_results.append(test_result)
            
            log_info(f"[测试2] 提取器功能测试完成 - {'成功' if extraction_result.success else '失败'}", component="FB2FATestSuite")
            log_info(f"[测试2] 找到密钥数量: {len(extraction_result.secrets)}", component="FB2FATestSuite")
            log_info(f"[测试2] 提取方法: {extraction_result.extraction_method}", component="FB2FATestSuite")
            
            if extraction_result.secrets:
                log_info(f"[测试2] 密钥详情:", component="FB2FATestSuite")
                for i, secret in enumerate(extraction_result.secrets, 1):
                    log_info(f"[测试2]   {i}. {secret.service_name} - {secret.account} - {secret.secret_key[:8]}...", component="FB2FATestSuite")
            
            # 清理
            extractor.cleanup()
            
        except Exception as e:
            log_error(f"[测试2] 提取器功能测试失败: {e}", component="FB2FATestSuite")
            self.test_results.append({
                'test_name': '提取器功能测试',
                'success': False,
                'error': str(e)
            })

    async def _test_task_executor(self):
        """测试3: 任务执行器测试"""
        try:
            log_info(f"[测试3] 开始任务执行器测试", component="FB2FATestSuite")
            
            # 创建任务执行器实例
            task = FacebookTwoFactorTask(self.emulator_id)
            
            # 执行任务
            task_result = await task.execute()
            
            test_result = {
                'test_name': '任务执行器测试',
                'success': task_result.get('success', False),
                'details': {
                    'execution_time': task_result.get('execution_time', 0),
                    'error_message': task_result.get('error_message', ''),
                    'extraction_result': task_result.get('extraction_result', {})
                }
            }
            
            self.test_results.append(test_result)
            
            log_info(f"[测试3] 任务执行器测试完成 - {'成功' if task_result.get('success') else '失败'}", component="FB2FATestSuite")
            log_info(f"[测试3] 执行时间: {task_result.get('execution_time', 0):.2f}秒", component="FB2FATestSuite")
            
            if task_result.get('error_message'):
                log_info(f"[测试3] 错误信息: {task_result.get('error_message')}", component="FB2FATestSuite")
            
        except Exception as e:
            log_error(f"[测试3] 任务执行器测试失败: {e}", component="FB2FATestSuite")
            self.test_results.append({
                'test_name': '任务执行器测试',
                'success': False,
                'error': str(e)
            })

    async def _test_data_format_validation(self):
        """测试4: 数据格式验证测试"""
        try:
            log_info(f"[测试4] 开始数据格式验证测试", component="FB2FATestSuite")
            
            # 创建提取器实例
            extractor = FacebookTwoFactorExtractor(self.emulator_id)
            
            # 测试密钥清理功能
            test_keys = [
                "JBSWY3DPEHPK3PXP",  # 有效的Base32密钥
                "jbswy3dpehpk3pxp",  # 小写密钥
                "JBSWY3DP EHPK3PXP",  # 带空格的密钥
                "invalid_key_123",    # 无效密钥
                "",                   # 空密钥
            ]
            
            clean_results = []
            for key in test_keys:
                cleaned = extractor._clean_secret_key(key)
                clean_results.append({
                    'original': key,
                    'cleaned': cleaned,
                    'valid': len(cleaned) > 0
                })
            
            # 测试OTP URI解析
            test_uri = "otpauth://totp/Facebook:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Facebook"
            parsed_secret = extractor._parse_otpauth_uri(test_uri)
            
            test_result = {
                'test_name': '数据格式验证测试',
                'success': True,
                'details': {
                    'key_cleaning_results': clean_results,
                    'uri_parsing_success': parsed_secret is not None,
                    'parsed_secret': parsed_secret.__dict__ if parsed_secret else None
                }
            }
            
            self.test_results.append(test_result)
            
            log_info(f"[测试4] 数据格式验证测试完成", component="FB2FATestSuite")
            log_info(f"[测试4] 密钥清理测试: {len([r for r in clean_results if r['valid']])}/{len(clean_results)}个有效", component="FB2FATestSuite")
            log_info(f"[测试4] URI解析测试: {'成功' if parsed_secret else '失败'}", component="FB2FATestSuite")
            
            # 清理
            extractor.cleanup()
            
        except Exception as e:
            log_error(f"[测试4] 数据格式验证测试失败: {e}", component="FB2FATestSuite")
            self.test_results.append({
                'test_name': '数据格式验证测试',
                'success': False,
                'error': str(e)
            })

    def _generate_test_report(self):
        """生成测试报告"""
        try:
            log_info(f"[报告] ========== Facebook 2FA提取功能测试报告 ==========", component="FB2FATestSuite")
            log_info(f"[报告] 测试模拟器: {self.emulator_id}", component="FB2FATestSuite")
            log_info(f"[报告] 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}", component="FB2FATestSuite")
            log_info(f"[报告] 总测试数: {len(self.test_results)}", component="FB2FATestSuite")
            
            success_count = len([r for r in self.test_results if r.get('success', False)])
            log_info(f"[报告] 成功测试: {success_count}", component="FB2FATestSuite")
            log_info(f"[报告] 失败测试: {len(self.test_results) - success_count}", component="FB2FATestSuite")
            
            log_info(f"[报告] 详细结果:", component="FB2FATestSuite")
            for i, result in enumerate(self.test_results, 1):
                status = "✅ 成功" if result.get('success', False) else "❌ 失败"
                log_info(f"[报告]   {i}. {result['test_name']}: {status}", component="FB2FATestSuite")
                
                if not result.get('success', False) and 'error' in result:
                    log_info(f"[报告]      错误: {result['error']}", component="FB2FATestSuite")
            
            log_info(f"[报告] ===============================================", component="FB2FATestSuite")
            
            # 保存报告到文件
            self._save_test_report()
            
        except Exception as e:
            log_error(f"[报告] 生成测试报告失败: {e}", component="FB2FATestSuite")

    def _save_test_report(self):
        """保存测试报告到文件"""
        try:
            import json
            from datetime import datetime
            
            report_dir = Path("./fb_2fa_results/test_reports/")
            report_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = report_dir / f"fb_2fa_test_report_emulator{self.emulator_id}_{timestamp}.json"
            
            report_data = {
                'emulator_id': self.emulator_id,
                'test_time': datetime.now().isoformat(),
                'total_tests': len(self.test_results),
                'successful_tests': len([r for r in self.test_results if r.get('success', False)]),
                'test_results': self.test_results
            }
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            log_info(f"[报告] 测试报告已保存: {report_file}", component="FB2FATestSuite")
            
        except Exception as e:
            log_error(f"[报告] 保存测试报告失败: {e}", component="FB2FATestSuite")

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2
        
        print(f"🚀 开始Facebook 2FA提取功能测试 - 模拟器{emulator_id}")
        print("=" * 60)
        
        # 创建测试套件
        test_suite = FacebookTwoFactorTestSuite(emulator_id)
        
        # 运行所有测试
        await test_suite.run_all_tests()
        
        print("=" * 60)
        print("✅ Facebook 2FA提取功能测试完成")
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
