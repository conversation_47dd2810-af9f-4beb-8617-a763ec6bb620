#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Facebook 2FA提取功能测试脚本 - 基于现有架构
========================================
功能描述: 测试Facebook双因子认证密钥提取功能，基于现有Instagram任务架构

主要功能:
1. 测试FB 2FA任务执行器
2. 验证基础连接和配置
3. 生成测试报告

使用方法:
python fb/test_fb_2fa_extraction.py [emulator_id]

注意事项:
- 需要模拟器正在运行
- 基于现有的Instagram任务架构实现
========================================
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class FacebookTwoFactorTestSuite:
    """Facebook 2FA提取功能测试套件 - 基于现有架构"""

    def __init__(self, emulator_id: int = 2):
        """
        初始化测试套件

        Args:
            emulator_id: 测试用的模拟器ID
        """
        self.emulator_id = emulator_id
        self.test_results = []

        log_info(f"[测试] Facebook 2FA提取功能测试套件初始化 - 模拟器{emulator_id}", component="FB2FATestSuite")

    async def run_all_tests(self):
        """运行所有测试 - 简化版本"""
        try:
            log_info(f"[测试] 开始运行Facebook 2FA提取功能测试", component="FB2FATestSuite")

            # 测试1: 任务执行器测试
            await self._test_task_executor()

            # 生成测试报告
            self._generate_test_report()

            log_info(f"[测试] Facebook 2FA提取功能测试完成", component="FB2FATestSuite")

        except Exception as e:
            log_error(f"[测试] 测试执行失败: {e}", component="FB2FATestSuite")

    async def _test_task_executor(self):
        """测试任务执行器 - 基于现有架构"""
        try:
            log_info(f"[测试] 开始任务执行器测试", component="FB2FATestSuite")

            # 创建任务执行器实例
            task = FacebookTwoFactorTask(self.emulator_id)

            # 执行任务
            task_result = await task.execute()

            test_result = {
                'test_name': '任务执行器测试',
                'success': task_result.get('success', False),
                'details': {
                    'execution_time': task_result.get('execution_time', 0),
                    'error_message': task_result.get('error_message', ''),
                    'extraction_result': task_result.get('extraction_result', {})
                }
            }

            self.test_results.append(test_result)

            log_info(f"[测试] 任务执行器测试完成 - {'成功' if task_result.get('success') else '失败'}", component="FB2FATestSuite")
            log_info(f"[测试] 执行时间: {task_result.get('execution_time', 0):.2f}秒", component="FB2FATestSuite")

            if task_result.get('success'):
                extraction_result = task_result.get('extraction_result', {})
                secrets_count = len(extraction_result.get('secrets', []))
                log_info(f"[测试] 找到密钥数量: {secrets_count}", component="FB2FATestSuite")
                log_info(f"[测试] 提取方法: {extraction_result.get('extraction_methods', '')}", component="FB2FATestSuite")
            else:
                log_info(f"[测试] 错误信息: {task_result.get('error_message', '')}", component="FB2FATestSuite")

        except Exception as e:
            log_error(f"[测试] 任务执行器测试失败: {e}", component="FB2FATestSuite")
            self.test_results.append({
                'test_name': '任务执行器测试',
                'success': False,
                'error': str(e)
            })

    def _generate_test_report(self):
        """生成测试报告 - 简化版本"""
        try:
            log_info(f"[报告] ========== Facebook 2FA提取功能测试报告 ==========", component="FB2FATestSuite")
            log_info(f"[报告] 测试模拟器: {self.emulator_id}", component="FB2FATestSuite")
            log_info(f"[报告] 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}", component="FB2FATestSuite")
            log_info(f"[报告] 总测试数: {len(self.test_results)}", component="FB2FATestSuite")

            success_count = len([r for r in self.test_results if r.get('success', False)])
            log_info(f"[报告] 成功测试: {success_count}", component="FB2FATestSuite")
            log_info(f"[报告] 失败测试: {len(self.test_results) - success_count}", component="FB2FATestSuite")

            log_info(f"[报告] 详细结果:", component="FB2FATestSuite")
            for i, result in enumerate(self.test_results, 1):
                status = "✅ 成功" if result.get('success', False) else "❌ 失败"
                log_info(f"[报告]   {i}. {result['test_name']}: {status}", component="FB2FATestSuite")

                if not result.get('success', False) and 'error' in result:
                    log_info(f"[报告]      错误: {result['error']}", component="FB2FATestSuite")

            log_info(f"[报告] ===============================================", component="FB2FATestSuite")

            # 保存报告到文件
            self._save_test_report()

        except Exception as e:
            log_error(f"[报告] 生成测试报告失败: {e}", component="FB2FATestSuite")

    def _save_test_report(self):
        """保存测试报告到文件"""
        try:
            import json
            from datetime import datetime

            report_dir = Path("./fb_2fa_results/test_reports/")
            report_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = report_dir / f"fb_2fa_test_report_emulator{self.emulator_id}_{timestamp}.json"

            report_data = {
                'emulator_id': self.emulator_id,
                'test_time': datetime.now().isoformat(),
                'total_tests': len(self.test_results),
                'successful_tests': len([r for r in self.test_results if r.get('success', False)]),
                'test_results': self.test_results
            }

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)

            log_info(f"[报告] 测试报告已保存: {report_file}", component="FB2FATestSuite")

        except Exception as e:
            log_error(f"[报告] 保存测试报告失败: {e}", component="FB2FATestSuite")



    def _generate_test_report(self):
        """生成测试报告"""
        try:
            log_info(f"[报告] ========== Facebook 2FA提取功能测试报告 ==========", component="FB2FATestSuite")
            log_info(f"[报告] 测试模拟器: {self.emulator_id}", component="FB2FATestSuite")
            log_info(f"[报告] 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}", component="FB2FATestSuite")
            log_info(f"[报告] 总测试数: {len(self.test_results)}", component="FB2FATestSuite")
            
            success_count = len([r for r in self.test_results if r.get('success', False)])
            log_info(f"[报告] 成功测试: {success_count}", component="FB2FATestSuite")
            log_info(f"[报告] 失败测试: {len(self.test_results) - success_count}", component="FB2FATestSuite")
            
            log_info(f"[报告] 详细结果:", component="FB2FATestSuite")
            for i, result in enumerate(self.test_results, 1):
                status = "✅ 成功" if result.get('success', False) else "❌ 失败"
                log_info(f"[报告]   {i}. {result['test_name']}: {status}", component="FB2FATestSuite")
                
                if not result.get('success', False) and 'error' in result:
                    log_info(f"[报告]      错误: {result['error']}", component="FB2FATestSuite")
            
            log_info(f"[报告] ===============================================", component="FB2FATestSuite")
            
            # 保存报告到文件
            self._save_test_report()
            
        except Exception as e:
            log_error(f"[报告] 生成测试报告失败: {e}", component="FB2FATestSuite")

    def _save_test_report(self):
        """保存测试报告到文件"""
        try:
            import json
            from datetime import datetime
            
            report_dir = Path("./fb_2fa_results/test_reports/")
            report_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = report_dir / f"fb_2fa_test_report_emulator{self.emulator_id}_{timestamp}.json"
            
            report_data = {
                'emulator_id': self.emulator_id,
                'test_time': datetime.now().isoformat(),
                'total_tests': len(self.test_results),
                'successful_tests': len([r for r in self.test_results if r.get('success', False)]),
                'test_results': self.test_results
            }
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            log_info(f"[报告] 测试报告已保存: {report_file}", component="FB2FATestSuite")
            
        except Exception as e:
            log_error(f"[报告] 保存测试报告失败: {e}", component="FB2FATestSuite")

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2
        
        print(f"🚀 开始Facebook 2FA提取功能测试 - 模拟器{emulator_id}")
        print("=" * 60)
        
        # 创建测试套件
        test_suite = FacebookTwoFactorTestSuite(emulator_id)
        
        # 运行所有测试
        await test_suite.run_all_tests()
        
        print("=" * 60)
        print("✅ Facebook 2FA提取功能测试完成")
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
