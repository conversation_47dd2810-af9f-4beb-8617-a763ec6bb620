# 🔢 LeiDian_Reorganized.py 方法编号索引文档

## 📋 文件概述
- **文件名**: <PERSON><PERSON><PERSON>ian_Reorganized.py
- **总方法数**: 82个
- **功能分组**: 11个
- **编号格式**: 编号.中文描述-方法名
- **分组格式**: 🎯 ### **功能组名 (起始编号-结束编号)**

---

## 🔍 完整方法索引

### **🎯 核心功能 (1-4)**
1. **execute_ld** - 核心命令执行方法 - 核心命令执行方法，所有功能的基础
2. **dnld** - 简化版命令执行 - 简化版命令执行
3. **CMD** - 系统命令执行 - 系统命令执行
4. **is_running** - 运行状态检查 - 运行状态和Android系统检查

### **🎯 应用管理 (5-13)**
5. **appVersion** - 应用版本获取 - 获取指定应用的版本号
6. **appIsrunning** - 应用运行状态检查 - 检查应用是否正在运行
7. **get_activity_name** - 当前Activity获取 - 获取当前显示的Activity
8. **runApp** - 应用启动 - 启动指定应用
9. **killApp** - 应用终止 - 强制停止指定应用
10. **installappOfFile** - 应用安装(文件) - 通过文件路径安装APK
11. **installappOfPkg** - 应用安装(包名) - 通过包名安装应用
12. **uninstallapp** - 应用卸载 - 卸载指定应用
13. **has_install** - 应用安装检查 - 检查指定应用是否已安装

### **🎯 ADB和输入操作 (14-20)**
14. **adb** - ADB命令执行 - 执行Android调试桥命令
15. **adb2** - ADB命令封装 - 正确的ADB命令封装
16. **touch** - 坐标点击 - 在指定坐标执行点击操作
17. **swipe** - 屏幕滑动 - 执行屏幕滑动手势
18. **input_text** - 文本输入 - 输入文本到当前焦点控件
19. **key** - 按键事件 - 发送系统按键事件
20. **click** - 直接点击 - 直接点击坐标核心方法

### **🎯 模拟器管理 (21-31)**
21. **launch** - 模拟器启动 - 启动指定的模拟器实例
22. **quit** - 模拟器关闭 - 关闭指定的模拟器实例
23. **reboot** - 模拟器重启 - 重启指定的模拟器实例
24. **add** - 模拟器创建 - 创建新的模拟器实例
25. **copy** - 模拟器复制 - 复制现有模拟器实例
26. **remove** - 模拟器删除 - 删除指定的模拟器实例
27. **rename** - 模拟器重命名 - 重命名指定模拟器
28. **get_list** - 模拟器列表 - 获取所有模拟器信息
29. **refresh_emulators** - 模拟器列表刷新 - 获取并解析雷电模拟器列表
30. **list_running** - 运行中模拟器列表 - 获取运行中的模拟器列表
31. **sortWnd** - 窗口排版 - 对模拟器窗口进行排版

### **🎯 错误处理 (32-33)**
32. **get_last_error** - 错误信息获取 - 获取最后一次操作的错误信息
33. **clear_error** - 错误状态清除 - 清除错误状态

### **🎯 UI检测和操作 (34-49)**
34. **find_node** - UI元素查找 - 查找指定条件的UI元素
35. **click_node** - UI节点点击 - 点击找到的UI节点
36. **wait_for_node** - UI节点等待 - 等待指定节点出现
37. **wait_activity** - Activity等待 - 等待指定Activity出现
38. **wait_and_click** - 图像等待点击 - 等待图像出现并点击
39. **smart_click** - 智能点击 - 自动查找并重试点击
40. **find_children** - 子节点查找 - 在指定父节点下查找子节点
41. **find_nodes** - 节点查找(修复版) - 修复版节点查找
42. **get_cur_activity_xml** - Activity XML获取 - 获取当前Activity的XML
43. **check_any_node** - 节点检查(增强版) - 增强版节点检查支持多种判断模式
44. **get_node_bounds** - 节点坐标解析 - 增强版坐标解析
45. **get_node_info** - 节点完整信息 - 获取节点完整信息
46. **get_node_text** - 节点文本获取 - 获取节点文本内容
47. **find_fans_entries_v2** - 节点查找(修复版v2) - 修复节点对象不一致问题
48. **wait_for** - 等待函数(增强版) - 增强版等待函数支持所有find_node的查询条件
49. **install** - APK安装 - 安装APK文件到模拟器

### **🎯 图像处理和截图 (50-58)**
50. **capture_window_back** - 窗口截图 - 通过Win32 API截取窗口图像
51. **capture_and_save** - 截图保存 - 截图并保存到指定文件
52. **background_mouse_click** - 后台鼠标点击 - 在指定窗口后台执行鼠标点击
53. **find_image** - 图像查找 - 在源图像中查找模板图像
54. **wait_for_image** - 图像等待 - 等待目标图像出现
55. **wait_for_images** - 多图像等待 - 等待多个图像出现
56. **wait_picture** - 图片等待 - 等待图片出现
57. **check_picture** - 图片检查 - 检查指定图片是否存在
58. **find_color** - 颜色查找 - 在源图像中查找指定颜色的像素点

### **🎯 坐标和滑动 (59-64)**
59. **convert_coordinates** - 坐标转换 - 转换坐标从一个尺寸到另一个尺寸
60. **screen_to_window** - 屏幕到窗口坐标 - 将模拟器实际分辨率坐标转换为窗口坐标
61. **window_to_screen** - 窗口到屏幕坐标 - 将窗口坐标转换为模拟器实际分辨率坐标
62. **simulate_swipe** - 模拟滑动 - 当节点不可用时的随机滑动
63. **random_swipe** - 随机滑动 - 执行智能随机滑动
64. **scroll_list_enhanced** - 增强滑动 - 增强滑动功能支持节点滑动和模拟滑动

### **🎯 模拟器配置 (65-75)**
65. **get_screen_size** - 屏幕分辨率获取 - 获取模拟器屏幕分辨率
66. **get_hwnd** - 窗口句柄获取 - 获取指定模拟器的窗口句柄
67. **modifyResolution** - 分辨率配置 - 修改模拟器分辨率配置
68. **modifyCPU** - CPU内存配置 - 修改模拟器CPU和内存配置
69. **modifyPhone** - 设备信息配置 - 修改模拟器设备信息
70. **modifyOthers** - 其他配置 - 修改模拟器其他配置选项
71. **auto_rate** - 自动帧率设置 - 设置模拟器自动帧率
72. **enable_bridge_mode** - 桥接模式开关 - 修改桥接模式开关
73. **modify_bridge** - 桥接模式设置 - 修改桥接模式设置
74. **mark_resolution_dirty** - 分辨率刷新标记 - 标记分辨率需要刷新
75. **set_default_font_size** - 字体大小调整 - 调整系统字体大小

### **🎯 文件和剪贴板 (76-81)**
76. **get_package_list** - 应用包列表 - 获取模拟器中安装的应用包列表
77. **get_clipboard_content** - 剪贴板内容获取 - 获取指定模拟器的剪贴板内容
78. **paste_text** - 粘贴指令 - 直接发送粘贴指令
79. **send_broadcast_to_clipboard** - 剪贴板广播 - 发送广播到剪贴板优化版
80. **pull_file** - 文件拉取 - 从模拟器复制文件到主机
81. **push_file** - 文件推送 - 从主机发送文件到模拟器

### **🎯 辅助功能 (82-82)**
82. **safe_sleep** - 安全等待 - 可中断的短间隔等待

---

## 📊 统计信息

| 功能分组 | 方法数量 | 编号范围 |
|---------|---------|---------|
| 核心功能 | 4 | 1-4 |
| 应用管理 | 9 | 5-13 |
| ADB和输入操作 | 7 | 14-20 |
| 模拟器管理 | 11 | 21-31 |
| 错误处理 | 2 | 32-33 |
| UI检测和操作 | 16 | 34-49 |
| 图像处理和截图 | 9 | 50-58 |
| 坐标和滑动 | 6 | 59-64 |
| 模拟器配置 | 11 | 65-75 |
| 文件和剪贴板 | 6 | 76-81 |
| 辅助功能 | 1 | 82-82 |
| **总计** | **82** | **1-82** |

---

## 🎯 快速查找指南

### 按功能类型查找
- **基础操作**: 1-4 (核心功能)
- **应用相关**: 5-13 (应用管理)
- **输入控制**: 14-20 (ADB和输入操作)
- **模拟器控制**: 21-31 (模拟器管理)
- **UI自动化**: 34-49 (UI检测和操作)
- **图像识别**: 50-58 (图像处理和截图)
- **坐标处理**: 59-64 (坐标和滑动)
- **系统配置**: 65-75 (模拟器配置)
- **文件操作**: 76-81 (文件和剪贴板)

### 常用方法快速索引
- **启动应用**: 8.runApp
- **点击操作**: 16.touch, 20.click
- **文本输入**: 18.input_text
- **截图功能**: 50.capture_window_back, 51.capture_and_save
- **图像查找**: 53.find_image, 54.wait_for_image
- **UI元素查找**: 34.find_node, 36.wait_for_node
- **模拟器管理**: 21.launch, 22.quit, 23.reboot

---

*📝 文档生成时间: 2025-01-14*  
*🔄 版本: v1.0*  
*📁 对应文件: LeiDian_Reorganized.py*
