#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 真正有效的Facebook跳转工具
========================================
功能描述: 使用Instagram成功的跳转方式来实现Facebook跳转

核心策略:
1. 使用雷电模拟器API的真正跳转方法
2. 参考Instagram成功的实现
3. 确保真正打开Facebook页面

使用方法:
python fb/real_facebook_jump.py

注意事项:
- 使用Instagram验证过的跳转方式
- 确保真正的页面跳转
========================================
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from core.ld_api import LDApi
    from core.logger_manager import log_info, log_error
except ImportError:
    print("⚠️ 无法导入核心模块，使用简化版本")
    
    class LDApi:
        def __init__(self):
            pass
        
        def execute_ld(self, emulator_id, command, silence=True):
            import subprocess
            try:
                ldconsole_path = "G:/leidian/LDPlayer9/ldconsole.exe"
                full_command = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", f"shell {command}"]
                result = subprocess.run(full_command, capture_output=True, text=True, timeout=15)
                return result.returncode == 0, result.stdout.strip()
            except Exception as e:
                return False, str(e)
        
        def runApp(self, emulator_id, package_name):
            success, output = self.execute_ld(emulator_id, f"monkey -p {package_name} -c android.intent.category.LAUNCHER 1")
            return output if success else ""
        
        def get_activity_name(self):
            return "unknown"
    
    def log_info(msg, component=""): print(f"INFO: {msg}")
    def log_error(msg, component=""): print(f"ERROR: {msg}")

class RealFacebookJumper:
    """真正有效的Facebook跳转器"""
    
    def __init__(self, emulator_id: int = 2):
        """初始化跳转器"""
        self.emulator_id = emulator_id
        self.ld = LDApi()
        
        # Facebook相关包名
        self.facebook_packages = [
            "com.facebook.katana",  # Facebook主应用
            "com.facebook.lite"     # Facebook Lite
        ]

    def real_jump_to_facebook(self):
        """真正跳转到Facebook - 使用Instagram验证过的方法"""
        print("🔓 真正有效的Facebook跳转工具")
        print("=" * 50)
        print(f"📱 目标模拟器: 模拟器{self.emulator_id}")
        print("⚠️ 使用Instagram验证过的跳转方式")
        print()
        
        # 方法1: 检查并启动Facebook应用
        if self.launch_facebook_with_real_method():
            print("✅ 成功启动Facebook应用!")
            
            # 等待应用完全加载
            print("⏳ 等待Facebook应用加载...")
            time.sleep(5)
            
            # 尝试导航到密码重置
            if self.navigate_to_reset_in_app():
                print("🎉 成功导航到密码重置页面!")
                self.show_success_guide()
                return True
            else:
                print("⚠️ 应用内导航可能需要手动操作")
                self.show_manual_navigation_guide()
                return True
        
        # 方法2: 使用系统浏览器（真正的方法）
        print("\n🌐 方法2: 使用系统浏览器...")
        if self.open_facebook_in_browser():
            print("✅ 成功在浏览器中打开Facebook!")
            self.show_browser_guide()
            return True
        
        # 方法3: 手动指导
        print("\n❌ 自动跳转失败，提供手动方法")
        self.show_complete_manual_guide()
        return False

    def launch_facebook_with_real_method(self):
        """使用真正有效的方法启动Facebook应用"""
        print("📱 使用真正有效的方法启动Facebook应用...")
        
        for package in self.facebook_packages:
            print(f"   🔍 尝试启动: {package}")
            
            # 方法1: 使用runApp方法（Instagram验证过的）
            try:
                result = self.ld.runApp(self.emulator_id, package)
                if result and result.strip():
                    print(f"      ✅ runApp方法成功: {package}")
                    time.sleep(3)
                    
                    # 验证应用是否真正启动
                    if self.verify_facebook_launched():
                        print(f"      🎉 {package} 真正启动成功!")
                        return True
                    else:
                        print(f"      ⚠️ {package} 启动命令成功但应用未显示")
                else:
                    print(f"      ❌ runApp方法失败: {package}")
            except Exception as e:
                print(f"      ❌ runApp方法异常: {e}")
            
            # 方法2: 使用Intent方法
            try:
                cmd = f"am start -n {package}/.LoginActivity"
                success, output = self.ld.execute_ld(self.emulator_id, cmd)
                if success:
                    print(f"      ✅ Intent方法成功: {package}")
                    time.sleep(3)
                    
                    if self.verify_facebook_launched():
                        print(f"      🎉 {package} Intent启动成功!")
                        return True
                else:
                    print(f"      ❌ Intent方法失败: {package}")
            except Exception as e:
                print(f"      ❌ Intent方法异常: {e}")
            
            # 方法3: 使用monkey方法
            try:
                cmd = f"monkey -p {package} -c android.intent.category.LAUNCHER 1"
                success, output = self.ld.execute_ld(self.emulator_id, cmd)
                if success:
                    print(f"      ✅ Monkey方法成功: {package}")
                    time.sleep(3)
                    
                    if self.verify_facebook_launched():
                        print(f"      🎉 {package} Monkey启动成功!")
                        return True
                else:
                    print(f"      ❌ Monkey方法失败: {package}")
            except Exception as e:
                print(f"      ❌ Monkey方法异常: {e}")
        
        print("   ❌ 所有Facebook应用启动方法都失败")
        return False

    def verify_facebook_launched(self):
        """验证Facebook应用是否真正启动"""
        try:
            # 检查当前运行的应用
            success, output = self.ld.execute_ld(self.emulator_id, "dumpsys window windows | grep -E 'mCurrentFocus'")
            if success and output:
                focus_info = output.lower()
                facebook_keywords = ['facebook', 'katana', 'com.facebook']
                if any(keyword in focus_info for keyword in facebook_keywords):
                    return True
            
            # 检查正在运行的进程
            success2, output2 = self.ld.execute_ld(self.emulator_id, "ps | grep facebook")
            if success2 and output2 and "facebook" in output2.lower():
                return True
            
            return False
            
        except Exception:
            return False

    def navigate_to_reset_in_app(self):
        """在Facebook应用中导航到密码重置"""
        print("   🧭 在Facebook应用中导航到密码重置...")
        
        try:
            # 等待应用完全加载
            time.sleep(2)
            
            # 获取屏幕内容
            success, ui_content = self.ld.execute_ld(self.emulator_id, "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml")
            if success and ui_content:
                print("      ✅ 成功获取屏幕内容")
                
                # 检查是否已经在登录页面
                if any(keyword in ui_content.lower() for keyword in ['登录', 'login', '密码', 'password']):
                    print("      ✅ 已在登录相关页面")
                    
                    # 尝试点击"忘记密码"相关按钮
                    self.click_forgot_password_elements()
                    return True
                else:
                    print("      ⚠️ 需要导航到登录页面")
                    return self.navigate_to_login_screen()
            else:
                print("      ❌ 无法获取屏幕内容")
                return False
                
        except Exception as e:
            print(f"      ❌ 应用内导航异常: {e}")
            return False

    def click_forgot_password_elements(self):
        """点击忘记密码相关元素"""
        print("      🔍 寻找并点击忘记密码元素...")
        
        # 常见的忘记密码按钮位置（基于Facebook应用布局）
        forgot_password_positions = [
            (360, 650),  # 屏幕中下方
            (360, 700),  # 屏幕下方
            (360, 600),  # 屏幕中间偏下
            (180, 650),  # 屏幕左下
            (540, 650),  # 屏幕右下
        ]
        
        for i, (x, y) in enumerate(forgot_password_positions, 1):
            print(f"         🔍 尝试点击位置 {i}: ({x}, {y})")
            
            try:
                success, output = self.ld.execute_ld(self.emulator_id, f"input tap {x} {y}")
                if success:
                    print(f"            ✅ 点击位置 {i} 成功")
                    time.sleep(2)
                    
                    # 检查是否有页面变化
                    if self.check_page_changed():
                        print(f"            🎉 点击位置 {i} 导致页面变化!")
                        return True
                else:
                    print(f"            ❌ 点击位置 {i} 失败")
            except Exception as e:
                print(f"            ❌ 点击位置 {i} 异常: {e}")
        
        return False

    def navigate_to_login_screen(self):
        """导航到登录屏幕"""
        print("      🧭 导航到登录屏幕...")
        
        # 尝试各种导航操作
        navigation_actions = [
            ("向上滑动", f"input swipe 360 800 360 400"),
            ("向下滑动", f"input swipe 360 400 360 800"),
            ("点击屏幕中央", f"input tap 360 640"),
            ("按返回键", f"input keyevent KEYCODE_BACK"),
            ("按菜单键", f"input keyevent KEYCODE_MENU"),
        ]
        
        for desc, cmd in navigation_actions:
            print(f"         🔍 {desc}...")
            
            try:
                success, output = self.ld.execute_ld(self.emulator_id, cmd)
                if success:
                    print(f"            ✅ {desc} 成功")
                    time.sleep(1)
                    
                    # 检查是否到达登录页面
                    if self.check_login_elements():
                        print(f"            🎉 {desc} 成功到达登录页面!")
                        return True
                else:
                    print(f"            ❌ {desc} 失败")
            except Exception as e:
                print(f"            ❌ {desc} 异常: {e}")
        
        return False

    def check_page_changed(self):
        """检查页面是否发生变化"""
        try:
            success, ui_content = self.ld.execute_ld(self.emulator_id, "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml")
            if success and ui_content:
                # 检查是否包含密码重置相关内容
                reset_keywords = ['重置', 'reset', '恢复', 'recover', '找回', '验证', 'verify']
                return any(keyword in ui_content.lower() for keyword in reset_keywords)
            return False
        except Exception:
            return False

    def show_success_guide(self):
        """显示成功指导"""
        print("\n🎉 Facebook应用成功打开!")
        print("=" * 50)
        print("📱 请在模拟器2中查看Facebook应用")
        print()
        print("🔍 寻找以下元素:")
        print("- '忘记密码?' 链接")
        print("- '找回账户' 按钮")
        print("- '重置密码' 选项")
        print()
        print("📝 操作步骤:")
        print("1. 点击'忘记密码?'")
        print("2. 输入您的邮箱地址")
        print("3. 选择通过邮箱重置")
        print("4. 检查邮箱中的重置链接")

    def show_browser_guide(self):
        """显示浏览器指导"""
        print("\n🌐 Facebook页面在浏览器中打开!")
        print("=" * 50)
        print("📱 请在模拟器2中查看浏览器")
        print()
        print("📝 操作步骤:")
        print("1. 在输入框中输入您的邮箱地址")
        print("2. 点击'搜索'或'查找账户'")
        print("3. 选择'通过邮箱重置密码'")
        print("4. 检查邮箱中的重置链接")

    def show_manual_navigation_guide(self):
        """显示手动导航指导"""
        print("\n🧭 Facebook应用已打开，需要手动导航")
        print("=" * 50)
        print("📱 请在模拟器2的Facebook应用中:")
        print()
        print("🔍 寻找并点击:")
        print("- '忘记密码?' 文字或链接")
        print("- '找回账户' 按钮")
        print("- '登录遇到问题?' 选项")
        print()
        print("📝 如果找不到上述选项:")
        print("1. 尝试向上或向下滑动屏幕")
        print("2. 点击屏幕上的任何登录相关按钮")
        print("3. 查看屏幕底部是否有更多选项")

    def show_complete_manual_guide(self):
        """显示完整的手动指导"""
        print("\n🔧 完整手动操作指导")
        print("=" * 50)
        print("❌ 自动跳转失败，请手动操作:")
        print()
        print("📱 方法1: 手动打开Facebook应用")
        print("1. 在模拟器2中找到Facebook应用图标")
        print("2. 点击打开Facebook应用")
        print("3. 在登录页面点击'忘记密码?'")
        print("4. 输入邮箱地址并选择邮箱重置")
        print()
        print("🌐 方法2: 手动打开浏览器")
        print("1. 在模拟器2中打开任意浏览器")
        print("2. 访问: https://m.facebook.com/login/identify")
        print("3. 输入邮箱地址")
        print("4. 选择通过邮箱重置密码")
        print()
        print("💡 重要提示:")
        print("- 使用邮箱地址，不要使用手机号")
        print("- 选择邮箱重置，避免短信验证")
        print("- 检查邮箱的垃圾邮件文件夹")
        print("- 重置成功后立即重新设置2FA")

def main():
    """主函数"""
    try:
        print("🔓 真正有效的Facebook跳转工具")
        print("⚠️ 使用Instagram验证过的跳转方式")
        print("⚠️ 确保真正的页面跳转")
        print("⚠️ 专门针对模拟器2")
        print()

        confirm = input("确认使用真正有效的方法跳转到Facebook? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return

        # 创建真正的跳转器
        jumper = RealFacebookJumper(emulator_id=2)

        # 执行真正的跳转
        success = jumper.real_jump_to_facebook()

        if success:
            print("\n🎉 跳转成功!")
            print("💡 请在模拟器2中查看并按照指导操作")
        else:
            print("\n❌ 自动跳转失败")
            print("💡 请按照手动指导完成操作")

        print("\n" + "=" * 50)
        print("✅ 真正有效的Facebook跳转工具执行完成")

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()

    def check_login_elements(self):
        """检查登录元素"""
        try:
            success, ui_content = self.ld.execute_ld(self.emulator_id, "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml")
            if success and ui_content:
                login_keywords = ['登录', 'login', '密码', 'password', '忘记', 'forgot']
                return any(keyword in ui_content.lower() for keyword in login_keywords)
            return False
        except Exception:
            return False

    def open_facebook_in_browser(self):
        """使用系统浏览器打开Facebook"""
        print("   🌐 使用系统浏览器打开Facebook...")
        
        facebook_urls = [
            "https://m.facebook.com/login/identify",
            "https://www.facebook.com/login/identify",
            "https://mbasic.facebook.com/login/identify"
        ]
        
        for i, url in enumerate(facebook_urls, 1):
            print(f"      🔍 尝试URL {i}: {url}")
            
            try:
                # 使用系统默认浏览器打开
                cmd = f"am start -a android.intent.action.VIEW -d '{url}'"
                success, output = self.ld.execute_ld(self.emulator_id, cmd)
                
                if success:
                    print(f"         ✅ URL {i} 启动成功")
                    time.sleep(5)  # 等待浏览器加载
                    
                    # 验证浏览器是否真正打开
                    if self.verify_browser_opened():
                        print(f"         🎉 URL {i} 在浏览器中成功打开!")
                        return True
                    else:
                        print(f"         ⚠️ URL {i} 启动成功但浏览器未显示")
                else:
                    print(f"         ❌ URL {i} 启动失败")
                    
            except Exception as e:
                print(f"         ❌ URL {i} 异常: {e}")
        
        return False

    def verify_browser_opened(self):
        """验证浏览器是否真正打开"""
        try:
            success, output = self.ld.execute_ld(self.emulator_id, "dumpsys window windows | grep -E 'mCurrentFocus'")
            if success and output:
                focus_info = output.lower()
                browser_keywords = ['browser', 'chrome', 'webview', 'facebook']
                return any(keyword in focus_info for keyword in browser_keywords)
            return False
        except Exception:
            return False
