#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 Facebook 2FA Root权限提取工具
========================================
功能描述: 基于android-otp-extractor方法的Facebook 2FA密钥提取工具

核心策略:
1. 正确使用root权限访问应用数据
2. 扫描所有认证器应用的数据库
3. 解析SQLite数据库中的TOTP密钥
4. 支持多种认证器应用格式
5. 生成可导入的备份文件

支持的应用:
- Google Authenticator
- Microsoft Authenticator  
- Authy
- FreeOTP/FreeOTP+
- Aegis
- AndOTP
- Steam Authenticator
- Battle.net Authenticator

使用方法:
python fb/root_2fa_extractor.py [emulator_id]

注意事项:
- 必须具有root权限
- 基于成熟的android-otp-extractor方法
- 仅用于合法的账户恢复目的
========================================
"""

import asyncio
import sys
import re
import json
import base64
import sqlite3
import tempfile
import struct
import hashlib
import binascii
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class RootTwoFactorExtractor:
    """Root权限2FA提取器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化Root提取器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.temp_dir = Path(tempfile.gettempdir()) / f"root_2fa_extract_{emulator_id}"
        self.temp_dir.mkdir(exist_ok=True)
        
        # 支持的认证器应用配置
        self.authenticator_apps = {
            'google_authenticator': {
                'package': 'com.google.android.apps.authenticator2',
                'database': 'databases/accounts.db',
                'table': 'accounts',
                'secret_column': 'secret',
                'issuer_column': 'issuer',
                'account_column': 'email'
            },
            'microsoft_authenticator': {
                'package': 'com.azure.authenticator',
                'database': 'databases/PhoneFactor',
                'table': 'accounts',
                'secret_column': 'oath_secret_key',
                'issuer_column': 'issuer',
                'account_column': 'username'
            },
            'authy': {
                'package': 'com.authy.authy',
                'database': 'shared_prefs/com.authy.storage.tokens.authenticator.xml',
                'format': 'xml'
            },
            'freeotp': {
                'package': 'org.fedorahosted.freeotp',
                'database': 'shared_prefs/tokens.xml',
                'format': 'xml'
            },
            'freeotp_plus': {
                'package': 'org.liberty.android.freeotpplus',
                'database': 'shared_prefs/tokens.xml',
                'format': 'xml'
            },
            'aegis': {
                'package': 'com.beemdevelopment.aegis',
                'database': 'files/aegis.json',
                'format': 'json'
            },
            'andotp': {
                'package': 'org.shadowice.flocke.andotp',
                'database': 'shared_prefs/encrypted_backup',
                'format': 'encrypted'
            },
            'steam': {
                'package': 'com.valvesoftware.android.steam.community',
                'database': 'files/Steamguard-*',
                'format': 'json'
            },
            'battlenet': {
                'package': 'com.blizzard.messenger',
                'database': 'databases/credential-store',
                'table': 'accounts',
                'secret_column': 'secret',
                'issuer_column': 'issuer',
                'account_column': 'account'
            }
        }
        
        log_info(f"[Root提取] Root权限2FA提取器初始化 - 模拟器{emulator_id}", component="RootExtractor")

    async def run_root_extraction(self):
        """运行Root权限提取流程"""
        try:
            log_info(f"[Root提取] 开始Root权限2FA提取", component="RootExtractor")
            
            print("🔓 Facebook 2FA Root权限提取工具")
            print("=" * 50)
            print("⚠️  基于android-otp-extractor方法")
            print("⚠️  需要完全root权限")
            print("⚠️  扫描所有认证器应用")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：验证root权限
            if not await self._verify_root_access():
                print("❌ 需要root权限才能继续")
                return
            
            # 第二步：扫描已安装的认证器应用
            installed_apps = await self._scan_installed_authenticators()
            
            if not installed_apps:
                print("❌ 未找到任何认证器应用")
                print("💡 建议: 先安装Google Authenticator等认证器应用")
                return
            
            # 第三步：从每个应用提取2FA密钥
            all_secrets = []
            
            for app_name, app_info in installed_apps.items():
                print(f"\n🔍 提取 {app_name} 的2FA密钥...")
                app_secrets = await self._extract_from_app(app_name, app_info)
                if app_secrets:
                    all_secrets.extend(app_secrets)
                    print(f"✅ 从 {app_name} 提取到 {len(app_secrets)} 个密钥")
                else:
                    print(f"❌ 从 {app_name} 未提取到密钥")
            
            # 第四步：验证和处理密钥
            verified_secrets = await self._verify_extracted_secrets(all_secrets)
            
            # 第五步：保存和显示结果
            await self._save_root_results(verified_secrets)
            await self._display_root_results(verified_secrets)
            
            log_info(f"[Root提取] Root权限2FA提取完成", component="RootExtractor")
            
        except Exception as e:
            log_error(f"[Root提取] Root权限提取失败: {e}", component="RootExtractor")
        finally:
            await self._cleanup()

    async def _verify_root_access(self) -> bool:
        """验证root权限"""
        try:
            print("🔐 验证root权限...")
            
            # 测试基本root权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'whoami'")
            if not success or "root" not in result:
                print("❌ 基本root权限验证失败")
                print("💡 请确保模拟器已开启root权限")
                return False
            
            print("✅ 基本root权限: 通过")
            
            # 测试/data分区访问权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'ls -la /data/data/ | wc -l'")
            if success and result.strip().isdigit() and int(result.strip()) > 10:
                print("✅ /data分区访问权限: 通过")
            else:
                print("❌ /data分区访问权限: 失败")
                print("💡 请检查root权限是否完整")
                return False
            
            # 测试SQLite访问
            success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'which sqlite3'")
            if success and result.strip():
                print("✅ SQLite工具: 可用")
            else:
                print("⚠️  SQLite工具: 不可用，将使用替代方法")
            
            print("✅ Root权限验证完成")
            print()
            return True
            
        except Exception as e:
            log_error(f"[Root提取] Root权限验证失败: {e}", component="RootExtractor")
            return False

    async def _scan_installed_authenticators(self) -> Dict[str, Dict[str, Any]]:
        """扫描已安装的认证器应用"""
        try:
            print("📱 扫描已安装的认证器应用...")
            print("-" * 40)
            
            installed_apps = {}
            
            for app_name, app_config in self.authenticator_apps.items():
                package = app_config['package']
                
                # 检查应用是否已安装
                success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages | grep {package}")
                if success and package in result:
                    print(f"✅ 发现应用: {app_name} ({package})")
                    
                    # 检查应用数据目录是否存在
                    data_path = f"/data/data/{package}"
                    success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'ls -la {data_path}'")
                    if success2 and "No such file" not in result2:
                        print(f"   📁 数据目录存在: {data_path}")
                        
                        # 检查数据库文件是否存在
                        db_path = f"{data_path}/{app_config.get('database', '')}"
                        success3, result3 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'ls -la {db_path}*'")
                        if success3 and "No such file" not in result3:
                            print(f"   🗄️ 数据库文件存在: {db_path}")
                            installed_apps[app_name] = {
                                'package': package,
                                'data_path': data_path,
                                'config': app_config
                            }
                        else:
                            print(f"   ❌ 数据库文件不存在: {db_path}")
                    else:
                        print(f"   ❌ 数据目录不存在: {data_path}")
                else:
                    print(f"❌ 未安装: {app_name} ({package})")
            
            print(f"\n✅ 扫描完成: 找到 {len(installed_apps)} 个可用的认证器应用")
            print()
            return installed_apps
            
        except Exception as e:
            log_error(f"[Root提取] 扫描认证器应用失败: {e}", component="RootExtractor")
            return {}

    async def _extract_from_app(self, app_name: str, app_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从指定应用提取2FA密钥"""
        try:
            secrets = []
            config = app_info['config']
            data_path = app_info['data_path']
            
            if config.get('format') == 'xml':
                secrets = await self._extract_from_xml(app_name, data_path, config)
            elif config.get('format') == 'json':
                secrets = await self._extract_from_json(app_name, data_path, config)
            elif config.get('format') == 'encrypted':
                secrets = await self._extract_from_encrypted(app_name, data_path, config)
            else:
                # 默认SQLite数据库格式
                secrets = await self._extract_from_sqlite(app_name, data_path, config)
            
            return secrets
            
        except Exception as e:
            log_error(f"[Root提取] 从{app_name}提取失败: {e}", component="RootExtractor")
            return []

    async def _extract_from_sqlite(self, app_name: str, data_path: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从SQLite数据库提取密钥"""
        try:
            secrets = []
            db_path = f"{data_path}/{config['database']}"
            
            print(f"   🗄️ 分析SQLite数据库: {db_path}")
            
            # 将数据库复制到临时位置
            temp_db = f"/sdcard/temp_{app_name}.db"
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'cp {db_path} {temp_db}'")
            if not success:
                print(f"   ❌ 无法复制数据库文件")
                return []
            
            # 使用sqlite3命令查询数据
            table = config.get('table', 'accounts')
            secret_col = config.get('secret_column', 'secret')
            issuer_col = config.get('issuer_column', 'issuer')
            account_col = config.get('account_column', 'email')
            
            query = f"SELECT {secret_col}, {issuer_col}, {account_col} FROM {table}"
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'sqlite3 {temp_db} \"{query}\"'")
            
            if success and result.strip():
                lines = result.strip().split('\n')
                print(f"   📊 找到 {len(lines)} 条记录")
                
                for line in lines:
                    if line.strip():
                        parts = line.split('|')
                        if len(parts) >= 3:
                            secret = parts[0].strip()
                            issuer = parts[1].strip()
                            account = parts[2].strip()
                            
                            # 验证密钥格式
                            if self._is_valid_base32_key(secret):
                                secrets.append({
                                    'secret_key': secret,
                                    'issuer': issuer,
                                    'account': account,
                                    'source': f'{app_name}_sqlite',
                                    'method': 'sqlite_extraction',
                                    'confidence': 'high'
                                })
                                print(f"   🔑 提取密钥: {issuer} - {account}")
            else:
                print(f"   ❌ 查询数据库失败或无数据")
            
            # 清理临时文件
            self.task.ld.execute_ld(self.emulator_id, f"rm -f {temp_db}")
            
            return secrets
            
        except Exception as e:
            log_error(f"[Root提取] SQLite提取失败: {e}", component="RootExtractor")
            return []

    async def _extract_from_xml(self, app_name: str, data_path: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从XML文件提取密钥"""
        try:
            secrets = []
            xml_path = f"{data_path}/{config['database']}"
            
            print(f"   📄 分析XML文件: {xml_path}")
            
            # 读取XML文件内容
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'cat {xml_path}'")
            if success and result.strip():
                xml_content = result.strip()
                
                # 使用正则表达式提取Base32密钥
                base32_matches = re.findall(r'[A-Z2-7]{16,}', xml_content)
                for match in base32_matches:
                    if self._is_valid_base32_key(match):
                        secrets.append({
                            'secret_key': match,
                            'issuer': 'Unknown',
                            'account': 'Unknown',
                            'source': f'{app_name}_xml',
                            'method': 'xml_extraction',
                            'confidence': 'medium'
                        })
                        print(f"   🔑 提取密钥: {match[:8]}...")
                
                print(f"   📊 从XML提取到 {len(secrets)} 个密钥")
            else:
                print(f"   ❌ 无法读取XML文件")
            
            return secrets
            
        except Exception as e:
            log_error(f"[Root提取] XML提取失败: {e}", component="RootExtractor")
            return []

    async def _extract_from_json(self, app_name: str, data_path: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从JSON文件提取密钥"""
        try:
            secrets = []
            json_path = f"{data_path}/{config['database']}"
            
            print(f"   📄 分析JSON文件: {json_path}")
            
            # 读取JSON文件内容
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'cat {json_path}'")
            if success and result.strip():
                try:
                    json_data = json.loads(result.strip())
                    
                    # 递归搜索JSON中的密钥
                    found_secrets = self._search_json_for_secrets(json_data)
                    for secret_info in found_secrets:
                        secrets.append({
                            'secret_key': secret_info['secret'],
                            'issuer': secret_info.get('issuer', 'Unknown'),
                            'account': secret_info.get('account', 'Unknown'),
                            'source': f'{app_name}_json',
                            'method': 'json_extraction',
                            'confidence': 'high'
                        })
                        print(f"   🔑 提取密钥: {secret_info.get('issuer', 'Unknown')}")
                    
                    print(f"   📊 从JSON提取到 {len(secrets)} 个密钥")
                except json.JSONDecodeError:
                    print(f"   ❌ JSON格式无效")
            else:
                print(f"   ❌ 无法读取JSON文件")
            
            return secrets
            
        except Exception as e:
            log_error(f"[Root提取] JSON提取失败: {e}", component="RootExtractor")
            return []

    async def _extract_from_encrypted(self, app_name: str, data_path: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从加密文件提取密钥"""
        try:
            secrets = []
            enc_path = f"{data_path}/{config['database']}"
            
            print(f"   🔐 分析加密文件: {enc_path}")
            
            # 尝试读取加密文件并搜索可能的密钥模式
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'strings {enc_path} | grep -E \"[A-Z2-7]{{16,}}\"'")
            if success and result.strip():
                potential_keys = result.strip().split('\n')
                for key in potential_keys:
                    if self._is_valid_base32_key(key.strip()):
                        secrets.append({
                            'secret_key': key.strip(),
                            'issuer': 'Unknown',
                            'account': 'Unknown',
                            'source': f'{app_name}_encrypted',
                            'method': 'encrypted_extraction',
                            'confidence': 'low'
                        })
                        print(f"   🔑 提取密钥: {key.strip()[:8]}...")
                
                print(f"   📊 从加密文件提取到 {len(secrets)} 个密钥")
            else:
                print(f"   ❌ 无法从加密文件提取密钥")
            
            return secrets
            
        except Exception as e:
            log_error(f"[Root提取] 加密文件提取失败: {e}", component="RootExtractor")
            return []

    def _is_valid_base32_key(self, key: str) -> bool:
        """验证是否为有效的Base32密钥"""
        try:
            if not key or len(key) < 16:
                return False

            # 清理密钥
            cleaned = re.sub(r'[^A-Z2-7]', '', key.upper())
            if len(cleaned) < 16:
                return False

            # 验证Base32字符集
            if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in cleaned):
                return False

            # 尝试解码
            try:
                base64.b32decode(cleaned)
                return True
            except Exception:
                return False

        except Exception:
            return False

    def _search_json_for_secrets(self, data: Any, path: str = "") -> List[Dict[str, Any]]:
        """递归搜索JSON中的密钥"""
        secrets = []

        try:
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{path}.{key}" if path else key

                    # 检查当前值是否为密钥
                    if isinstance(value, str) and self._is_valid_base32_key(value):
                        secret_info = {
                            'secret': value,
                            'path': current_path
                        }

                        # 尝试从同级字段获取issuer和account信息
                        if 'issuer' in data:
                            secret_info['issuer'] = data['issuer']
                        if 'account' in data:
                            secret_info['account'] = data['account']
                        if 'name' in data:
                            secret_info['account'] = data['name']
                        if 'label' in data:
                            secret_info['account'] = data['label']

                        secrets.append(secret_info)

                    # 递归搜索
                    secrets.extend(self._search_json_for_secrets(value, current_path))

            elif isinstance(data, list):
                for i, item in enumerate(data):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    secrets.extend(self._search_json_for_secrets(item, current_path))

            elif isinstance(data, str) and self._is_valid_base32_key(data):
                secrets.append({
                    'secret': data,
                    'path': path
                })

        except Exception:
            pass

        return secrets

    async def _verify_extracted_secrets(self, all_secrets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证提取的密钥"""
        try:
            print("🔍 验证提取的密钥...")
            print("-" * 40)

            # 去重
            unique_secrets = []
            seen_keys = set()

            for secret in all_secrets:
                key = secret.get('secret_key', '')
                if key and key not in seen_keys:
                    seen_keys.add(key)

                    # 验证密钥并生成测试TOTP
                    if self._is_valid_base32_key(key):
                        try:
                            import hmac
                            import hashlib
                            import struct
                            import time

                            decoded_key = base64.b32decode(key)
                            timestamp = int(time.time()) // 30
                            msg = struct.pack('>Q', timestamp)
                            hmac_digest = hmac.new(decoded_key, msg, hashlib.sha1).digest()
                            offset = hmac_digest[-1] & 0x0f
                            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
                            code = (code & 0x7fffffff) % 1000000

                            secret['test_totp'] = f"{code:06d}"
                            secret['verified'] = True
                            secret['key_length'] = len(key)

                            issuer = secret.get('issuer', 'Unknown')
                            account = secret.get('account', 'Unknown')
                            print(f"✅ 验证密钥: {issuer} - {account}")
                            print(f"   🔢 测试验证码: {code:06d}")
                            print(f"   📊 置信度: {secret.get('confidence', 'unknown')}")

                        except Exception as e:
                            secret['verified'] = False
                            secret['error'] = str(e)
                            print(f"❌ 密钥验证失败: {key[:8]}... - {e}")

                        unique_secrets.append(secret)

            # 按置信度排序
            confidence_order = {'high': 3, 'medium': 2, 'low': 1}
            unique_secrets.sort(key=lambda x: confidence_order.get(x.get('confidence', 'low'), 0), reverse=True)

            print(f"✅ 验证完成: {len(unique_secrets)} 个有效密钥")
            print()
            return unique_secrets

        except Exception as e:
            log_error(f"[Root提取] 密钥验证失败: {e}", component="RootExtractor")
            return []

    async def _save_root_results(self, secrets: List[Dict[str, Any]]):
        """保存Root提取结果"""
        try:
            if not secrets:
                return

            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存到JSON文件
            output_dir = Path("./fb_2fa_results/root_extraction/")
            output_dir.mkdir(parents=True, exist_ok=True)

            json_file = output_dir / f"root_extracted_secrets_{self.emulator_id}_{timestamp}.json"

            result_data = {
                'emulator_id': self.emulator_id,
                'extraction_time': datetime.datetime.now().isoformat(),
                'extraction_method': 'root_authenticator_extraction',
                'total_secrets': len(secrets),
                'secrets': secrets
            }

            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)

            # 保存为AndOTP备份格式
            andotp_file = output_dir / f"andotp_backup_{self.emulator_id}_{timestamp}.json"

            andotp_data = []
            for secret in secrets:
                andotp_entry = {
                    "secret": secret['secret_key'],
                    "issuer": secret.get('issuer', 'Unknown'),
                    "label": secret.get('account', 'Unknown'),
                    "digits": 6,
                    "type": "TOTP",
                    "algorithm": "SHA1",
                    "thumbnail": "Default",
                    "last_used": 0,
                    "used_frequency": 0,
                    "period": 30
                }
                andotp_data.append(andotp_entry)

            with open(andotp_file, 'w', encoding='utf-8') as f:
                json.dump(andotp_data, f, indent=2, ensure_ascii=False)

            # 保存到文本文件
            txt_file = output_dir / f"root_extracted_secrets_{self.emulator_id}_{timestamp}.txt"

            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(f"2FA Root权限提取结果\n")
                f.write(f"模拟器ID: {self.emulator_id}\n")
                f.write(f"提取时间: {result_data['extraction_time']}\n")
                f.write(f"提取方法: Root权限认证器扫描\n")
                f.write(f"总密钥数: {len(secrets)}\n")
                f.write("=" * 50 + "\n\n")

                for i, secret in enumerate(secrets, 1):
                    f.write(f"{i}. 密钥: {secret['secret_key']}\n")
                    f.write(f"   发行方: {secret.get('issuer', 'Unknown')}\n")
                    f.write(f"   账户: {secret.get('account', 'Unknown')}\n")
                    f.write(f"   来源: {secret['source']}\n")
                    f.write(f"   提取方法: {secret['method']}\n")
                    f.write(f"   置信度: {secret.get('confidence', 'unknown')}\n")
                    if secret.get('verified'):
                        f.write(f"   测试验证码: {secret.get('test_totp', 'N/A')}\n")
                    f.write(f"   验证状态: {'✅ 有效' if secret.get('verified') else '❌ 无效'}\n")
                    f.write("-" * 30 + "\n")

            print(f"💾 Root提取结果已保存:")
            print(f"   📄 详细结果: {json_file}")
            print(f"   📄 AndOTP备份: {andotp_file}")
            print(f"   📄 文本报告: {txt_file}")

        except Exception as e:
            log_error(f"[Root提取] 保存结果失败: {e}", component="RootExtractor")

    async def _display_root_results(self, secrets: List[Dict[str, Any]]):
        """显示Root提取结果"""
        try:
            print("\n" + "=" * 50)
            print("🎯 2FA Root权限提取结果")
            print("=" * 50)

            if not secrets:
                print("❌ Root权限提取未找到任何2FA密钥")
                print("\n💡 可能的原因:")
                print("1. 没有安装任何认证器应用")
                print("2. 认证器应用中没有配置任何账户")
                print("3. 应用使用了不支持的数据格式")
                print("4. 数据被加密且无法解密")
                print("\n🔧 建议:")
                print("1. 安装Google Authenticator或其他认证器应用")
                print("2. 在认证器中添加一些测试账户")
                print("3. 重新运行提取工具")
                return

            print(f"✅ Root权限提取找到 {len(secrets)} 个2FA密钥:")
            print()

            for i, secret in enumerate(secrets, 1):
                confidence = secret.get('confidence', 'unknown').upper()
                issuer = secret.get('issuer', 'Unknown')
                account = secret.get('account', 'Unknown')

                print(f"🔑 密钥 {i} (置信度: {confidence}):")
                print(f"   发行方: {issuer}")
                print(f"   账户: {account}")
                print(f"   密钥: {secret['secret_key']}")
                print(f"   来源: {secret['source']}")
                print(f"   提取方法: {secret['method']}")

                if secret.get('verified'):
                    print(f"   ✅ 验证状态: 有效")
                    print(f"   🔢 当前验证码: {secret.get('test_totp', 'N/A')}")
                    print(f"   📏 密钥长度: {secret.get('key_length', 0)} 字符")
                else:
                    print(f"   ❌ 验证状态: 无效")
                    if secret.get('error'):
                        print(f"   ⚠️  错误: {secret['error']}")

                print("-" * 30)

            print("\n🎉 Root权限提取成功!")
            print("💡 使用建议:")
            print("1. 优先使用置信度为'HIGH'的密钥")
            print("2. 可以直接导入AndOTP备份文件到认证器应用")
            print("3. 验证生成的验证码是否与原应用匹配")
            print("4. 建议备份这些密钥以防丢失")

        except Exception as e:
            log_error(f"[Root提取] 显示结果失败: {e}", component="RootExtractor")

    async def _cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir, ignore_errors=True)

            # 清理模拟器上的临时文件
            self.task.ld.execute_ld(self.emulator_id, "rm -f /sdcard/temp_*.db")
        except Exception:
            pass

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2

        print("🔓 Facebook 2FA Root权限提取工具")
        print("⚠️  基于android-otp-extractor方法")
        print("⚠️  需要完全root权限")
        print("⚠️  扫描所有认证器应用")
        print("⚠️  仅用于合法的账户恢复目的")
        print()

        confirm = input("确认继续Root权限提取? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return

        # 创建Root权限提取器
        extractor = RootTwoFactorExtractor(emulator_id)

        # 运行Root权限提取
        await extractor.run_root_extraction()

        print("\n" + "=" * 50)
        print("✅ 2FA Root权限提取完成")

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
