{"emulator_id": 2, "test_time": "2025-07-31T12:57:39.515323", "total_tests": 4, "successful_tests": 0, "test_results": [{"test_name": "基础连接测试", "success": false, "error": "Dnconsole.__init__() missing 2 required positional arguments: 'base_path' and 'share_path'"}, {"test_name": "提取器功能测试", "success": false, "error": "Dnconsole.__init__() missing 2 required positional arguments: 'base_path' and 'share_path'"}, {"test_name": "任务执行器测试", "success": false, "error": "'LeiDianNativeAPI' object has no attribute 'ld'"}, {"test_name": "数据格式验证测试", "success": false, "error": "Dnconsole.__init__() missing 2 required positional arguments: 'base_path' and 'share_path'"}]}