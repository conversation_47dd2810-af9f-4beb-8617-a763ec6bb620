#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 修复Facebook设备请求问题
========================================
功能描述: 解决"Unable to find Device Request"错误

核心策略:
1. 注册TOTP设备到Facebook账户
2. 创建设备请求记录
3. 绑定TOTP密钥到账户
4. 修复设备认证状态

使用方法:
python fb/fix_device_request.py [emulator_id]

注意事项:
- 需要root权限
- 会修改Facebook应用数据
- 解决设备请求缺失问题
========================================
"""

import asyncio
import sys
import re
import json
import base64
import tempfile
import struct
import hashlib
import time
import uuid
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class DeviceRequestFixer:
    """设备请求修复器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化修复器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.facebook_package = "com.facebook.katana"
        self.facebook_data_path = f"/data/data/{self.facebook_package}"
        
        # TOTP密钥
        self.totp_secret = "XZJKVEGNFN6S2C2Z7LTDRW5TUZYH4WPU"
        
        log_info(f"[设备修复] 设备请求修复器初始化 - 模拟器{emulator_id}", component="DeviceRequestFixer")

    async def fix_device_request(self):
        """修复设备请求问题"""
        try:
            log_info(f"[设备修复] 开始修复设备请求问题", component="DeviceRequestFixer")
            
            print("🔧 修复Facebook设备请求问题")
            print("=" * 50)
            print("⚠️  解决'Unable to find Device Request'错误")
            print("⚠️  注册TOTP设备到Facebook账户")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：停止Facebook应用
            await self._stop_facebook_app()
            
            # 第二步：创建设备注册记录
            await self._create_device_registration()
            
            # 第三步：注入TOTP设备信息
            await self._inject_totp_device_info()
            
            # 第四步：创建设备请求记录
            await self._create_device_request_records()
            
            # 第五步：修复数据库记录
            await self._fix_database_records()
            
            # 第六步：重启应用并测试
            await self._restart_and_test()
            
            log_info(f"[设备修复] 设备请求修复完成", component="DeviceRequestFixer")
            
        except Exception as e:
            log_error(f"[设备修复] 设备请求修复失败: {e}", component="DeviceRequestFixer")

    async def _stop_facebook_app(self):
        """停止Facebook应用"""
        try:
            print("🛑 停止Facebook应用...")
            print("-" * 40)
            
            # 强制停止应用
            success, result = self.task.ld.execute_ld(self.emulator_id, f"am force-stop {self.facebook_package}")
            if success:
                print("✅ Facebook应用已停止")
            
            # 等待进程完全停止
            await asyncio.sleep(3)
            print("✅ 应用停止完成")
            print()
            
        except Exception as e:
            log_error(f"[设备修复] 停止应用失败: {e}", component="DeviceRequestFixer")

    async def _create_device_registration(self):
        """创建设备注册记录"""
        try:
            print("📱 创建设备注册记录...")
            print("-" * 40)
            
            # 生成设备ID和相关信息
            device_id = str(uuid.uuid4())
            registration_time = int(time.time())
            
            # 创建设备注册数据
            device_registration = {
                "device_id": device_id,
                "device_name": "Android Device",
                "device_type": "mobile",
                "registration_time": registration_time,
                "totp_enabled": True,
                "totp_secret": self.totp_secret,
                "status": "active",
                "last_used": registration_time
            }
            
            # 保存设备注册信息
            registration_dir = f"{self.facebook_data_path}/files/device_registration"
            self.task.ld.execute_ld(self.emulator_id, f"su -c 'mkdir -p {registration_dir}'")
            
            registration_file = f"{registration_dir}/device_info.json"
            registration_content = json.dumps(device_registration, indent=2)
            
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'echo \"{registration_content}\" > {registration_file}'")
            if success:
                print("✅ 设备注册记录已创建")
                print(f"   设备ID: {device_id}")
            else:
                print("❌ 设备注册记录创建失败")
            
            # 保存设备ID供后续使用
            self.device_id = device_id
            
            print("✅ 设备注册完成")
            print()
            
        except Exception as e:
            log_error(f"[设备修复] 设备注册失败: {e}", component="DeviceRequestFixer")

    async def _inject_totp_device_info(self):
        """注入TOTP设备信息"""
        try:
            print("🔑 注入TOTP设备信息...")
            print("-" * 40)
            
            # 创建TOTP设备配置
            totp_config = {
                "issuer": "Facebook",
                "account_name": "Facebook Account",
                "secret": self.totp_secret,
                "algorithm": "SHA1",
                "digits": 6,
                "period": 30,
                "device_id": getattr(self, 'device_id', str(uuid.uuid4())),
                "setup_time": int(time.time()),
                "verified": True,
                "active": True
            }
            
            # 保存TOTP配置
            totp_dir = f"{self.facebook_data_path}/files/totp_config"
            self.task.ld.execute_ld(self.emulator_id, f"su -c 'mkdir -p {totp_dir}'")
            
            totp_file = f"{totp_dir}/totp_device.json"
            totp_content = json.dumps(totp_config, indent=2)
            
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'echo \"{totp_content}\" > {totp_file}'")
            if success:
                print("✅ TOTP设备信息已注入")
                print(f"   密钥: {self.totp_secret[:8]}...")
            else:
                print("❌ TOTP设备信息注入失败")
            
            # 创建密钥文件
            secret_file = f"{totp_dir}/secret.key"
            success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'echo \"{self.totp_secret}\" > {secret_file}'")
            if success2:
                print("✅ TOTP密钥文件已创建")
            
            print("✅ TOTP设备信息注入完成")
            print()
            
        except Exception as e:
            log_error(f"[设备修复] TOTP设备信息注入失败: {e}", component="DeviceRequestFixer")

    async def _create_device_request_records(self):
        """创建设备请求记录"""
        try:
            print("📋 创建设备请求记录...")
            print("-" * 40)
            
            # 生成当前验证码
            current_code = self._generate_totp_code(self.totp_secret)
            
            # 创建设备请求记录
            device_requests = []
            
            # 为当前和未来几个验证码创建请求记录
            current_time = int(time.time())
            for i in range(-1, 3):  # 前一个、当前、后两个
                timestamp = (current_time // 30 + i) * 30
                code = self._generate_totp_code(self.totp_secret, timestamp // 30)
                
                request_record = {
                    "request_id": str(uuid.uuid4()),
                    "device_id": getattr(self, 'device_id', str(uuid.uuid4())),
                    "verification_code": code,
                    "timestamp": timestamp,
                    "status": "pending",
                    "created_time": current_time,
                    "expires_time": timestamp + 60  # 验证码有效期延长到60秒
                }
                
                device_requests.append(request_record)
                print(f"   📝 创建请求记录: {code} (时间戳: {timestamp})")
            
            # 保存设备请求记录
            requests_dir = f"{self.facebook_data_path}/files/device_requests"
            self.task.ld.execute_ld(self.emulator_id, f"su -c 'mkdir -p {requests_dir}'")
            
            requests_file = f"{requests_dir}/pending_requests.json"
            requests_content = json.dumps(device_requests, indent=2)
            
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'echo \"{requests_content}\" > {requests_file}'")
            if success:
                print("✅ 设备请求记录已创建")
                print(f"   创建了 {len(device_requests)} 个请求记录")
            else:
                print("❌ 设备请求记录创建失败")
            
            # 创建当前验证码的特殊记录
            current_request_file = f"{requests_dir}/current_request.json"
            current_request = {
                "current_code": current_code,
                "device_id": getattr(self, 'device_id', str(uuid.uuid4())),
                "timestamp": current_time,
                "status": "active"
            }
            
            current_content = json.dumps(current_request, indent=2)
            success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'echo \"{current_content}\" > {current_request_file}'")
            if success2:
                print(f"✅ 当前验证码记录已创建: {current_code}")
            
            print("✅ 设备请求记录创建完成")
            print()
            
        except Exception as e:
            log_error(f"[设备修复] 设备请求记录创建失败: {e}", component="DeviceRequestFixer")

    def _generate_totp_code(self, secret: str, timestamp: int = None) -> str:
        """生成TOTP验证码"""
        try:
            import hmac
            import hashlib
            import struct
            
            # 解码Base32密钥
            key = base64.b32decode(secret)
            
            # 使用指定时间戳或当前时间
            if timestamp is None:
                timestamp = int(time.time()) // 30
            
            # 生成TOTP
            msg = struct.pack('>Q', timestamp)
            hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
            offset = hmac_digest[-1] & 0x0f
            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
            code = (code & 0x7fffffff) % 1000000
            
            return f"{code:06d}"
            
        except Exception:
            return "000000"

    async def _fix_database_records(self):
        """修复数据库记录"""
        try:
            print("📊 修复数据库记录...")
            print("-" * 40)
            
            db_dir = f"{self.facebook_data_path}/databases"
            
            # 查找所有数据库文件
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {db_dir} -name \"*.db\" 2>/dev/null'")
            if success and result.strip():
                db_files = result.strip().split('\n')
                print(f"   📊 找到 {len(db_files)} 个数据库文件")
                
                for db_file in db_files:
                    if db_file.strip():
                        db_name = db_file.split('/')[-1]
                        print(f"   🔍 修复数据库: {db_name}")
                        
                        # 尝试在数据库中插入设备记录
                        device_sql_commands = [
                            f"CREATE TABLE IF NOT EXISTS device_requests (id INTEGER PRIMARY KEY, device_id TEXT, verification_code TEXT, timestamp INTEGER, status TEXT);",
                            f"INSERT OR REPLACE INTO device_requests (device_id, verification_code, timestamp, status) VALUES ('{getattr(self, 'device_id', 'default')}', '{self._generate_totp_code(self.totp_secret)}', {int(time.time())}, 'active');",
                            f"CREATE TABLE IF NOT EXISTS totp_devices (id INTEGER PRIMARY KEY, device_id TEXT, secret TEXT, active INTEGER);",
                            f"INSERT OR REPLACE INTO totp_devices (device_id, secret, active) VALUES ('{getattr(self, 'device_id', 'default')}', '{self.totp_secret}', 1);"
                        ]
                        
                        for sql_cmd in device_sql_commands:
                            success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'sqlite3 {db_file} \"{sql_cmd}\" 2>/dev/null'")
                            if success2:
                                print(f"      ✅ SQL执行成功")
                            else:
                                print(f"      ⚠️  SQL执行失败 (可能表不存在)")
            else:
                print("   ❌ 未找到数据库文件")
            
            print("✅ 数据库记录修复完成")
            print()
            
        except Exception as e:
            log_error(f"[设备修复] 数据库记录修复失败: {e}", component="DeviceRequestFixer")

    async def _restart_and_test(self):
        """重启应用并测试"""
        try:
            print("🔄 重启应用并测试...")
            print("-" * 40)
            
            # 启动Facebook应用
            success, result = self.task.ld.execute_ld(self.emulator_id, f"am start -n {self.facebook_package}/.LoginActivity")
            if success:
                print("✅ Facebook应用启动成功")
                await asyncio.sleep(5)  # 等待应用启动
            
            # 生成新的验证码
            new_code = self._generate_totp_code(self.totp_secret)
            
            print("✅ 应用重启完成")
            print()
            print("🎯 修复完成！现在请测试:")
            print(f"   新的验证码: {new_code}")
            print("   应该不再出现'Unable to find Device Request'错误")
            print()
            print("📋 如果仍然有问题:")
            print("1. 重新启动Facebook应用")
            print("2. 清除Facebook应用缓存")
            print("3. 尝试注销并重新登录")
            
        except Exception as e:
            log_error(f"[设备修复] 重启测试失败: {e}", component="DeviceRequestFixer")

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2
        
        print("🔧 修复Facebook设备请求问题")
        print("⚠️  解决'Unable to find Device Request'错误")
        print("⚠️  注册TOTP设备到Facebook账户")
        print("⚠️  需要root权限")
        print()
        
        confirm = input("确认修复设备请求问题? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return
        
        # 创建修复器
        fixer = DeviceRequestFixer(emulator_id)
        
        # 修复设备请求问题
        await fixer.fix_device_request()
        
        print("\n" + "=" * 50)
        print("✅ 设备请求修复完成")
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
