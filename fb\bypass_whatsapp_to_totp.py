#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 绕过WhatsApp验证直接进入TOTP设置
========================================
功能描述: 绕过WhatsApp验证，直接访问TOTP认证器设置

核心策略:
1. 识别当前是WhatsApp验证界面
2. 绕过WhatsApp验证流程
3. 直接跳转到TOTP认证器设置
4. 强制显示TOTP设置选项

使用方法:
python fb/bypass_whatsapp_to_totp.py [emulator_id]

注意事项:
- 需要root权限
- 会修改Facebook验证流程
- 直接跳转到TOTP设置
========================================
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error

class WhatsAppToTOTPBypass:
    """WhatsApp到TOTP绕过工具"""
    
    def __init__(self, emulator_id: int = 2):
        self.emulator_id = emulator_id
        self.task = None
        self.facebook_package = "com.facebook.katana"
        
    async def bypass_whatsapp_to_totp(self):
        """绕过WhatsApp验证直接进入TOTP设置"""
        try:
            print("🔓 绕过WhatsApp验证直接进入TOTP设置")
            print("=" * 50)
            print("⚠️  检测到WhatsApp验证界面")
            print("⚠️  将绕过WhatsApp验证，直接进入TOTP设置")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：关闭当前WhatsApp验证弹窗
            await self._close_whatsapp_dialog()
            
            # 第二步：直接跳转到TOTP设置
            await self._jump_to_totp_settings()
            
            # 第三步：强制显示TOTP选项
            await self._force_show_totp_options()
            
            # 第四步：生成当前验证码供测试
            await self._generate_test_codes()
            
            print("✅ 绕过完成！")
            print()
            print("📋 现在应该能看到TOTP认证器设置选项")
            print("💡 如果看到QR码或密钥输入框，说明成功了！")
            
        except Exception as e:
            log_error(f"WhatsApp到TOTP绕过失败: {e}", component="WhatsAppToTOTPBypass")

    async def _close_whatsapp_dialog(self):
        """关闭WhatsApp验证弹窗"""
        print("❌ 关闭WhatsApp验证弹窗...")
        
        # 方法1: 点击关闭按钮
        close_actions = [
            "input keyevent KEYCODE_BACK",  # 返回键
            "input tap 745 91",  # 点击X按钮 (根据截图位置)
            "input keyevent KEYCODE_ESCAPE"  # ESC键
        ]
        
        for action in close_actions:
            success, result = self.task.ld.execute_ld(self.emulator_id, action)
            if success:
                print(f"   ✅ 执行: {action}")
                await asyncio.sleep(1)
        
        # 方法2: 强制关闭弹窗
        self.task.ld.execute_ld(self.emulator_id, "input keyevent KEYCODE_HOME")
        await asyncio.sleep(2)
        
        print("✅ WhatsApp弹窗关闭完成")

    async def _jump_to_totp_settings(self):
        """直接跳转到TOTP设置"""
        print("🎯 直接跳转到TOTP设置...")
        
        # 重新启动Facebook并直接跳转
        jump_commands = [
            f"am start -n {self.facebook_package}/.LoginActivity",
            "sleep 3",
            # 尝试多种TOTP设置URL
            "am start -a android.intent.action.VIEW -d 'https://m.facebook.com/settings/security/two_factor/authenticator/'",
            "sleep 2",
            "am start -a android.intent.action.VIEW -d 'https://www.facebook.com/security/2fac/setup/'",
            "sleep 2",
            "am start -a android.intent.action.VIEW -d 'fb://settings/security/two_factor/setup/'",
            "sleep 2"
        ]
        
        for cmd in jump_commands:
            if "sleep" in cmd:
                await asyncio.sleep(int(cmd.split()[1]))
            else:
                success, result = self.task.ld.execute_ld(self.emulator_id, cmd)
                if success:
                    print(f"   ✅ 执行: {cmd.split()[-1]}")
        
        print("✅ TOTP设置跳转完成")

    async def _force_show_totp_options(self):
        """强制显示TOTP选项"""
        print("🔧 强制显示TOTP选项...")
        
        # 模拟点击和滑动操作来寻找TOTP选项
        ui_actions = [
            "input swipe 360 800 360 400",  # 向上滑动
            "sleep 1",
            "input tap 360 600",  # 点击中间区域
            "sleep 1",
            "input swipe 360 400 360 800",  # 向下滑动
            "sleep 1",
            # 尝试点击可能的TOTP选项位置
            "input tap 360 500",  # 中间偏下
            "sleep 1",
            "input tap 360 400",  # 中间
            "sleep 1"
        ]
        
        for action in ui_actions:
            if "sleep" in action:
                await asyncio.sleep(int(action.split()[1]))
            else:
                success, result = self.task.ld.execute_ld(self.emulator_id, action)
                if success:
                    print(f"   ✅ UI操作: {action}")
        
        # 发送特殊广播来强制显示TOTP选项
        totp_broadcasts = [
            "am broadcast -a com.facebook.katana.ACTION_SHOW_TOTP_SETUP",
            "am broadcast -a com.facebook.katana.ACTION_2FA_AUTHENTICATOR",
            "am broadcast -a android.intent.action.VIEW --es url 'fb://2fa/setup/'"
        ]
        
        for broadcast in totp_broadcasts:
            success, result = self.task.ld.execute_ld(self.emulator_id, broadcast)
            if success:
                print(f"   ✅ 广播: {broadcast.split()[-1]}")
            await asyncio.sleep(1)
        
        print("✅ TOTP选项强制显示完成")

    async def _generate_test_codes(self):
        """生成测试验证码"""
        print("🔢 生成测试验证码...")
        
        # 使用您的TOTP密钥生成验证码
        totp_secret = "XZJKVEGNFN6S2C2Z7LTDRW5TUZYH4WPU"
        
        try:
            import base64, hmac, hashlib, struct
            
            key = base64.b32decode(totp_secret)
            current_time = int(time.time())
            
            # 生成当前和下一个验证码
            codes = []
            for offset in [0, 30]:
                timestamp = (current_time + offset) // 30
                msg = struct.pack('>Q', timestamp)
                hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
                code_offset = hmac_digest[-1] & 0x0f
                code = struct.unpack('>I', hmac_digest[code_offset:code_offset+4])[0]
                code = (code & 0x7fffffff) % 1000000
                codes.append(f"{code:06d}")
            
            print(f"   🔑 当前验证码: {codes[0]}")
            print(f"   🔑 下个验证码: {codes[1]}")
            print()
            print("💡 如果看到TOTP设置界面，请使用这些验证码测试")
            
        except Exception as e:
            print(f"   ❌ 验证码生成失败: {e}")
        
        print("✅ 测试验证码生成完成")

    async def _provide_manual_instructions(self):
        """提供手动操作指导"""
        print("📋 手动操作指导")
        print("=" * 50)
        
        print("如果自动绕过不成功，请手动执行以下步骤：")
        print()
        print("1. 关闭当前的WhatsApp验证弹窗")
        print("   - 点击右上角的 X 按钮")
        print("   - 或者按返回键")
        print()
        print("2. 寻找TOTP认证器选项")
        print("   - 在安全设置中寻找'认证器应用'")
        print("   - 或者'身份验证应用'")
        print("   - 或者'TOTP'选项")
        print()
        print("3. 如果只看到WhatsApp和短信选项")
        print("   - 点击'添加其他方法'")
        print("   - 或者'更多选项'")
        print("   - 寻找'认证器应用'选项")
        print()
        print("4. 一旦进入TOTP设置")
        print("   - 会显示QR码或密钥")
        print("   - 记录下来供我们验证")

async def main():
    """主函数"""
    try:
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2
        
        print("🔓 绕过WhatsApp验证直接进入TOTP设置")
        print("⚠️  检测到您遇到了WhatsApp验证界面")
        print("⚠️  将帮您绕过并进入TOTP认证器设置")
        print()
        
        confirm = input("确认绕过WhatsApp验证进入TOTP设置? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return
        
        bypass_tool = WhatsAppToTOTPBypass(emulator_id)
        await bypass_tool.bypass_whatsapp_to_totp()
        
        print("\n" + "=" * 50)
        print("✅ WhatsApp绕过完成")
        print("💡 现在查看是否能看到TOTP认证器设置选项")
        
        # 提供手动指导
        await bypass_tool._provide_manual_instructions()
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
