#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Facebook 2FA提取任务执行器 - 基于现有架构的简化实现
========================================
功能描述: Facebook双因子认证密钥提取任务，100%继承Instagram任务架构

技术特点:
- 架构继承: 100%继承InstagramDMTask的完整架构和统一设置
- 配置管理: 使用现有的配置管理系统
- API集成: 使用现有的雷电模拟器API
- 日志系统: 使用统一的日志管理器

调用关系: 被异步桥梁调用，执行具体的Facebook 2FA提取业务逻辑
注意事项: 基于现有技术参考，保持架构一致性
========================================
"""

import asyncio
import time
import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional

# 导入项目核心模块 - 使用现有架构
from core.logger_manager import log_info, log_error, log_warning
from core.simple_config import get_config_manager

# ============================================================================
# 🎯 1. Facebook 2FA任务执行器 - 基于Instagram任务架构
# ============================================================================

class FacebookTwoFactorTask:
    """🎯 Facebook 2FA任务执行器 - 基于现有架构的简化实现"""

    def __init__(self, emulator_id: int):
        """
        初始化Facebook 2FA任务执行器 - 参考Instagram任务初始化

        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id

        # ========================================================================
        # 🎯 1.1 基础属性初始化 - 参考Instagram任务
        # ========================================================================

        # 任务状态
        self.task_start_time = time.time()
        self.task_timeout = 300  # 5分钟超时

        # ========================================================================
        # 🎯 1.2 雷电模拟器API初始化 - 完全参考Instagram任务
        # ========================================================================

        self.ld = None
        self._init_leidian_api()

        # ========================================================================
        # 🎯 1.3 配置系统初始化 - 参考Instagram任务
        # ========================================================================

        self._load_config()

        log_info(f"[模拟器{emulator_id}] Facebook 2FA任务执行器初始化完成", component="FacebookTwoFactorTask")

    def _init_leidian_api(self):
        """初始化雷电模拟器API - 完全参考Instagram任务实现"""
        try:
            # 🎯 获取配置管理器
            config_manager = get_config_manager()

            # 🎯 获取模拟器路径配置
            emulator_path = config_manager.get("emulator_path", "G:/leidian/LDPlayer9")
            shared_path = config_manager.get("basic_config.emulator_shared_path", "C:/Users/<USER>/Documents/leidian9")

            # 🎯 如果共享路径为空，使用默认路径
            if not shared_path:
                shared_path = "C:/Users/<USER>/Documents/leidian9"

            # 🎯 创建LeiDian API实例 - 完全参考Instagram任务
            from core.leidianapi.LeiDian_Reorganized import Dnconsole
            self.ld = Dnconsole(base_path=emulator_path, share_path=shared_path)

            # 🎯 设置emulator_id属性 - 重要：用于各种操作
            self.ld.emulator_id = self.emulator_id

            log_info(f"[模拟器{self.emulator_id}] 雷电模拟器API初始化成功", component="FacebookTwoFactorTask")
            log_info(f"[模拟器{self.emulator_id}] 模拟器路径: {emulator_path}", component="FacebookTwoFactorTask")
            log_info(f"[模拟器{self.emulator_id}] 共享路径: {shared_path}", component="FacebookTwoFactorTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 雷电模拟器API初始化失败: {e}", component="FacebookTwoFactorTask")
            self.ld = None

    def _load_config(self):
        """加载任务配置 - 参考Instagram任务配置加载"""
        try:
            config_manager = get_config_manager()

            # 🎯 FB 2FA任务的默认配置
            self.task_config = {
                'task_timeout_seconds': 300,
                'output_directory': './fb_2fa_results/',
                'save_to_file': True,
                'cleanup_temp_files': True
            }

            # 🎯 从配置文件获取基础配置
            basic_config = config_manager.get("basic_config", {})
            if basic_config:
                self.task_config.update(basic_config)

            # 🎯 更新任务超时时间
            timeout_minutes = self.task_config.get('task_timeout_minutes', 5)
            self.task_timeout = timeout_minutes * 60

            log_info(f"[模拟器{self.emulator_id}] FB 2FA任务配置加载完成", component="FacebookTwoFactorTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 加载任务配置失败: {e}", component="FacebookTwoFactorTask")
            # 使用默认配置
            self.task_config = {
                'task_timeout_seconds': 300,
                'output_directory': './fb_2fa_results/',
                'save_to_file': True,
                'cleanup_temp_files': True
            }

    # ------------------------------------------------------------------------
    # 🎯 2. 主要执行方法 - 参考Instagram任务的execute方法
    # ------------------------------------------------------------------------

    async def execute(self) -> Dict[str, Any]:
        """
        执行Facebook 2FA提取任务的主入口方法 - 参考Instagram任务架构

        Returns:
            Dict[str, Any]: 任务执行结果
        """
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始执行Facebook 2FA提取任务", component="FacebookTwoFactorTask")

            # 🎯 检查雷电API是否初始化成功
            if not self.ld:
                return self._create_failure_result("雷电模拟器API初始化失败")

            # 🎯 阶段一：基础检测 - 参考Instagram任务的应用检测
            if not await self._stage1_basic_check():
                return self._create_failure_result("基础检测失败")

            # 🎯 阶段二：2FA密钥提取 - 核心业务逻辑
            extraction_result = await self._stage2_extract_2fa_secrets()

            # 🎯 阶段三：结果处理和保存
            if not await self._stage3_process_results(extraction_result):
                return self._create_failure_result("结果处理失败")

            # 🎯 创建成功结果
            result = self._create_success_result(extraction_result)
            log_info(f"[模拟器{self.emulator_id}] Facebook 2FA提取任务执行成功，找到{len(extraction_result.get('secrets', []))}个密钥", component="FacebookTwoFactorTask")
            return result

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] Facebook 2FA提取任务执行失败: {e}", component="FacebookTwoFactorTask")
            return self._create_failure_result(f"任务执行异常: {str(e)}")

    async def _stage1_basic_check(self) -> bool:
        """阶段一：基础检测 - 参考Instagram任务的应用检测逻辑"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 阶段一：基础检测", component="FacebookTwoFactorTask")

            # 🎯 检查模拟器状态 - 参考Instagram任务
            is_running, is_android, info = self.ld.is_running(self.emulator_id)
            if not is_running:
                log_error(f"[模拟器{self.emulator_id}] 模拟器未运行", component="FacebookTwoFactorTask")
                return False

            if not is_android:
                log_error(f"[模拟器{self.emulator_id}] Android系统未就绪", component="FacebookTwoFactorTask")
                return False

            # 🎯 测试ADB连接
            success, result = self.ld.execute_ld(self.emulator_id, "echo 'test'")
            if not success or "test" not in result:
                log_error(f"[模拟器{self.emulator_id}] ADB连接测试失败", component="FacebookTwoFactorTask")
                return False

            # 🎯 创建输出目录
            output_dir = Path(self.task_config.get('output_directory', './fb_2fa_results/'))
            output_dir.mkdir(parents=True, exist_ok=True)

            log_info(f"[模拟器{self.emulator_id}] 阶段一完成：基础检测通过", component="FacebookTwoFactorTask")
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 阶段一失败: {e}", component="FacebookTwoFactorTask")
            return False

    async def _stage2_extract_2fa_secrets(self) -> Dict[str, Any]:
        """阶段二：2FA密钥提取 - 核心业务逻辑"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 阶段二：2FA密钥提取", component="FacebookTwoFactorTask")

            secrets = []
            extraction_methods = []

            # 🎯 方法1：从Facebook应用提取
            fb_secrets = await self._extract_from_facebook_apps()
            if fb_secrets:
                secrets.extend(fb_secrets)
                extraction_methods.append("facebook_apps")
                log_info(f"[模拟器{self.emulator_id}] 从Facebook应用提取到{len(fb_secrets)}个密钥", component="FacebookTwoFactorTask")

            # 🎯 方法2：从认证器应用提取
            auth_secrets = await self._extract_from_authenticator_apps()
            if auth_secrets:
                secrets.extend(auth_secrets)
                extraction_methods.append("authenticator_apps")
                log_info(f"[模拟器{self.emulator_id}] 从认证器应用提取到{len(auth_secrets)}个密钥", component="FacebookTwoFactorTask")

            # 🎯 去重处理
            unique_secrets = self._deduplicate_secrets(secrets)

            result = {
                'success': len(unique_secrets) > 0,
                'secrets': unique_secrets,
                'extraction_methods': "+".join(extraction_methods),
                'error_message': "" if unique_secrets else "未找到任何2FA密钥",
                'timestamp': time.time()
            }

            log_info(f"[模拟器{self.emulator_id}] 阶段二完成：提取到{len(unique_secrets)}个唯一密钥", component="FacebookTwoFactorTask")
            return result

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 阶段二失败: {e}", component="FacebookTwoFactorTask")
            return {
                'success': False,
                'secrets': [],
                'extraction_methods': "error",
                'error_message': str(e),
                'timestamp': time.time()
            }

    async def _extract_from_facebook_apps(self) -> List[Dict[str, Any]]:
        """从Facebook应用提取2FA密钥 - 简化实现"""
        try:
            secrets = []

            # Facebook相关应用包名
            facebook_packages = [
                "com.facebook.katana",     # Facebook主应用
                "com.facebook.orca",       # Messenger
                "com.facebook.mlite",      # Facebook Lite
            ]

            for package in facebook_packages:
                # 🎯 检查应用是否安装 - 使用现有API
                success, result = self.ld.execute_ld(self.emulator_id, f"pm list packages | grep {package}")
                if not success or package not in result:
                    continue

                log_info(f"[模拟器{self.emulator_id}] 检测到{package}，尝试提取数据", component="FacebookTwoFactorTask")

                # 🎯 尝试访问应用数据目录
                app_secrets = await self._extract_from_app_data(package)
                if app_secrets:
                    secrets.extend(app_secrets)

            return secrets

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从Facebook应用提取失败: {e}", component="FacebookTwoFactorTask")
            return []

    async def _extract_from_authenticator_apps(self) -> List[Dict[str, Any]]:
        """从认证器应用提取2FA密钥 - 简化实现"""
        try:
            secrets = []

            # 认证器应用包名
            authenticator_packages = [
                "com.google.android.apps.authenticator2",  # Google Authenticator
                "com.microsoft.msa.authenticator",         # Microsoft Authenticator
                "com.authy.authy",                         # Authy
            ]

            for package in authenticator_packages:
                # 🎯 检查应用是否安装
                success, result = self.ld.execute_ld(self.emulator_id, f"pm list packages | grep {package}")
                if not success or package not in result:
                    continue

                log_info(f"[模拟器{self.emulator_id}] 检测到认证器应用{package}，尝试提取数据", component="FacebookTwoFactorTask")

                # 🎯 尝试访问应用数据目录
                app_secrets = await self._extract_from_app_data(package)
                if app_secrets:
                    secrets.extend(app_secrets)

            return secrets

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从认证器应用提取失败: {e}", component="FacebookTwoFactorTask")
            return []

    async def _extract_from_app_data(self, package_name: str) -> List[Dict[str, Any]]:
        """从应用数据目录提取2FA密钥 - 简化实现"""
        try:
            secrets = []

            # 🎯 应用数据目录路径
            data_paths = [
                f"/data/data/{package_name}/databases/",
                f"/data/data/{package_name}/shared_prefs/",
                f"/data/data/{package_name}/files/",
            ]

            for data_path in data_paths:
                # 🎯 列出目录内容
                success, result = self.ld.execute_ld(self.emulator_id, f"ls -la {data_path}")
                if not success or "No such file" in result:
                    continue

                # 🎯 查找可能包含2FA数据的文件
                lines = result.strip().split('\n')
                for line in lines:
                    if not line.strip() or line.startswith('total'):
                        continue

                    parts = line.split()
                    if len(parts) >= 9:
                        filename = parts[-1]

                        # 🎯 检查是否为潜在的2FA文件
                        if self._is_potential_2fa_file(filename):
                            file_path = f"{data_path}/{filename}"
                            file_secrets = await self._extract_from_file(file_path)
                            if file_secrets:
                                secrets.extend(file_secrets)

            return secrets

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从应用数据{package_name}提取失败: {e}", component="FacebookTwoFactorTask")
            return []

    def _is_potential_2fa_file(self, filename: str) -> bool:
        """判断文件是否可能包含2FA数据 - 简化实现"""
        potential_keywords = [
            'auth', '2fa', 'totp', 'secret', 'account', 'token'
        ]

        filename_lower = filename.lower()
        return any(keyword in filename_lower for keyword in potential_keywords)

    async def _extract_from_file(self, file_path: str) -> List[Dict[str, Any]]:
        """从具体文件提取2FA密钥 - 简化实现"""
        try:
            # 🎯 尝试读取文件内容
            success, content = self.ld.execute_ld(self.emulator_id, f"cat {file_path}")
            if not success or not content or "No such file" in content:
                return []

            secrets = []

            # 🎯 简单的文本模式匹配
            import re

            # 查找可能的Base32密钥
            base32_pattern = r'[A-Z2-7]{16,}'
            matches = re.findall(base32_pattern, content)

            for match in matches:
                if len(match) >= 16:  # 最小密钥长度
                    secret = {
                        'service_name': 'Facebook',
                        'account': 'Unknown',
                        'secret_key': match,
                        'source_file': file_path,
                        'type': 'TOTP'
                    }
                    secrets.append(secret)

            return secrets

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从文件{file_path}提取失败: {e}", component="FacebookTwoFactorTask")
            return []

    def _deduplicate_secrets(self, secrets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重处理 - 简化实现"""
        try:
            seen = set()
            unique_secrets = []

            for secret in secrets:
                # 使用密钥作为唯一标识
                key = secret.get('secret_key', '')
                if key and key not in seen:
                    seen.add(key)
                    unique_secrets.append(secret)

            return unique_secrets

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 去重处理失败: {e}", component="FacebookTwoFactorTask")
            return secrets

    async def _stage3_process_results(self, extraction_result: Dict[str, Any]) -> bool:
        """阶段三：结果处理和保存 - 参考Instagram任务的结果处理"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 阶段三：结果处理和保存", component="FacebookTwoFactorTask")

            # 🎯 保存结果到文件
            if self.task_config.get('save_to_file', True):
                if not await self._save_results_to_file(extraction_result):
                    return False

            # 🎯 打印结果摘要
            await self._print_results_summary(extraction_result)

            log_info(f"[模拟器{self.emulator_id}] 阶段三完成：结果处理完成", component="FacebookTwoFactorTask")
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 阶段三失败: {e}", component="FacebookTwoFactorTask")
            return False

    async def _save_results_to_file(self, extraction_result: Dict[str, Any]) -> bool:
        """保存结果到文件 - 简化实现"""
        try:
            output_dir = Path(self.task_config.get('output_directory', './fb_2fa_results/'))

            # 🎯 生成文件名
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"fb_2fa_emulator{self.emulator_id}_{timestamp}.json"
            filepath = output_dir / filename

            # 🎯 准备保存的数据
            save_data = {
                'emulator_id': self.emulator_id,
                'extraction_result': extraction_result,
                'task_config': self.task_config,
                'timestamp': datetime.datetime.now().isoformat()
            }

            # 🎯 保存到JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            log_info(f"[模拟器{self.emulator_id}] 结果已保存到文件: {filepath}", component="FacebookTwoFactorTask")

            # 🎯 同时保存一个简化的密钥列表文件
            if extraction_result.get('secrets'):
                keys_filename = f"fb_2fa_keys_emulator{self.emulator_id}_{timestamp}.txt"
                keys_filepath = output_dir / keys_filename

                with open(keys_filepath, 'w', encoding='utf-8') as f:
                    f.write(f"Facebook 2FA密钥提取结果 - 模拟器{self.emulator_id}\n")
                    f.write(f"提取时间: {extraction_result.get('timestamp', '')}\n")
                    f.write(f"提取方法: {extraction_result.get('extraction_methods', '')}\n")
                    f.write("=" * 50 + "\n\n")

                    for i, secret in enumerate(extraction_result['secrets'], 1):
                        f.write(f"{i}. 服务: {secret.get('service_name', 'Unknown')}\n")
                        f.write(f"   账户: {secret.get('account', 'Unknown')}\n")
                        f.write(f"   密钥: {secret.get('secret_key', '')}\n")
                        f.write(f"   类型: {secret.get('type', 'TOTP')}\n")
                        f.write(f"   来源: {secret.get('source_file', 'Unknown')}\n")
                        f.write("-" * 30 + "\n")

                log_info(f"[模拟器{self.emulator_id}] 密钥列表已保存到文件: {keys_filepath}", component="FacebookTwoFactorTask")

            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 保存结果到文件失败: {e}", component="FacebookTwoFactorTask")
            return False

    async def _print_results_summary(self, extraction_result: Dict[str, Any]):
        """打印结果摘要 - 参考Instagram任务的日志输出"""
        try:
            log_info(f"[模拟器{self.emulator_id}] ========== Facebook 2FA提取结果摘要 ==========", component="FacebookTwoFactorTask")
            log_info(f"[模拟器{self.emulator_id}] 提取状态: {'成功' if extraction_result.get('success') else '失败'}", component="FacebookTwoFactorTask")
            log_info(f"[模拟器{self.emulator_id}] 提取方法: {extraction_result.get('extraction_methods', '')}", component="FacebookTwoFactorTask")
            log_info(f"[模拟器{self.emulator_id}] 密钥数量: {len(extraction_result.get('secrets', []))}", component="FacebookTwoFactorTask")

            if extraction_result.get('secrets'):
                log_info(f"[模拟器{self.emulator_id}] 密钥详情:", component="FacebookTwoFactorTask")
                for i, secret in enumerate(extraction_result['secrets'], 1):
                    secret_key = secret.get('secret_key', '')
                    masked_key = secret_key[:8] + "..." if len(secret_key) > 8 else secret_key
                    log_info(f"[模拟器{self.emulator_id}]   {i}. {secret.get('service_name', 'Unknown')} - {secret.get('account', 'Unknown')} - {masked_key}", component="FacebookTwoFactorTask")

            if extraction_result.get('error_message'):
                log_info(f"[模拟器{self.emulator_id}] 错误信息: {extraction_result.get('error_message')}", component="FacebookTwoFactorTask")

            log_info(f"[模拟器{self.emulator_id}] ===============================================", component="FacebookTwoFactorTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 打印结果摘要失败: {e}", component="FacebookTwoFactorTask")

    def _create_success_result(self, extraction_result: Dict[str, Any]) -> Dict[str, Any]:
        """创建成功结果 - 参考Instagram任务的结果格式"""
        return {
            'success': True,
            'emulator_id': self.emulator_id,
            'task_type': 'facebook_2fa_extraction',
            'extraction_result': extraction_result,
            'execution_time': time.time() - self.task_start_time,
            'timestamp': time.time()
        }

    def _create_failure_result(self, error_message: str) -> Dict[str, Any]:
        """创建失败结果 - 参考Instagram任务的结果格式"""
        return {
            'success': False,
            'emulator_id': self.emulator_id,
            'task_type': 'facebook_2fa_extraction',
            'error_message': error_message,
            'execution_time': time.time() - self.task_start_time,
            'timestamp': time.time()
        }

    async def _stage1_environment_check(self) -> bool:
        """阶段一：环境检测和准备"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 阶段一：环境检测和准备", component="FacebookTwoFactorTask")
            
            # 检查模拟器状态
            if not await self._check_emulator_status():
                return False
            
            # 检查ADB连接
            if not await self._check_adb_connection():
                return False
            
            # 创建输出目录
            if not await self._create_output_directory():
                return False
            
            # 初始化提取器
            self.extractor = FacebookTwoFactorExtractor(self.emulator_id)
            
            log_info(f"[模拟器{self.emulator_id}] 阶段一完成：环境检测通过", component="FacebookTwoFactorTask")
            return True
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 阶段一失败: {e}", component="FacebookTwoFactorTask")
            return False




    async def _save_results_to_file(self, extraction_result: Dict[str, Any]) -> bool:
        """保存结果到文件"""
        try:
            output_dir = Path(self.task_config.get('output_directory', './fb_2fa_results/'))

            # 生成文件名
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"fb_2fa_emulator{self.emulator_id}_{timestamp}.json"
            filepath = output_dir / filename

            # 准备保存的数据
            save_data = {
                'emulator_id': self.emulator_id,
                'extraction_result': extraction_result,
                'task_config': self.task_config,
                'timestamp': datetime.datetime.now().isoformat()
            }

            # 保存到JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            log_info(f"[模拟器{self.emulator_id}] 结果已保存到文件: {filepath}", component="FacebookTwoFactorTask")

            # 同时保存一个简化的密钥列表文件
            if extraction_result.get('secrets'):
                keys_filename = f"fb_2fa_keys_emulator{self.emulator_id}_{timestamp}.txt"
                keys_filepath = output_dir / keys_filename

                with open(keys_filepath, 'w', encoding='utf-8') as f:
                    f.write(f"Facebook 2FA密钥提取结果 - 模拟器{self.emulator_id}\n")
                    f.write(f"提取时间: {extraction_result.get('timestamp', '')}\n")
                    f.write(f"提取方法: {extraction_result.get('extraction_methods', '')}\n")
                    f.write("=" * 50 + "\n\n")

                    for i, secret in enumerate(extraction_result['secrets'], 1):
                        f.write(f"{i}. 服务: {secret.get('service_name', 'Unknown')}\n")
                        f.write(f"   账户: {secret.get('account', 'Unknown')}\n")
                        f.write(f"   密钥: {secret.get('secret_key', '')}\n")
                        f.write(f"   类型: {secret.get('type', 'TOTP')}\n")
                        f.write(f"   来源: {secret.get('source_file', 'Unknown')}\n")
                        f.write("-" * 30 + "\n")

                log_info(f"[模拟器{self.emulator_id}] 密钥列表已保存到文件: {keys_filepath}", component="FacebookTwoFactorTask")

            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 保存结果到文件失败: {e}", component="FacebookTwoFactorTask")
            return False

    async def _print_results_summary(self, extraction_result: Dict[str, Any]):
        """打印结果摘要"""
        try:
            log_info(f"[模拟器{self.emulator_id}] ========== Facebook 2FA提取结果摘要 ==========", component="FacebookTwoFactorTask")
            log_info(f"[模拟器{self.emulator_id}] 提取状态: {'成功' if extraction_result.get('success') else '失败'}", component="FacebookTwoFactorTask")
            log_info(f"[模拟器{self.emulator_id}] 提取方法: {extraction_result.get('extraction_methods', '')}", component="FacebookTwoFactorTask")
            log_info(f"[模拟器{self.emulator_id}] 密钥数量: {len(extraction_result.get('secrets', []))}", component="FacebookTwoFactorTask")

            if extraction_result.get('secrets'):
                log_info(f"[模拟器{self.emulator_id}] 密钥详情:", component="FacebookTwoFactorTask")
                for i, secret in enumerate(extraction_result['secrets'], 1):
                    secret_key = secret.get('secret_key', '')
                    masked_key = secret_key[:8] + "..." if len(secret_key) > 8 else secret_key
                    log_info(f"[模拟器{self.emulator_id}]   {i}. {secret.get('service_name', 'Unknown')} - {secret.get('account', 'Unknown')} - {masked_key}", component="FacebookTwoFactorTask")

            if extraction_result.get('error_message'):
                log_info(f"[模拟器{self.emulator_id}] 错误信息: {extraction_result.get('error_message')}", component="FacebookTwoFactorTask")

            log_info(f"[模拟器{self.emulator_id}] ===============================================", component="FacebookTwoFactorTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 打印结果摘要失败: {e}", component="FacebookTwoFactorTask")

    def _create_success_result(self, extraction_result: Dict[str, Any]) -> Dict[str, Any]:
        """创建成功结果"""
        return {
            'success': True,
            'emulator_id': self.emulator_id,
            'task_type': 'facebook_2fa_extraction',
            'extraction_result': extraction_result,
            'execution_time': time.time() - self.task_start_time,
            'timestamp': time.time()
        }

    def _create_failure_result(self, error_message: str) -> Dict[str, Any]:
        """创建失败结果"""
        return {
            'success': False,
            'emulator_id': self.emulator_id,
            'task_type': 'facebook_2fa_extraction',
            'error_message': error_message,
            'execution_time': time.time() - self.task_start_time,
            'timestamp': time.time()
        }

    async def _cleanup(self):
        """清理资源"""
        try:
            if self.extractor and self.task_config.get('cleanup_temp_files', True):
                self.extractor.cleanup()
                log_info(f"[模拟器{self.emulator_id}] 资源清理完成", component="FacebookTwoFactorTask")
        except Exception as e:
            log_warning(f"[模拟器{self.emulator_id}] 资源清理失败: {e}", component="FacebookTwoFactorTask")

    # ------------------------------------------------------------------------
    # 🎯 4. 超时检查
    # ------------------------------------------------------------------------

    def _check_timeout(self) -> bool:
        """检查是否超时"""
        elapsed = time.time() - self.task_start_time
        if elapsed > self.task_timeout:
            log_warning(f"[模拟器{self.emulator_id}] 任务执行超时: {elapsed:.1f}秒 > {self.task_timeout}秒", component="FacebookTwoFactorTask")
            return True
        return False
