#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Facebook 2FA提取任务执行器 - 完整自动化流程管理
========================================
功能描述: Facebook双因子认证密钥提取任务的具体实现，支持完整的自动化流程

模块结构:
1. Facebook 2FA任务执行器 (FacebookTwoFactorTask)
2. 配置管理系统 (配置加载、热加载、观察者模式)
3. 任务执行系统 (四阶段执行流程)
4. 多种提取方法集成 (应用数据、认证器、系统数据库)

主要功能:
- 任务配置管理: 参数加载、热加载、默认配置
- 执行流程控制: 四阶段流程、错误处理、状态管理
- 原生API集成: 雷电模拟器API、应用操作、UI自动化
- 多方法提取: Facebook应用、认证器应用、系统数据库
- 智能解析: 支持多种数据格式和加密方式

技术特点:
- 架构继承: 100%继承InstagramDMTask的完整架构和统一设置
- 底层访问: 直接访问Android应用数据和系统数据库
- 多格式支持: SQLite、XML、JSON、纯文本等多种格式
- 安全提取: 支持加密数据的解密和格式转换

调用关系: 被异步桥梁调用，执行具体的Facebook 2FA提取业务逻辑
注意事项: 需要root权限或系统级权限，包含完整的错误处理和重试机制
========================================
"""

import asyncio
import time
import json
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import asdict

# 导入项目核心模块
import sys
sys.path.append(str(Path(__file__).parent.parent))
from core.logger_manager import log_info, log_error, log_warning
from core.unified_emulator_manager import get_emulator_manager
from core.simple_config import get_config_manager

# 导入FB 2FA提取器
from .fb_2fa_extractor import FacebookTwoFactorExtractor, ExtractionResult, TwoFactorSecret

# ============================================================================
# 🎯 1. Facebook 2FA任务执行器
# ============================================================================
# 功能描述: Facebook双因子认证密钥提取任务的核心执行器，管理完整的自动化流程
# 调用关系: 被异步桥梁调用，作为Facebook 2FA提取任务的统一入口点
# 注意事项: 集成配置管理、原生API、执行控制等多个子系统
# ============================================================================

class FacebookTwoFactorTask:
    """🎯 Facebook 2FA任务执行器 - 完整自动化流程管理"""
    
    def __init__(self, emulator_id: int):
        """
        初始化Facebook 2FA任务执行器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.config_manager = get_config_manager()
        self.emulator_manager = get_emulator_manager()
        
        # 获取雷电API实例
        try:
            native_api = self.emulator_manager.get_native_api()
            if hasattr(native_api, 'ld'):
                self.ld = native_api.ld
            else:
                # 如果没有ld属性，直接创建Dnconsole实例
                from core.leidianapi.LeiDian_Reorganized import Dnconsole
                default_base_path = "G:/leidian/LDPlayer9"
                default_share_path = "C:/Users/<USER>/Documents/leidian9"
                self.ld = Dnconsole(default_base_path, default_share_path, emulator_id=emulator_id)
        except Exception as e:
            log_warning(f"[模拟器{emulator_id}] 无法获取原生API，使用默认配置: {e}", component="FacebookTwoFactorTask")
            from core.leidianapi.LeiDian_Reorganized import Dnconsole
            default_base_path = "G:/leidian/LDPlayer9"
            default_share_path = "C:/Users/<USER>/Documents/leidian9"
            self.ld = Dnconsole(default_base_path, default_share_path, emulator_id=emulator_id)
        
        # 任务配置
        self.task_config = self._load_task_config()
        
        # 任务状态
        self.task_start_time = time.time()
        self.task_timeout = self.task_config.get('task_timeout_seconds', 300)  # 默认5分钟超时
        
        # 提取器实例
        self.extractor = None
        
        log_info(f"[模拟器{emulator_id}] Facebook 2FA任务执行器初始化完成", component="FacebookTwoFactorTask")

    def _load_task_config(self) -> Dict[str, Any]:
        """加载任务配置"""
        try:
            # 从配置管理器获取基础配置
            basic_config = self.config_manager.get_config('basic_config', {})
            
            # FB 2FA任务的默认配置
            default_config = {
                'task_timeout_seconds': 300,
                'extraction_methods': ['facebook_app', 'authenticator_apps', 'system_database'],
                'output_format': 'json',
                'save_to_file': True,
                'output_directory': './fb_2fa_results/',
                'cleanup_temp_files': True,
                'retry_count': 3,
                'retry_delay': 5
            }
            
            # 合并配置
            task_config = {**default_config, **basic_config}
            
            log_info(f"[模拟器{self.emulator_id}] FB 2FA任务配置加载完成", component="FacebookTwoFactorTask")
            return task_config
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 加载任务配置失败: {e}", component="FacebookTwoFactorTask")
            return {}

    # ------------------------------------------------------------------------
    # 🎯 2. 主要执行方法
    # ------------------------------------------------------------------------

    async def execute(self) -> Dict[str, Any]:
        """
        执行Facebook 2FA提取任务的主入口方法
        
        Returns:
            Dict[str, Any]: 任务执行结果
        """
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始执行Facebook 2FA提取任务", component="FacebookTwoFactorTask")
            
            # 阶段一：环境检测和准备
            if not await self._stage1_environment_check():
                return self._create_failure_result("环境检测失败")
            
            # 阶段二：权限检查和获取
            if not await self._stage2_permission_check():
                return self._create_failure_result("权限检查失败")
            
            # 阶段三：2FA密钥提取
            extraction_result = await self._stage3_extract_2fa_secrets()
            if not extraction_result.success:
                return self._create_failure_result(f"2FA密钥提取失败: {extraction_result.error_message}")
            
            # 阶段四：结果处理和保存
            if not await self._stage4_process_results(extraction_result):
                return self._create_failure_result("结果处理失败")
            
            # 创建成功结果
            result = self._create_success_result(extraction_result)
            log_info(f"[模拟器{self.emulator_id}] Facebook 2FA提取任务执行成功", component="FacebookTwoFactorTask")
            return result
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] Facebook 2FA提取任务执行失败: {e}", component="FacebookTwoFactorTask")
            return self._create_failure_result(f"任务执行异常: {str(e)}")
        finally:
            # 清理资源
            await self._cleanup()

    async def _stage1_environment_check(self) -> bool:
        """阶段一：环境检测和准备"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 阶段一：环境检测和准备", component="FacebookTwoFactorTask")
            
            # 检查模拟器状态
            if not await self._check_emulator_status():
                return False
            
            # 检查ADB连接
            if not await self._check_adb_connection():
                return False
            
            # 创建输出目录
            if not await self._create_output_directory():
                return False
            
            # 初始化提取器
            self.extractor = FacebookTwoFactorExtractor(self.emulator_id)
            
            log_info(f"[模拟器{self.emulator_id}] 阶段一完成：环境检测通过", component="FacebookTwoFactorTask")
            return True
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 阶段一失败: {e}", component="FacebookTwoFactorTask")
            return False

    async def _stage2_permission_check(self) -> bool:
        """阶段二：权限检查和获取"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 阶段二：权限检查和获取", component="FacebookTwoFactorTask")
            
            # 检查root权限
            root_available = await self._check_root_permission()
            if root_available:
                log_info(f"[模拟器{self.emulator_id}] Root权限可用", component="FacebookTwoFactorTask")
            else:
                log_warning(f"[模拟器{self.emulator_id}] Root权限不可用，将使用有限的提取方法", component="FacebookTwoFactorTask")
            
            # 检查应用权限
            app_permissions = await self._check_app_permissions()
            log_info(f"[模拟器{self.emulator_id}] 应用权限检查完成: {len(app_permissions)}个应用可访问", component="FacebookTwoFactorTask")
            
            log_info(f"[模拟器{self.emulator_id}] 阶段二完成：权限检查通过", component="FacebookTwoFactorTask")
            return True
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 阶段二失败: {e}", component="FacebookTwoFactorTask")
            return False

    async def _stage3_extract_2fa_secrets(self) -> ExtractionResult:
        """阶段三：2FA密钥提取"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 阶段三：2FA密钥提取", component="FacebookTwoFactorTask")
            
            # 使用提取器执行提取
            extraction_result = self.extractor.extract_2fa_secrets()
            
            log_info(f"[模拟器{self.emulator_id}] 阶段三完成：提取到{len(extraction_result.secrets)}个2FA密钥", component="FacebookTwoFactorTask")
            return extraction_result
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 阶段三失败: {e}", component="FacebookTwoFactorTask")
            import datetime
            return ExtractionResult(
                success=False,
                secrets=[],
                error_message=str(e),
                extraction_method="error",
                timestamp=datetime.datetime.now().isoformat()
            )

    async def _stage4_process_results(self, extraction_result: ExtractionResult) -> bool:
        """阶段四：结果处理和保存"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 阶段四：结果处理和保存", component="FacebookTwoFactorTask")
            
            # 保存结果到文件
            if self.task_config.get('save_to_file', True):
                if not await self._save_results_to_file(extraction_result):
                    return False
            
            # 打印结果摘要
            await self._print_results_summary(extraction_result)
            
            log_info(f"[模拟器{self.emulator_id}] 阶段四完成：结果处理完成", component="FacebookTwoFactorTask")
            return True
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 阶段四失败: {e}", component="FacebookTwoFactorTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 3. 辅助方法
    # ------------------------------------------------------------------------

    async def _check_emulator_status(self) -> bool:
        """检查模拟器状态"""
        try:
            # 使用统一模拟器管理器检查状态
            status = await self.emulator_manager.get_emulator_status(self.emulator_id)
            if status and status.get('running', False):
                log_info(f"[模拟器{self.emulator_id}] 模拟器状态正常", component="FacebookTwoFactorTask")
                return True
            else:
                log_error(f"[模拟器{self.emulator_id}] 模拟器未运行", component="FacebookTwoFactorTask")
                return False
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 检查模拟器状态失败: {e}", component="FacebookTwoFactorTask")
            return False

    async def _check_adb_connection(self) -> bool:
        """检查ADB连接"""
        try:
            # 执行简单的ADB命令测试连接
            result = self.ld.adb(self.emulator_id, "echo 'test'")
            if "test" in result:
                log_info(f"[模拟器{self.emulator_id}] ADB连接正常", component="FacebookTwoFactorTask")
                return True
            else:
                log_error(f"[模拟器{self.emulator_id}] ADB连接失败", component="FacebookTwoFactorTask")
                return False
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 检查ADB连接失败: {e}", component="FacebookTwoFactorTask")
            return False

    async def _create_output_directory(self) -> bool:
        """创建输出目录"""
        try:
            output_dir = Path(self.task_config.get('output_directory', './fb_2fa_results/'))
            output_dir.mkdir(parents=True, exist_ok=True)
            log_info(f"[模拟器{self.emulator_id}] 输出目录创建成功: {output_dir}", component="FacebookTwoFactorTask")
            return True
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 创建输出目录失败: {e}", component="FacebookTwoFactorTask")
            return False

    async def _check_root_permission(self) -> bool:
        """检查root权限"""
        try:
            result = self.ld.adb(self.emulator_id, "su -c 'echo root_test'")
            return "root_test" in result
        except Exception:
            return False

    async def _check_app_permissions(self) -> List[str]:
        """检查应用权限"""
        try:
            accessible_apps = []
            
            # 检查Facebook相关应用
            facebook_packages = [
                "com.facebook.katana",
                "com.facebook.orca",
                "com.facebook.mlite",
            ]
            
            for package in facebook_packages:
                result = self.ld.adb(self.emulator_id, f"pm list packages | grep {package}")
                if package in result:
                    accessible_apps.append(package)
            
            return accessible_apps
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 检查应用权限失败: {e}", component="FacebookTwoFactorTask")
            return []

    async def _save_results_to_file(self, extraction_result: ExtractionResult) -> bool:
        """保存结果到文件"""
        try:
            output_dir = Path(self.task_config.get('output_directory', './fb_2fa_results/'))

            # 生成文件名
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"fb_2fa_emulator{self.emulator_id}_{timestamp}.json"
            filepath = output_dir / filename

            # 准备保存的数据
            save_data = {
                'emulator_id': self.emulator_id,
                'extraction_result': asdict(extraction_result),
                'task_config': self.task_config,
                'timestamp': datetime.datetime.now().isoformat()
            }

            # 保存到JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            log_info(f"[模拟器{self.emulator_id}] 结果已保存到文件: {filepath}", component="FacebookTwoFactorTask")

            # 同时保存一个简化的密钥列表文件
            if extraction_result.secrets:
                keys_filename = f"fb_2fa_keys_emulator{self.emulator_id}_{timestamp}.txt"
                keys_filepath = output_dir / keys_filename

                with open(keys_filepath, 'w', encoding='utf-8') as f:
                    f.write(f"Facebook 2FA密钥提取结果 - 模拟器{self.emulator_id}\n")
                    f.write(f"提取时间: {extraction_result.timestamp}\n")
                    f.write(f"提取方法: {extraction_result.extraction_method}\n")
                    f.write("=" * 50 + "\n\n")

                    for i, secret in enumerate(extraction_result.secrets, 1):
                        f.write(f"{i}. 服务: {secret.service_name}\n")
                        f.write(f"   账户: {secret.account}\n")
                        f.write(f"   密钥: {secret.secret_key}\n")
                        f.write(f"   发行者: {secret.issuer}\n")
                        f.write(f"   类型: {secret.type}\n")
                        f.write(f"   算法: {secret.algorithm}\n")
                        f.write(f"   位数: {secret.digits}\n")
                        f.write(f"   周期: {secret.period}秒\n")
                        f.write("-" * 30 + "\n")

                log_info(f"[模拟器{self.emulator_id}] 密钥列表已保存到文件: {keys_filepath}", component="FacebookTwoFactorTask")

            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 保存结果到文件失败: {e}", component="FacebookTwoFactorTask")
            return False

    async def _print_results_summary(self, extraction_result: ExtractionResult):
        """打印结果摘要"""
        try:
            log_info(f"[模拟器{self.emulator_id}] ========== Facebook 2FA提取结果摘要 ==========", component="FacebookTwoFactorTask")
            log_info(f"[模拟器{self.emulator_id}] 提取状态: {'成功' if extraction_result.success else '失败'}", component="FacebookTwoFactorTask")
            log_info(f"[模拟器{self.emulator_id}] 提取方法: {extraction_result.extraction_method}", component="FacebookTwoFactorTask")
            log_info(f"[模拟器{self.emulator_id}] 密钥数量: {len(extraction_result.secrets)}", component="FacebookTwoFactorTask")

            if extraction_result.secrets:
                log_info(f"[模拟器{self.emulator_id}] 密钥详情:", component="FacebookTwoFactorTask")
                for i, secret in enumerate(extraction_result.secrets, 1):
                    log_info(f"[模拟器{self.emulator_id}]   {i}. {secret.service_name} - {secret.account} - {secret.secret_key[:8]}...", component="FacebookTwoFactorTask")

            if extraction_result.error_message:
                log_info(f"[模拟器{self.emulator_id}] 错误信息: {extraction_result.error_message}", component="FacebookTwoFactorTask")

            log_info(f"[模拟器{self.emulator_id}] ===============================================", component="FacebookTwoFactorTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 打印结果摘要失败: {e}", component="FacebookTwoFactorTask")

    def _create_success_result(self, extraction_result: ExtractionResult) -> Dict[str, Any]:
        """创建成功结果"""
        return {
            'success': True,
            'emulator_id': self.emulator_id,
            'task_type': 'facebook_2fa_extraction',
            'extraction_result': asdict(extraction_result),
            'execution_time': time.time() - self.task_start_time,
            'timestamp': time.time()
        }

    def _create_failure_result(self, error_message: str) -> Dict[str, Any]:
        """创建失败结果"""
        return {
            'success': False,
            'emulator_id': self.emulator_id,
            'task_type': 'facebook_2fa_extraction',
            'error_message': error_message,
            'execution_time': time.time() - self.task_start_time,
            'timestamp': time.time()
        }

    async def _cleanup(self):
        """清理资源"""
        try:
            if self.extractor and self.task_config.get('cleanup_temp_files', True):
                self.extractor.cleanup()
                log_info(f"[模拟器{self.emulator_id}] 资源清理完成", component="FacebookTwoFactorTask")
        except Exception as e:
            log_warning(f"[模拟器{self.emulator_id}] 资源清理失败: {e}", component="FacebookTwoFactorTask")

    # ------------------------------------------------------------------------
    # 🎯 4. 超时检查
    # ------------------------------------------------------------------------

    def _check_timeout(self) -> bool:
        """检查是否超时"""
        elapsed = time.time() - self.task_start_time
        if elapsed > self.task_timeout:
            log_warning(f"[模拟器{self.emulator_id}] 任务执行超时: {elapsed:.1f}秒 > {self.task_timeout}秒", component="FacebookTwoFactorTask")
            return True
        return False
