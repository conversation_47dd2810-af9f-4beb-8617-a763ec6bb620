#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 安装认证器并提取2FA密钥工具
========================================
功能描述: 先安装认证器应用，然后提取2FA密钥

核心策略:
1. 自动安装Google Authenticator
2. 创建测试2FA账户
3. 使用多种方法提取密钥
4. 绕过雷电模拟器的权限限制

使用方法:
python fb/install_and_extract.py [emulator_id]

注意事项:
- 专门适配雷电模拟器
- 自动安装必要的应用
- 仅用于合法的账户恢复目的
========================================
"""

import asyncio
import sys
import re
import json
import base64
import tempfile
import struct
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class InstallAndExtractTool:
    """安装认证器并提取2FA密钥工具"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化工具
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.temp_dir = Path(tempfile.gettempdir()) / f"install_extract_{emulator_id}"
        self.temp_dir.mkdir(exist_ok=True)
        
        log_info(f"[安装提取] 安装认证器并提取工具初始化 - 模拟器{emulator_id}", component="InstallExtract")

    async def run_install_and_extract(self):
        """运行安装和提取流程"""
        try:
            log_info(f"[安装提取] 开始安装认证器并提取", component="InstallExtract")
            
            print("🔓 安装认证器并提取2FA密钥工具")
            print("=" * 50)
            print("⚠️  将自动安装Google Authenticator")
            print("⚠️  创建测试2FA账户")
            print("⚠️  然后提取密钥进行验证")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：检查并安装Google Authenticator
            await self._install_google_authenticator()
            
            # 第二步：创建测试2FA账户
            await self._create_test_2fa_accounts()
            
            # 第三步：尝试多种方法提取密钥
            secrets = await self._extract_with_multiple_methods()
            
            # 第四步：验证和显示结果
            verified_secrets = await self._verify_and_display_results(secrets)
            
            # 第五步：保存结果
            await self._save_results(verified_secrets)
            
            log_info(f"[安装提取] 安装认证器并提取完成", component="InstallExtract")
            
        except Exception as e:
            log_error(f"[安装提取] 安装提取失败: {e}", component="InstallExtract")
        finally:
            await self._cleanup()

    async def _install_google_authenticator(self):
        """安装Google Authenticator"""
        try:
            print("📱 检查并安装Google Authenticator...")
            print("-" * 40)
            
            # 检查是否已安装
            success, result = self.task.ld.execute_ld(self.emulator_id, "pm list packages | grep com.google.android.apps.authenticator2")
            if success and "com.google.android.apps.authenticator2" in result:
                print("✅ Google Authenticator已安装")
                return
            
            print("📥 Google Authenticator未安装，正在安装...")
            
            # 尝试从Play Store安装
            install_cmd = "am start -a android.intent.action.VIEW -d 'market://details?id=com.google.android.apps.authenticator2'"
            success, result = self.task.ld.execute_ld(self.emulator_id, install_cmd)
            if success:
                print("✅ 已打开Play Store安装页面")
                print("💡 请手动安装Google Authenticator，然后按回车继续...")
                input("按回车键继续...")
            else:
                print("❌ 无法打开Play Store")
                print("💡 请手动安装Google Authenticator APK文件")
                print("💡 可以从以下地址下载:")
                print("   https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2")
                input("安装完成后按回车键继续...")
            
            # 再次检查是否安装成功
            success, result = self.task.ld.execute_ld(self.emulator_id, "pm list packages | grep com.google.android.apps.authenticator2")
            if success and "com.google.android.apps.authenticator2" in result:
                print("✅ Google Authenticator安装成功")
            else:
                print("❌ Google Authenticator安装失败")
                print("💡 继续使用其他方法...")
            
            print()
            
        except Exception as e:
            log_error(f"[安装提取] 安装Google Authenticator失败: {e}", component="InstallExtract")

    async def _create_test_2fa_accounts(self):
        """创建测试2FA账户"""
        try:
            print("🔑 创建测试2FA账户...")
            print("-" * 40)
            
            # 生成测试密钥
            test_secrets = [
                {
                    'secret': 'JBSWY3DPEHPK3PXP',
                    'issuer': 'TestFacebook',
                    'account': '<EMAIL>'
                },
                {
                    'secret': 'KNRZ2OB5CK7L3THMQHACQRSTG6OB7VDF',
                    'issuer': 'Facebook',
                    'account': '<EMAIL>'
                }
            ]
            
            print("📋 生成的测试密钥:")
            for i, secret_info in enumerate(test_secrets, 1):
                print(f"{i}. 发行方: {secret_info['issuer']}")
                print(f"   账户: {secret_info['account']}")
                print(f"   密钥: {secret_info['secret']}")
                
                # 生成当前TOTP验证码
                current_code = self._generate_totp(secret_info['secret'])
                print(f"   当前验证码: {current_code}")
                print()
            
            print("💡 这些是测试密钥，用于验证提取工具的功能")
            print("💡 在实际使用中，工具会提取真实的2FA密钥")
            print()
            
        except Exception as e:
            log_error(f"[安装提取] 创建测试账户失败: {e}", component="InstallExtract")

    async def _extract_with_multiple_methods(self) -> List[Dict[str, Any]]:
        """使用多种方法提取密钥"""
        try:
            print("🔍 使用多种方法提取密钥...")
            print("-" * 40)
            
            all_secrets = []
            
            # 方法1：扫描应用数据目录
            print("📁 方法1: 扫描应用数据目录")
            secrets1 = await self._scan_app_data_directories()
            all_secrets.extend(secrets1)
            print(f"   找到 {len(secrets1)} 个密钥")
            
            # 方法2：分析系统服务
            print("⚙️  方法2: 分析系统服务")
            secrets2 = await self._analyze_system_services()
            all_secrets.extend(secrets2)
            print(f"   找到 {len(secrets2)} 个密钥")
            
            # 方法3：搜索文件系统
            print("🗂️  方法3: 搜索文件系统")
            secrets3 = await self._search_filesystem()
            all_secrets.extend(secrets3)
            print(f"   找到 {len(secrets3)} 个密钥")
            
            # 方法4：分析进程内存
            print("🧠 方法4: 分析进程内存")
            secrets4 = await self._analyze_process_memory()
            all_secrets.extend(secrets4)
            print(f"   找到 {len(secrets4)} 个密钥")
            
            # 方法5：网络和日志分析
            print("🌐 方法5: 网络和日志分析")
            secrets5 = await self._analyze_network_logs()
            all_secrets.extend(secrets5)
            print(f"   找到 {len(secrets5)} 个密钥")
            
            print(f"✅ 多方法提取完成: 总共找到 {len(all_secrets)} 个可能的密钥")
            print()
            return all_secrets
            
        except Exception as e:
            log_error(f"[安装提取] 多方法提取失败: {e}", component="InstallExtract")
            return []

    async def _scan_app_data_directories(self) -> List[Dict[str, Any]]:
        """扫描应用数据目录"""
        try:
            secrets = []
            
            # 尝试访问Google Authenticator数据目录
            auth_package = "com.google.android.apps.authenticator2"
            
            # 使用不同的权限级别尝试访问
            access_methods = [
                f"ls -la /data/data/{auth_package}/",
                f"run-as {auth_package} ls -la",
                f"su -c 'ls -la /data/data/{auth_package}/'",
                f"ls -la /storage/emulated/0/Android/data/{auth_package}/",
            ]
            
            for method in access_methods:
                success, result = self.task.ld.execute_ld(self.emulator_id, method)
                if success and "databases" in result:
                    print(f"   ✅ 访问成功: {method}")
                    
                    # 尝试访问数据库文件
                    db_path = f"/data/data/{auth_package}/databases/accounts.db"
                    success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'ls -la {db_path}'")
                    if success2 and "accounts.db" in result2:
                        print(f"   📊 找到数据库文件: {db_path}")
                        
                        # 尝试读取数据库内容
                        db_secrets = await self._extract_from_database(db_path)
                        secrets.extend(db_secrets)
                    break
                else:
                    print(f"   ❌ 访问失败: {method}")
            
            return secrets
            
        except Exception as e:
            log_error(f"[安装提取] 扫描应用数据目录失败: {e}", component="InstallExtract")
            return []

    async def _extract_from_database(self, db_path: str) -> List[Dict[str, Any]]:
        """从数据库提取密钥"""
        try:
            secrets = []
            
            # 尝试复制数据库到可访问位置
            temp_db = "/sdcard/temp_auth.db"
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'cp {db_path} {temp_db}'")
            if success:
                print(f"   📋 数据库复制成功")
                
                # 尝试使用sqlite3读取
                success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"sqlite3 {temp_db} 'SELECT * FROM accounts'")
                if success2 and result2.strip():
                    print(f"   📊 数据库查询成功")
                    lines = result2.strip().split('\n')
                    for line in lines:
                        if line.strip():
                            # 搜索Base32模式
                            base32_matches = re.findall(r'[A-Z2-7]{16,}', line)
                            for match in base32_matches:
                                if self._is_valid_base32_key(match):
                                    secrets.append({
                                        'secret_key': match,
                                        'source': 'google_authenticator_db',
                                        'method': 'database_extraction',
                                        'confidence': 'high'
                                    })
                                    print(f"   🔑 提取密钥: {match[:8]}...")
                else:
                    # 尝试使用strings命令
                    success3, result3 = self.task.ld.execute_ld(self.emulator_id, f"strings {temp_db} | grep -E '[A-Z2-7]{{16,}}'")
                    if success3 and result3.strip():
                        potential_keys = result3.strip().split('\n')
                        for key in potential_keys:
                            if self._is_valid_base32_key(key.strip()):
                                secrets.append({
                                    'secret_key': key.strip(),
                                    'source': 'google_authenticator_strings',
                                    'method': 'strings_extraction',
                                    'confidence': 'medium'
                                })
                                print(f"   🔑 提取密钥: {key.strip()[:8]}...")
                
                # 清理临时文件
                self.task.ld.execute_ld(self.emulator_id, f"rm -f {temp_db}")
            
            return secrets
            
        except Exception as e:
            log_error(f"[安装提取] 数据库提取失败: {e}", component="InstallExtract")
            return []

    async def _analyze_system_services(self) -> List[Dict[str, Any]]:
        """分析系统服务"""
        try:
            secrets = []
            
            # 分析账户管理器
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys account")
            if success and result.strip():
                # 搜索Base32模式
                base32_matches = re.findall(r'[A-Z2-7]{16,}', result)
                for match in base32_matches:
                    if self._is_valid_base32_key(match):
                        secrets.append({
                            'secret_key': match,
                            'source': 'account_manager',
                            'method': 'system_service_analysis',
                            'confidence': 'medium'
                        })
            
            return secrets
            
        except Exception as e:
            log_error(f"[安装提取] 系统服务分析失败: {e}", component="InstallExtract")
            return []

    async def _search_filesystem(self) -> List[Dict[str, Any]]:
        """搜索文件系统"""
        try:
            secrets = []
            
            # 搜索可访问的目录
            search_paths = [
                "/sdcard/",
                "/storage/emulated/0/",
                "/data/local/tmp/",
            ]
            
            for path in search_paths:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"find {path} -type f -exec grep -l '[A-Z2-7]{{16,}}' {{}} \\; 2>/dev/null")
                if success and result.strip():
                    files = result.strip().split('\n')
                    for file_path in files[:5]:  # 限制处理前5个文件
                        if file_path.strip():
                            file_secrets = await self._extract_from_file(file_path.strip())
                            secrets.extend(file_secrets)
            
            return secrets
            
        except Exception as e:
            log_error(f"[安装提取] 文件系统搜索失败: {e}", component="InstallExtract")
            return []

    async def _extract_from_file(self, file_path: str) -> List[Dict[str, Any]]:
        """从文件提取密钥"""
        try:
            secrets = []
            
            success, result = self.task.ld.execute_ld(self.emulator_id, f"cat '{file_path}' | grep -oE '[A-Z2-7]{{16,}}' | head -5")
            if success and result.strip():
                potential_keys = result.strip().split('\n')
                for key in potential_keys:
                    if self._is_valid_base32_key(key.strip()):
                        secrets.append({
                            'secret_key': key.strip(),
                            'source': f'file:{file_path}',
                            'method': 'file_extraction',
                            'confidence': 'low'
                        })
            
            return secrets
            
        except Exception as e:
            log_error(f"[安装提取] 文件提取失败: {e}", component="InstallExtract")
            return []

    async def _analyze_process_memory(self) -> List[Dict[str, Any]]:
        """分析进程内存"""
        try:
            secrets = []
            
            # 获取认证器进程
            success, result = self.task.ld.execute_ld(self.emulator_id, "ps | grep authenticator")
            if success and result.strip():
                processes = result.strip().split('\n')
                for process in processes:
                    parts = process.split()
                    if len(parts) > 1:
                        pid = parts[1]
                        # 分析进程环境变量
                        success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"cat /proc/{pid}/environ 2>/dev/null | tr '\\0' '\\n' | grep -E '[A-Z2-7]{{16,}}'")
                        if success2 and result2.strip():
                            env_keys = result2.strip().split('\n')
                            for key in env_keys:
                                if self._is_valid_base32_key(key.strip()):
                                    secrets.append({
                                        'secret_key': key.strip(),
                                        'source': f'process_memory:{pid}',
                                        'method': 'memory_analysis',
                                        'confidence': 'high'
                                    })
            
            return secrets
            
        except Exception as e:
            log_error(f"[安装提取] 进程内存分析失败: {e}", component="InstallExtract")
            return []

    async def _analyze_network_logs(self) -> List[Dict[str, Any]]:
        """分析网络和日志"""
        try:
            secrets = []
            
            # 分析系统日志
            success, result = self.task.ld.execute_ld(self.emulator_id, "logcat -d | grep -E '[A-Z2-7]{16,}' | tail -10")
            if success and result.strip():
                log_lines = result.strip().split('\n')
                for line in log_lines:
                    base32_matches = re.findall(r'[A-Z2-7]{16,}', line)
                    for match in base32_matches:
                        if self._is_valid_base32_key(match):
                            secrets.append({
                                'secret_key': match,
                                'source': 'system_logs',
                                'method': 'log_analysis',
                                'confidence': 'low'
                            })
            
            return secrets
            
        except Exception as e:
            log_error(f"[安装提取] 网络日志分析失败: {e}", component="InstallExtract")
            return []
