#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 雷电模拟器兼容的Facebook 2FA提取工具
========================================
功能描述: 专门适配雷电模拟器权限限制的2FA密钥提取工具

核心策略:
1. 绕过雷电模拟器的权限限制
2. 使用可访问的路径进行深度扫描
3. 利用应用沙盒外的数据存储
4. 通过系统服务获取数据
5. 使用替代方法访问受限数据

适用场景:
- 雷电模拟器环境
- 受限的root权限
- 需要绕过权限限制

使用方法:
python fb/ld_compatible_extract.py [emulator_id]

注意事项:
- 专门为雷电模拟器优化
- 绕过权限限制
- 仅用于合法的账户恢复目的
========================================
"""

import asyncio
import sys
import re
import json
import base64
import tempfile
import struct
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class LeiDianCompatibleExtractor:
    """雷电模拟器兼容的Facebook 2FA提取器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化雷电兼容提取器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.temp_dir = Path(tempfile.gettempdir()) / f"ld_extract_{emulator_id}"
        self.temp_dir.mkdir(exist_ok=True)
        
        # 雷电模拟器可访问的路径
        self.accessible_paths = [
            # 外部存储路径
            "/sdcard/",
            "/storage/emulated/0/",
            "/mnt/sdcard/",
            
            # Android数据目录
            "/sdcard/Android/",
            "/storage/emulated/0/Android/",
            
            # 下载和文档目录
            "/sdcard/Download/",
            "/sdcard/Documents/",
            "/sdcard/Pictures/",
            
            # 系统可访问目录
            "/system/bin/",
            "/system/lib/",
            "/vendor/",
            
            # 进程信息
            "/proc/",
            "/sys/",
            
            # 缓存目录
            "/cache/",
            "/tmp/",
        ]
        
        # 雷电模拟器特有的路径
        self.leidian_specific_paths = [
            # 雷电共享目录
            "/mnt/shared/",
            "/mnt/shell/emulated/0/",
            
            # 雷电特殊目录
            "/data/local/tmp/",
            "/data/misc/",
            
            # 可能的应用数据备份位置
            "/sdcard/Android/data/",
            "/storage/emulated/0/Android/data/",
        ]
        
        log_info(f"[雷电兼容] 雷电模拟器兼容提取器初始化 - 模拟器{emulator_id}", component="LDExtractor")

    async def run_leidian_extraction(self):
        """运行雷电兼容提取流程"""
        try:
            log_info(f"[雷电兼容] 开始雷电模拟器兼容提取", component="LDExtractor")
            
            print("🔓 雷电模拟器兼容的Facebook 2FA提取工具")
            print("=" * 50)
            print("⚠️  专门适配雷电模拟器权限限制")
            print("⚠️  使用替代方法绕过权限限制")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：雷电环境检测
            await self._detect_leidian_environment()
            
            # 第二步：权限绕过设置
            await self._bypass_permission_restrictions()
            
            # 第三步：可访问路径扫描
            secrets1 = await self._scan_accessible_paths()
            
            # 第四步：应用数据导出
            secrets2 = await self._export_app_data()
            
            # 第五步：系统服务利用
            secrets3 = await self._utilize_system_services()
            
            # 第六步：内存和进程分析
            secrets4 = await self._analyze_memory_processes()
            
            # 第七步：网络和日志分析
            secrets5 = await self._analyze_network_logs()
            
            # 第八步：雷电特有功能利用
            secrets6 = await self._utilize_leidian_features()
            
            # 合并所有结果
            all_secrets = []
            all_secrets.extend(secrets1)
            all_secrets.extend(secrets2)
            all_secrets.extend(secrets3)
            all_secrets.extend(secrets4)
            all_secrets.extend(secrets5)
            all_secrets.extend(secrets6)
            
            # 验证和处理
            verified_secrets = await self._verify_leidian_secrets(all_secrets)
            
            # 保存和显示结果
            await self._save_leidian_results(verified_secrets)
            await self._display_leidian_results(verified_secrets)
            
            log_info(f"[雷电兼容] 雷电模拟器兼容提取完成", component="LDExtractor")
            
        except Exception as e:
            log_error(f"[雷电兼容] 雷电兼容提取失败: {e}", component="LDExtractor")
        finally:
            await self._cleanup()

    async def _detect_leidian_environment(self):
        """雷电环境检测"""
        try:
            print("🔍 雷电环境检测...")
            print("-" * 40)
            
            # 检测雷电模拟器特征
            success, result = self.task.ld.execute_ld(self.emulator_id, "getprop ro.product.model")
            if success and result.strip():
                model = result.strip()
                print(f"📱 设备型号: {model}")
                
                if "LDPlayer" in model or "雷电" in model:
                    print("✅ 确认为雷电模拟器环境")
                else:
                    print("⚠️  可能不是雷电模拟器环境")
            
            # 检测Android版本
            success, result = self.task.ld.execute_ld(self.emulator_id, "getprop ro.build.version.release")
            if success and result.strip():
                android_version = result.strip()
                print(f"🤖 Android版本: {android_version}")
            
            # 检测可用的权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "whoami")
            if success and result.strip():
                user = result.strip()
                print(f"👤 当前用户: {user}")
            
            # 检测root权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'whoami'")
            if success and "root" in result:
                print("✅ Root权限可用")
                
                # 检测具体的权限限制
                success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'ls -la /data/ | wc -l'")
                if success and result.strip().isdigit():
                    data_access = int(result.strip())
                    if data_access > 5:
                        print("✅ /data目录部分可访问")
                    else:
                        print("❌ /data目录访问受限")
                else:
                    print("❌ /data目录完全不可访问")
            else:
                print("❌ Root权限不可用")
            
            print("✅ 环境检测完成")
            print()
            
        except Exception as e:
            log_error(f"[雷电兼容] 环境检测失败: {e}", component="LDExtractor")

    async def _bypass_permission_restrictions(self):
        """权限绕过设置"""
        try:
            print("🔓 权限绕过设置...")
            print("-" * 40)
            
            # 尝试创建临时工作目录
            work_dirs = [
                "/sdcard/fb_extract_temp/",
                "/storage/emulated/0/fb_extract_temp/",
                "/data/local/tmp/fb_extract/",
            ]
            
            for work_dir in work_dirs:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"mkdir -p {work_dir}")
                if success:
                    print(f"✅ 创建工作目录: {work_dir}")
                    self.work_dir = work_dir
                    break
                else:
                    print(f"❌ 无法创建: {work_dir}")
            
            # 尝试设置权限
            if hasattr(self, 'work_dir'):
                success, result = self.task.ld.execute_ld(self.emulator_id, f"chmod 777 {self.work_dir}")
                if success:
                    print(f"✅ 设置工作目录权限")
            
            # 尝试启动Facebook应用
            print("🚀 尝试启动Facebook应用...")
            success, result = self.task.ld.execute_ld(self.emulator_id, "am start -n com.facebook.katana/.LoginActivity")
            if success:
                print("✅ Facebook应用启动命令已发送")
                await asyncio.sleep(3)  # 等待应用启动
            else:
                print("❌ 无法启动Facebook应用")
            
            print("✅ 权限绕过设置完成")
            print()
            
        except Exception as e:
            log_error(f"[雷电兼容] 权限绕过设置失败: {e}", component="LDExtractor")

    async def _scan_accessible_paths(self) -> List[Dict[str, Any]]:
        """可访问路径扫描"""
        try:
            print("🗂️  可访问路径扫描...")
            print("-" * 40)
            
            secrets = []
            
            # 扫描所有可访问的路径
            all_paths = self.accessible_paths + self.leidian_specific_paths
            
            for path in all_paths:
                print(f"🔍 扫描路径: {path}")
                
                # 检查路径是否存在
                success, result = self.task.ld.execute_ld(self.emulator_id, f"ls -la '{path}' 2>/dev/null")
                if not success or "No such file" in result:
                    print(f"   ❌ 路径不存在")
                    continue
                
                # 搜索Facebook相关文件
                facebook_search = f"find '{path}' -type f -name '*facebook*' -o -name '*katana*' 2>/dev/null"
                
                success, result = self.task.ld.execute_ld(self.emulator_id, facebook_search)
                if success and result.strip():
                    facebook_files = result.strip().split('\n')
                    print(f"   📱 找到 {len(facebook_files)} 个Facebook相关文件")
                    
                    for fb_file in facebook_files[:10]:  # 限制处理前10个
                        if fb_file.strip():
                            file_secrets = await self._analyze_accessible_file(fb_file.strip())
                            if file_secrets:
                                secrets.extend(file_secrets)
                                print(f"   🔑 从 {fb_file} 提取到 {len(file_secrets)} 个密钥")
                
                # 搜索可能包含Base32密钥的文件
                base32_search = f"find '{path}' -type f -exec grep -l '[A-Z2-7]{{16,}}' {{}} \\; 2>/dev/null"
                
                success, result = self.task.ld.execute_ld(self.emulator_id, base32_search)
                if success and result.strip():
                    potential_files = result.strip().split('\n')
                    print(f"   🎯 找到 {len(potential_files)} 个可能包含密钥的文件")
                    
                    for pot_file in potential_files[:5]:  # 限制处理前5个
                        if pot_file.strip():
                            file_secrets = await self._extract_base32_from_accessible_file(pot_file.strip())
                            if file_secrets:
                                secrets.extend(file_secrets)
                                print(f"   🔑 从 {pot_file} 提取Base32密钥: {len(file_secrets)} 个")
            
            print(f"✅ 可访问路径扫描完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets
            
        except Exception as e:
            log_error(f"[雷电兼容] 可访问路径扫描失败: {e}", component="LDExtractor")
            return []

    async def _export_app_data(self) -> List[Dict[str, Any]]:
        """应用数据导出"""
        try:
            print("📦 应用数据导出...")
            print("-" * 40)

            secrets = []

            # 尝试使用adb backup导出应用数据
            if hasattr(self, 'work_dir'):
                backup_file = f"{self.work_dir}/facebook_backup.ab"

                print("🔄 尝试导出Facebook应用数据...")
                success, result = self.task.ld.execute_ld(self.emulator_id, f"pm dump com.facebook.katana > {backup_file}")
                if success:
                    print("✅ 应用信息导出成功")

                    # 分析导出的数据
                    success2, content = self.task.ld.execute_ld(self.emulator_id, f"cat {backup_file}")
                    if success2 and content.strip():
                        # 搜索内容中的Base32模式
                        base32_matches = re.findall(r'[A-Z2-7]{16,}', content)
                        for match in base32_matches:
                            if self._is_valid_base32_key(match):
                                secrets.append({
                                    'secret_key': match,
                                    'source': 'app_data_export',
                                    'method': 'app_data_export',
                                    'confidence': 'medium'
                                })
                                print(f"   🔑 导出数据中发现密钥: {match[:8]}...")
                else:
                    print("❌ 应用数据导出失败")

            # 尝试通过logcat获取应用日志
            print("📋 分析应用日志...")
            success, result = self.task.ld.execute_ld(self.emulator_id, "logcat -d -s FacebookApp:* | grep -E '(2fa|totp|auth|secret)' | tail -20")
            if success and result.strip():
                log_lines = result.strip().split('\n')
                print(f"   📝 找到 {len(log_lines)} 条相关日志")

                for line in log_lines:
                    # 搜索日志中的Base32模式
                    base32_matches = re.findall(r'[A-Z2-7]{16,}', line)
                    for match in base32_matches:
                        if self._is_valid_base32_key(match):
                            secrets.append({
                                'secret_key': match,
                                'source': 'app_logs',
                                'method': 'app_data_export',
                                'log_context': line[:100],
                                'confidence': 'high'
                            })
                            print(f"   🔑 应用日志中发现密钥: {match[:8]}...")
            else:
                print("   ❌ 未找到相关应用日志")

            print(f"✅ 应用数据导出完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets

        except Exception as e:
            log_error(f"[雷电兼容] 应用数据导出失败: {e}", component="LDExtractor")
            return []

    async def _utilize_system_services(self) -> List[Dict[str, Any]]:
        """系统服务利用"""
        try:
            print("⚙️  系统服务利用...")
            print("-" * 40)

            secrets = []

            # 检查账户管理器
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys account | grep -A 10 -B 10 facebook")
            if success and result.strip():
                print("👤 分析账户管理器数据...")
                account_data = result.strip()

                # 搜索账户数据中的Base32模式
                base32_matches = re.findall(r'[A-Z2-7]{16,}', account_data)
                for match in base32_matches:
                    if self._is_valid_base32_key(match):
                        secrets.append({
                            'secret_key': match,
                            'source': 'account_manager',
                            'method': 'system_services',
                            'confidence': 'high'
                        })
                        print(f"   🔑 账户管理器中发现密钥: {match[:8]}...")

            # 检查包管理器信息
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys package com.facebook.katana | grep -E '(auth|secret|token)'")
            if success and result.strip():
                print("📦 分析包管理器数据...")
                package_data = result.strip()

                # 搜索包数据中的Base32模式
                base32_matches = re.findall(r'[A-Z2-7]{16,}', package_data)
                for match in base32_matches:
                    if self._is_valid_base32_key(match):
                        secrets.append({
                            'secret_key': match,
                            'source': 'package_manager',
                            'method': 'system_services',
                            'confidence': 'medium'
                        })
                        print(f"   🔑 包管理器中发现密钥: {match[:8]}...")

            print(f"✅ 系统服务利用完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets

        except Exception as e:
            log_error(f"[雷电兼容] 系统服务利用失败: {e}", component="LDExtractor")
            return []

    async def _analyze_memory_processes(self) -> List[Dict[str, Any]]:
        """内存和进程分析"""
        try:
            print("🧠 内存和进程分析...")
            print("-" * 40)

            secrets = []

            # 获取Facebook进程信息
            success, result = self.task.ld.execute_ld(self.emulator_id, "ps | grep facebook")
            if success and result.strip():
                processes = result.strip().split('\n')
                print(f"📱 找到 {len(processes)} 个Facebook进程")

                for process in processes:
                    if process.strip():
                        parts = process.split()
                        if len(parts) > 1:
                            pid = parts[1]
                            print(f"   🔍 分析进程 PID: {pid}")

                            # 分析进程的环境变量
                            success3, environ = self.task.ld.execute_ld(self.emulator_id, f"cat /proc/{pid}/environ 2>/dev/null | tr '\\0' '\\n'")
                            if success3 and environ.strip():
                                env_lines = environ.strip().split('\n')
                                for env_line in env_lines:
                                    if env_line.strip():
                                        # 搜索环境变量中的Base32模式
                                        base32_matches = re.findall(r'[A-Z2-7]{16,}', env_line)
                                        for match in base32_matches:
                                            if self._is_valid_base32_key(match):
                                                secrets.append({
                                                    'secret_key': match,
                                                    'source': f'process_environ:{pid}',
                                                    'method': 'memory_process_analysis',
                                                    'confidence': 'high'
                                                })
                                                print(f"   🔑 进程环境变量中发现密钥: {match[:8]}...")
            else:
                print("❌ 未找到Facebook进程")

            print(f"✅ 内存和进程分析完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets

        except Exception as e:
            log_error(f"[雷电兼容] 内存和进程分析失败: {e}", component="LDExtractor")
            return []

    async def _analyze_network_logs(self) -> List[Dict[str, Any]]:
        """网络和日志分析"""
        try:
            print("🌐 网络和日志分析...")
            print("-" * 40)

            secrets = []

            # 分析系统日志
            success, result = self.task.ld.execute_ld(self.emulator_id, "logcat -d | grep -i facebook | grep -E '(2fa|totp|auth|secret)' | tail -10")
            if success and result.strip():
                log_lines = result.strip().split('\n')
                print(f"📋 找到 {len(log_lines)} 条相关系统日志")

                for line in log_lines:
                    # 搜索日志中的Base32模式
                    base32_matches = re.findall(r'[A-Z2-7]{16,}', line)
                    for match in base32_matches:
                        if self._is_valid_base32_key(match):
                            secrets.append({
                                'secret_key': match,
                                'source': 'system_logs',
                                'method': 'network_log_analysis',
                                'log_context': line[:100],
                                'confidence': 'medium'
                            })
                            print(f"   🔑 系统日志中发现密钥: {match[:8]}...")
            else:
                print("❌ 未找到相关系统日志")

            print(f"✅ 网络和日志分析完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets

        except Exception as e:
            log_error(f"[雷电兼容] 网络和日志分析失败: {e}", component="LDExtractor")
            return []

    async def _utilize_leidian_features(self) -> List[Dict[str, Any]]:
        """雷电特有功能利用"""
        try:
            print("⚡ 雷电特有功能利用...")
            print("-" * 40)

            secrets = []

            # 检查雷电共享目录
            shared_paths = [
                "/mnt/shared/",
                "/mnt/shell/emulated/0/",
                self.task.ld.share_path if hasattr(self.task.ld, 'share_path') else None
            ]

            for shared_path in shared_paths:
                if shared_path:
                    print(f"🔍 检查雷电共享目录: {shared_path}")

                    # 在共享目录中搜索Facebook相关文件
                    success, result = self.task.ld.execute_ld(self.emulator_id, f"find '{shared_path}' -name '*facebook*' -o -name '*katana*' 2>/dev/null")
                    if success and result.strip():
                        shared_files = result.strip().split('\n')
                        print(f"   📁 找到 {len(shared_files)} 个相关文件")

                        for shared_file in shared_files:
                            if shared_file.strip():
                                file_secrets = await self._analyze_accessible_file(shared_file.strip())
                                if file_secrets:
                                    secrets.extend(file_secrets)
                                    print(f"   🔑 从共享文件提取到密钥: {len(file_secrets)} 个")

            print(f"✅ 雷电特有功能利用完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets

        except Exception as e:
            log_error(f"[雷电兼容] 雷电特有功能利用失败: {e}", component="LDExtractor")
            return []

    def _is_valid_base32_key(self, key: str) -> bool:
        """验证是否为有效的Base32密钥"""
        try:
            if not key or len(key) < 16:
                return False

            # 清理密钥
            cleaned = re.sub(r'[^A-Z2-7]', '', key.upper())
            if len(cleaned) < 16:
                return False

            # 验证Base32字符集
            if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in cleaned):
                return False

            # 尝试解码
            try:
                base64.b32decode(cleaned)
                return True
            except Exception:
                return False

        except Exception:
            return False

    async def _analyze_accessible_file(self, file_path: str) -> List[Dict[str, Any]]:
        """分析可访问文件"""
        try:
            secrets = []

            # 使用strings命令提取可读字符串
            success, result = self.task.ld.execute_ld(self.emulator_id, f"strings '{file_path}' | grep -E '[A-Z2-7]{{16,}}' | head -10")
            if success and result.strip():
                potential_keys = result.strip().split('\n')

                for key in potential_keys:
                    if self._is_valid_base32_key(key.strip()):
                        secrets.append({
                            'secret_key': key.strip(),
                            'source': f'accessible_file:{file_path}',
                            'method': 'accessible_file_analysis',
                            'confidence': 'medium'
                        })

            return secrets

        except Exception as e:
            log_error(f"[雷电兼容] 分析可访问文件失败: {e}", component="LDExtractor")
            return []

    async def _extract_base32_from_accessible_file(self, file_path: str) -> List[Dict[str, Any]]:
        """从可访问文件中提取Base32密钥"""
        try:
            secrets = []

            # 读取文件内容并搜索Base32模式
            success, result = self.task.ld.execute_ld(self.emulator_id, f"cat '{file_path}' | grep -oE '[A-Z2-7]{{16,}}' | head -5")
            if success and result.strip():
                potential_keys = result.strip().split('\n')

                for key in potential_keys:
                    if self._is_valid_base32_key(key.strip()):
                        secrets.append({
                            'secret_key': key.strip(),
                            'source': f'base32_file:{file_path}',
                            'method': 'base32_file_extraction',
                            'confidence': 'high'
                        })

            return secrets

        except Exception as e:
            log_error(f"[雷电兼容] Base32文件提取失败: {e}", component="LDExtractor")
            return []

    async def _verify_leidian_secrets(self, all_secrets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证雷电提取的密钥"""
        try:
            print("🔍 验证雷电提取的密钥...")
            print("-" * 40)

            # 去重
            unique_secrets = []
            seen_keys = set()

            for secret in all_secrets:
                key = secret.get('secret_key', '')
                if key and key not in seen_keys:
                    seen_keys.add(key)

                    # 验证密钥并生成测试TOTP
                    if self._is_valid_base32_key(key):
                        try:
                            import hmac
                            import hashlib
                            import struct
                            import time

                            decoded_key = base64.b32decode(key)
                            timestamp = int(time.time()) // 30
                            msg = struct.pack('>Q', timestamp)
                            hmac_digest = hmac.new(decoded_key, msg, hashlib.sha1).digest()
                            offset = hmac_digest[-1] & 0x0f
                            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
                            code = (code & 0x7fffffff) % 1000000

                            secret['test_totp'] = f"{code:06d}"
                            secret['verified'] = True
                            secret['key_length'] = len(key)

                            print(f"✅ 验证密钥: {key[:8]}... (来源: {secret.get('source', 'unknown')})")
                            print(f"   🔢 测试验证码: {code:06d}")
                            print(f"   📊 置信度: {secret.get('confidence', 'unknown')}")

                        except Exception as e:
                            secret['verified'] = False
                            secret['error'] = str(e)
                            print(f"❌ 密钥验证失败: {key[:8]}... - {e}")

                        unique_secrets.append(secret)

            # 按置信度排序
            confidence_order = {'high': 3, 'medium': 2, 'low': 1}
            unique_secrets.sort(key=lambda x: confidence_order.get(x.get('confidence', 'low'), 0), reverse=True)

            print(f"✅ 雷电验证完成: {len(unique_secrets)} 个有效密钥")
            print()
            return unique_secrets

        except Exception as e:
            log_error(f"[雷电兼容] 密钥验证失败: {e}", component="LDExtractor")
            return []

    async def _save_leidian_results(self, secrets: List[Dict[str, Any]]):
        """保存雷电提取结果"""
        try:
            if not secrets:
                return

            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存到JSON文件
            output_dir = Path("./fb_2fa_results/leidian_extraction/")
            output_dir.mkdir(parents=True, exist_ok=True)

            json_file = output_dir / f"leidian_extracted_secrets_{self.emulator_id}_{timestamp}.json"

            result_data = {
                'emulator_id': self.emulator_id,
                'extraction_time': datetime.datetime.now().isoformat(),
                'extraction_method': 'leidian_compatible_extraction',
                'total_secrets': len(secrets),
                'secrets': secrets
            }

            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)

            # 保存到文本文件
            txt_file = output_dir / f"leidian_extracted_secrets_{self.emulator_id}_{timestamp}.txt"

            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(f"Facebook 2FA雷电兼容提取结果\n")
                f.write(f"模拟器ID: {self.emulator_id}\n")
                f.write(f"提取时间: {result_data['extraction_time']}\n")
                f.write(f"提取方法: 雷电模拟器兼容提取\n")
                f.write(f"总密钥数: {len(secrets)}\n")
                f.write("=" * 50 + "\n\n")

                for i, secret in enumerate(secrets, 1):
                    f.write(f"{i}. 密钥: {secret['secret_key']}\n")
                    f.write(f"   来源: {secret['source']}\n")
                    f.write(f"   提取方法: {secret['method']}\n")
                    f.write(f"   置信度: {secret.get('confidence', 'unknown')}\n")
                    if secret.get('verified'):
                        f.write(f"   测试验证码: {secret.get('test_totp', 'N/A')}\n")
                    f.write(f"   验证状态: {'✅ 有效' if secret.get('verified') else '❌ 无效'}\n")
                    f.write("-" * 30 + "\n")

            print(f"💾 雷电提取结果已保存:")
            print(f"   📄 {json_file}")
            print(f"   📄 {txt_file}")

        except Exception as e:
            log_error(f"[雷电兼容] 保存结果失败: {e}", component="LDExtractor")

    async def _display_leidian_results(self, secrets: List[Dict[str, Any]]):
        """显示雷电提取结果"""
        try:
            print("\n" + "=" * 50)
            print("🎯 Facebook 2FA雷电兼容提取结果")
            print("=" * 50)

            if not secrets:
                print("❌ 雷电兼容提取未找到任何2FA密钥")
                print("\n💡 最终结论:")
                print("经过所有可能的提取方法，包括：")
                print("- 常规文件系统扫描")
                print("- 深度数据库分析")
                print("- 内存和进程分析")
                print("- 系统服务利用")
                print("- 网络和日志分析")
                print("- 超级Root级提取")
                print("- 雷电模拟器兼容提取")
                print()
                print("🔍 **确认结论**: 您的Facebook账户目前没有启用2FA功能")
                print()
                print("📋 **解决方案**:")
                print("1. 通过官方渠道恢复Facebook账户访问权限")
                print("2. 联系Facebook客服，提供身份验证文件")
                print("3. 使用备用邮箱重置密码")
                print("4. 恢复访问后立即启用2FA并更新手机号码")
                return

            print(f"✅ 雷电兼容提取找到 {len(secrets)} 个2FA密钥:")
            print()

            for i, secret in enumerate(secrets, 1):
                confidence = secret.get('confidence', 'unknown').upper()
                print(f"🔑 密钥 {i} (置信度: {confidence}):")
                print(f"   密钥: {secret['secret_key']}")
                print(f"   来源: {secret['source']}")
                print(f"   提取方法: {secret['method']}")

                if secret.get('verified'):
                    print(f"   ✅ 验证状态: 有效")
                    print(f"   🔢 当前验证码: {secret.get('test_totp', 'N/A')}")
                    print(f"   📏 密钥长度: {secret.get('key_length', 0)} 字符")
                else:
                    print(f"   ❌ 验证状态: 无效")
                    if secret.get('error'):
                        print(f"   ⚠️  错误: {secret['error']}")

                print("-" * 30)

            print("\n🎉 雷电兼容提取成功!")
            print("💡 使用建议:")
            print("1. 优先使用置信度为'HIGH'的密钥")
            print("2. 在认证器应用中添加这些密钥")
            print("3. 验证生成的验证码是否与Facebook匹配")
            print("4. 成功验证后，立即更新Facebook的2FA设置")

        except Exception as e:
            log_error(f"[雷电兼容] 显示结果失败: {e}", component="LDExtractor")

    async def _cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir, ignore_errors=True)

            # 清理工作目录
            if hasattr(self, 'work_dir'):
                self.task.ld.execute_ld(self.emulator_id, f"rm -rf {self.work_dir}")
        except Exception:
            pass

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2

        print("🔓 雷电模拟器兼容的Facebook 2FA提取工具")
        print("⚠️  专门适配雷电模拟器权限限制")
        print("⚠️  使用替代方法绕过权限限制")
        print("⚠️  仅用于合法的账户恢复目的")
        print()

        confirm = input("确认继续雷电兼容提取? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return

        # 创建雷电兼容提取器
        extractor = LeiDianCompatibleExtractor(emulator_id)

        # 运行雷电兼容提取
        await extractor.run_leidian_extraction()

        print("\n" + "=" * 50)
        print("✅ Facebook 2FA雷电兼容提取完成")

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
