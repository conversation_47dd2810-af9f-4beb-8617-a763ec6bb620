#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 简单Facebook测试工具
========================================
功能描述: 最简单的Facebook跳转测试

使用方法:
python fb/simple_facebook_test.py
========================================
"""

import subprocess
import time

def test_facebook_jump():
    """测试Facebook跳转"""
    print("🔓 简单Facebook跳转测试")
    print("=" * 50)
    
    emulator_id = 2
    ldconsole_path = "G:/leidian/LDPlayer9/ldconsole.exe"
    
    print(f"📱 测试模拟器: {emulator_id}")
    print()
    
    # 测试1: 检查模拟器状态
    print("🔍 测试1: 检查模拟器状态...")
    try:
        result = subprocess.run([ldconsole_path, "list2"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            if f"雷电模拟器-{emulator_id}" in result.stdout:
                print("   ✅ 模拟器2存在")
            else:
                print("   ❌ 模拟器2不存在")
                return
        else:
            print("   ❌ 无法获取模拟器列表")
            return
    except Exception as e:
        print(f"   ❌ 检查模拟器状态失败: {e}")
        return
    
    # 测试2: 检查Facebook应用
    print("\n🔍 测试2: 检查Facebook应用...")
    facebook_packages = ["com.facebook.katana", "com.facebook.lite"]
    
    for package in facebook_packages:
        print(f"   🔍 检查: {package}")
        try:
            cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", 
                   "--value", f"shell pm list packages | grep {package}"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and package in result.stdout:
                print(f"      ✅ {package} 已安装")
                
                # 尝试启动这个应用
                print(f"      🚀 尝试启动 {package}...")
                start_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", 
                            "--value", f"shell monkey -p {package} -c android.intent.category.LAUNCHER 1"]
                start_result = subprocess.run(start_cmd, capture_output=True, text=True, timeout=15)
                
                if start_result.returncode == 0:
                    print(f"         ✅ {package} 启动命令成功")
                    time.sleep(5)  # 等待应用启动
                    
                    # 检查应用是否真正启动
                    check_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", 
                                "--value", "shell dumpsys window windows | grep -E 'mCurrentFocus'"]
                    check_result = subprocess.run(check_cmd, capture_output=True, text=True, timeout=10)
                    
                    if check_result.returncode == 0:
                        focus_info = check_result.stdout.lower()
                        if "facebook" in focus_info or "katana" in focus_info:
                            print(f"         🎉 {package} 真正启动成功!")
                            print(f"         📱 请查看模拟器2屏幕")
                            show_facebook_guide()
                            return
                        else:
                            print(f"         ⚠️ {package} 启动但未显示")
                            print(f"         当前焦点: {focus_info[:100]}...")
                    else:
                        print(f"         ❌ 无法检查 {package} 启动状态")
                else:
                    print(f"         ❌ {package} 启动命令失败")
            else:
                print(f"      ❌ {package} 未安装")
        except Exception as e:
            print(f"      ❌ 检查 {package} 异常: {e}")
    
    # 测试3: 尝试浏览器方式
    print("\n🔍 测试3: 尝试浏览器方式...")
    facebook_urls = [
        "https://m.facebook.com/login/identify",
        "https://www.facebook.com/login/identify"
    ]
    
    for i, url in enumerate(facebook_urls, 1):
        print(f"   🔍 尝试URL {i}: {url}")
        try:
            cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", 
                   "--value", f"shell am start -a android.intent.action.VIEW -d '{url}'"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                print(f"      ✅ URL {i} 启动命令成功")
                time.sleep(5)  # 等待浏览器加载
                
                # 检查浏览器是否打开
                check_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", 
                            "--value", "shell dumpsys window windows | grep -E 'mCurrentFocus'"]
                check_result = subprocess.run(check_cmd, capture_output=True, text=True, timeout=10)
                
                if check_result.returncode == 0:
                    focus_info = check_result.stdout.lower()
                    if any(keyword in focus_info for keyword in ['browser', 'chrome', 'webview', 'facebook']):
                        print(f"      🎉 URL {i} 在浏览器中成功打开!")
                        print(f"      📱 请查看模拟器2屏幕")
                        show_browser_guide()
                        return
                    else:
                        print(f"      ⚠️ URL {i} 启动但浏览器未显示")
                        print(f"      当前焦点: {focus_info[:100]}...")
                else:
                    print(f"      ❌ 无法检查URL {i} 打开状态")
            else:
                print(f"      ❌ URL {i} 启动命令失败")
        except Exception as e:
            print(f"      ❌ URL {i} 异常: {e}")
    
    # 测试4: 获取屏幕截图
    print("\n🔍 测试4: 获取屏幕截图...")
    try:
        screenshot_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", 
                         "--value", "shell screencap -p /sdcard/test_screenshot.png"]
        result = subprocess.run(screenshot_cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("   ✅ 屏幕截图成功")
            print("   💡 截图保存在模拟器的 /sdcard/test_screenshot.png")
        else:
            print("   ❌ 屏幕截图失败")
    except Exception as e:
        print(f"   ❌ 屏幕截图异常: {e}")
    
    # 如果所有方法都失败
    print("\n❌ 所有自动方法都失败")
    show_manual_guide()

def show_facebook_guide():
    """显示Facebook应用指导"""
    print("\n🎉 Facebook应用已启动!")
    print("=" * 50)
    print("📱 请在模拟器2中查看Facebook应用")
    print()
    print("🔍 寻找以下元素:")
    print("- '忘记密码?' 链接")
    print("- '找回账户' 按钮")
    print("- 登录页面的任何重置选项")
    print()
    print("📝 操作步骤:")
    print("1. 点击'忘记密码?'")
    print("2. 输入您的邮箱地址")
    print("3. 选择通过邮箱重置")
    print("4. 检查邮箱中的重置链接")

def show_browser_guide():
    """显示浏览器指导"""
    print("\n🌐 Facebook页面在浏览器中打开!")
    print("=" * 50)
    print("📱 请在模拟器2中查看浏览器")
    print()
    print("📝 操作步骤:")
    print("1. 在输入框中输入您的邮箱地址")
    print("2. 点击'搜索'或'查找账户'")
    print("3. 选择'通过邮箱重置密码'")
    print("4. 检查邮箱中的重置链接")

def show_manual_guide():
    """显示手动指导"""
    print("\n🔧 手动操作指导")
    print("=" * 50)
    print("请手动在模拟器2中执行以下操作:")
    print()
    print("📱 方法1: 手动打开Facebook应用")
    print("1. 在模拟器2中找到Facebook应用图标")
    print("2. 点击打开Facebook应用")
    print("3. 在登录页面点击'忘记密码?'")
    print()
    print("🌐 方法2: 手动打开浏览器")
    print("1. 在模拟器2中打开浏览器")
    print("2. 访问: https://m.facebook.com/login/identify")
    print("3. 输入邮箱地址并重置密码")
    print()
    print("💡 重要提示:")
    print("- 使用邮箱地址，不要使用手机号")
    print("- 选择邮箱重置，避免短信验证")
    print("- 检查邮箱的垃圾邮件文件夹")

def main():
    """主函数"""
    try:
        print("🔓 简单Facebook跳转测试")
        print("⚠️ 测试模拟器2的Facebook跳转功能")
        print("⚠️ 检查应用安装和启动情况")
        print()
        
        confirm = input("确认开始Facebook跳转测试? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 测试取消")
            return
        
        test_facebook_jump()
        
        print("\n" + "=" * 50)
        print("✅ Facebook跳转测试完成")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")

if __name__ == "__main__":
    main()
