#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业级雷电模拟器中控系统 - 基础配置UI模块

本模块提供了一个美观的基础配置界面，参考系统设置中模拟器操作的设计风格。
该配置页面作为共享组件，可被其他任务模块调用，支持热重载功能。

主要功能:
- APK配置：APK路径和包名设置
- 网络配置：订阅地址配置
- 模拟器配置：共享路径设置
- 任务配置：超时时间设置
- 休息功能：工作和休息时间配置
- 热加载：使用观察者模式自动响应配置变更
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                           QLabel, QScrollArea, QGridLayout, QFrame)
from PyQt6.QtCore import pyqtSignal
import logging

# 导入自定义UI组件
from .styled_widgets import (ModernSpinBox, ModernToggleSwitch, StyledLineEdit,
                           create_secondary_button)
from .style_manager import StyleManager
from core.simple_config import get_config_manager


class BasicConfigUI(QWidget):
    """
    基础配置界面
    ========================================
    功能描述: 所有任务共享的基础配置参数界面
    主要功能:
    - APK配置：APK路径和包名设置，支持多个路径用|分割
    - 网络配置：V2Ray订阅地址配置
    - 任务配置：模拟器共享路径、任务超时时间设置
    - 休息功能：工作和休息时间范围配置，支持启用/禁用
    - 配置管理：与simple_config.py统一配置系统集成，支持热重载
    ========================================
    """
    
    # 🎯 信号定义 - UI与业务层通信
    config_changed = pyqtSignal(str, object)  # 配置变更信号：参数名, 新值

    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 🎯 核心组件初始化
        self.config_manager = get_config_manager()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 🎯 UI组件初始化
        self.init_ui()
        self.load_settings()
        self.connect_signals()
        
        # 🎯 关键：注册为配置观察者，实现热加载
        self.setup_hot_reload()

        # 🎯 连接浏览按钮事件
        self.connect_browse_buttons()

        self.logger.info("基础配置UI初始化完成")

    def setup_hot_reload(self):
        """
        设置热加载功能
        ========================================
        功能描述: 注册为配置观察者，实现配置文件变更时UI自动更新
        实现方式: 使用UnifiedConfigManager的观察者模式
        监听范围: basic_config.* 和 rest_config.* 配置项
        ========================================
        """
        try:
            # 注册为配置观察者，当配置文件变化时自动重新加载UI
            self.config_manager.register_observer(self.on_config_changed)
            self.logger.info("基础配置UI已注册为配置观察者，支持热加载")
        except Exception as e:
            self.logger.error(f"设置热加载功能失败: {e}")

    def on_config_changed(self, key: str, old_value, new_value):
        """
        配置变更回调
        ========================================
        功能描述: 响应配置文件变更，自动更新UI显示
        参数说明:
        - key: 配置项键名，如 basic_config.apk_paths
        - old_value: 变更前的值
        - new_value: 变更后的值
        处理逻辑: 只处理基础配置和休息配置相关的变更
        ========================================
        """
        try:
            # 只处理基础配置和休息配置相关的变更
            if key.startswith(('basic_config.', 'rest_config.')):
                self.logger.info(f"检测到配置热加载: {key} = {old_value} -> {new_value}")
                # 重新加载UI显示，实现热加载效果
                self.load_settings()
        except Exception as e:
            self.logger.error(f"处理配置变更失败: {e}")

    def init_ui(self):
        """创建界面布局 - 参考系统设置模拟器操作页面的布局方式"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")

        # 创建内容容器
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(30, 30, 30, 30)  # 参考系统设置的边距
        layout.setSpacing(25)  # 参考系统设置的间距

        # 标题 - 参考Instagram私信配置的标题样式
        title_label = QLabel("基础配置")
        title_label.setStyleSheet("font-size: 22px; font-weight: bold; color: #333; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # APK配置区域
        apk_section = self.create_apk_config_section()
        layout.addWidget(apk_section)

        # 网络配置区域
        network_section = self.create_network_config_section()
        layout.addWidget(network_section)

        # 任务配置区域
        task_section = self.create_task_config_section()
        layout.addWidget(task_section)

        # 休息功能配置区域
        rest_section = self.create_rest_config_section()
        layout.addWidget(rest_section)

        layout.addStretch()

        scroll_area.setWidget(content_widget)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

    def create_apk_config_section(self):
        """创建APK配置区域 - 参考系统设置的分组框样式"""
        apk_group = QGroupBox()
        apk_group.setStyleSheet(StyleManager.get_groupbox_style('primary'))

        # 创建标题区域，包含标题和复制分割符按钮
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(10)

        # 标题标签
        title_label = QLabel("📱 APK配置")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #333333;
                padding: 0px;
                margin: 0px;
            }
        """)

        # 复制分割符按钮
        self.copy_separator_btn = create_secondary_button("点击复制分割符号")
        self.copy_separator_btn.setFixedSize(120, 25)
        self.copy_separator_btn.setToolTip("点击复制分割符号 | 到剪贴板")
        self.copy_separator_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border-radius: 12px;
                padding: 2px 6px;
                font-size: 14px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
        """)

        title_layout.addWidget(title_label)
        title_layout.addWidget(self.copy_separator_btn)
        title_layout.addStretch()  # 推到左边

        # 设置自定义标题
        apk_group.setTitle("")  # 清空默认标题

        # 创建主布局容器
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        main_layout.addWidget(title_widget)

        # 创建内容区域的网格布局
        content_widget = QWidget()
        layout = QGridLayout(content_widget)
        layout.setContentsMargins(0, 10, 0, 0)
        layout.setVerticalSpacing(15)
        layout.setHorizontalSpacing(10)
        layout.setColumnMinimumWidth(0, 120)  # 调整标签列宽度
        layout.setColumnStretch(1, 1)  # 让输入框列可以拉伸，充分利用空间

        # 将内容区域添加到主布局
        main_layout.addWidget(content_widget)

        # 设置apk_group的布局
        group_layout = QVBoxLayout(apk_group)
        group_layout.setContentsMargins(25, 15, 25, 25)
        group_layout.addWidget(main_widget)

        row = 0

        # APK路径
        label = QLabel("APK路径:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))
        self.apk_path_edit = StyledLineEdit()
        self.apk_path_edit.setPlaceholderText("多个路径用 | 分割，如: /path/app.apk|/path2/app.apk")
        self.apk_path_edit.setFixedHeight(40)  # 增加高度以确保字体完整显示
        # 移除固定宽度，让输入框自适应拉伸

        self.apk_path_btn = create_secondary_button("浏览")  # 创建浏览按钮
        self.apk_path_btn.setFixedSize(60, 35)  # 设置按钮固定尺寸
        # 设置更圆润的样式
        self.apk_path_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border-radius: 17px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #999999;
            }
        """)
        apk_path_widget = QWidget()
        apk_path_widget.setFixedHeight(40)  # 设置容器固定高度匹配输入框
        apk_path_layout = QHBoxLayout(apk_path_widget)
        apk_path_layout.setContentsMargins(0, 0, 0, 0)
        apk_path_layout.setSpacing(8)  # 设置间距
        apk_path_layout.addWidget(self.apk_path_edit, 1)  # 输入框占更多空间
        apk_path_layout.addWidget(self.apk_path_btn, 0)  # 按钮不拉伸

        layout.addWidget(label, row, 0)
        layout.addWidget(apk_path_widget, row, 1)
        row += 1

        # APK包名
        label = QLabel("APK包名:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))
        self.apk_package_edit = StyledLineEdit()
        self.apk_package_edit.setPlaceholderText("多个包名用 | 分割，如: com.app1|com.app2")
        self.apk_package_edit.setFixedHeight(40)  # 增加高度以确保字体完整显示

        layout.addWidget(label, row, 0)
        layout.addWidget(self.apk_package_edit, row, 1)
        row += 1

        return apk_group

    def create_network_config_section(self):
        """创建网络配置区域"""
        network_group = QGroupBox("🌐 网络配置")
        network_group.setStyleSheet(StyleManager.get_groupbox_style('success'))

        layout = QGridLayout(network_group)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setVerticalSpacing(15)
        layout.setHorizontalSpacing(10)
        layout.setColumnMinimumWidth(0, 120)  # 调整标签列宽度
        layout.setColumnStretch(1, 1)  # 让输入框列可以拉伸，充分利用空间

        row = 0

        # 订阅地址
        label = QLabel("订阅地址:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))
        self.subscription_edit = StyledLineEdit()
        self.subscription_edit.setPlaceholderText("输入V2Ray订阅地址")
        self.subscription_edit.setFixedHeight(40)  # 增加高度以确保字体完整显示
        # 移除固定宽度，让输入框自适应拉伸

        layout.addWidget(label, row, 0)
        layout.addWidget(self.subscription_edit, row, 1)
        row += 1

        return network_group

    def create_task_config_section(self):
        """创建任务配置区域"""
        task_group = QGroupBox("⚙️ 任务配置")
        task_group.setStyleSheet(StyleManager.get_groupbox_style('warning'))

        layout = QGridLayout(task_group)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setVerticalSpacing(15)
        layout.setHorizontalSpacing(10)
        layout.setColumnMinimumWidth(0, 120)  # 调整标签列宽度
        layout.setColumnStretch(1, 1)  # 让输入框列可以拉伸，充分利用空间

        row = 0

        # 模拟器共享路径
        label = QLabel("模拟器共享路径:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))
        self.emulator_path_edit = StyledLineEdit()
        self.emulator_path_edit.setPlaceholderText("设置模拟器共享文件夹路径")
        self.emulator_path_edit.setFixedHeight(40)  # 增加高度以确保字体完整显示
        # 移除固定宽度，让输入框自适应拉伸

        self.emulator_path_btn = create_secondary_button("浏览")  # 创建浏览按钮
        self.emulator_path_btn.setFixedSize(60, 35)  # 设置按钮固定尺寸
        # 设置更圆润的样式
        self.emulator_path_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border-radius: 17px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #999999;
            }
        """)
        emulator_path_widget = QWidget()
        emulator_path_widget.setFixedHeight(40)  # 设置容器固定高度匹配输入框
        emulator_path_layout = QHBoxLayout(emulator_path_widget)
        emulator_path_layout.setContentsMargins(0, 0, 0, 0)
        emulator_path_layout.setSpacing(8)  # 设置间距
        emulator_path_layout.addWidget(self.emulator_path_edit, 1)  # 输入框占更多空间
        emulator_path_layout.addWidget(self.emulator_path_btn, 0)  # 按钮不拉伸

        layout.addWidget(label, row, 0)
        layout.addWidget(emulator_path_widget, row, 1)
        row += 1

        # 任务总超时时间 - 参考系统设置的数字输入框样式
        label = QLabel("任务总超时时间:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))
        self.timeout_spinbox = ModernSpinBox(suffix=" 秒")
        self.timeout_spinbox.setRange(1, 10800)  # 1秒到3小时(10800秒)
        self.timeout_spinbox.setFixedWidth(200)  # 参考系统设置的固定宽度
        
        layout.addWidget(label, row, 0)
        layout.addWidget(self.timeout_spinbox, row, 1)
        row += 1

        return task_group

    def create_rest_config_section(self):
        """创建休息功能配置区域"""
        rest_group = QGroupBox("😴 休息功能配置")
        rest_group.setStyleSheet(StyleManager.get_groupbox_style('info'))

        layout = QGridLayout(rest_group)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setVerticalSpacing(15)
        layout.setHorizontalSpacing(10)
        layout.setColumnMinimumWidth(0, 100)  # 调整标签列宽度为100
        layout.setColumnStretch(1, 0)
        layout.setColumnStretch(2, 1)

        row = 0

        # 启用休息功能开关
        label = QLabel("启用休息功能:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))
        self.rest_enabled_switch = ModernToggleSwitch()

        layout.addWidget(label, row, 0)
        layout.addWidget(self.rest_enabled_switch, row, 1)
        row += 1

        # 操作时间范围
        label = QLabel("操作时间范围:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))
        work_time_widget = QWidget()
        work_time_layout = QHBoxLayout(work_time_widget)
        work_time_layout.setContentsMargins(0, 0, 0, 0)
        work_time_layout.setSpacing(10)

        self.work_time_min_spinbox = ModernSpinBox()
        self.work_time_min_spinbox.setRange(1, 3600)
        self.work_time_min_spinbox.setFixedWidth(100)
        self.work_time_min_spinbox.spinbox.setSuffix("  秒")

        work_time_to_label = QLabel("到")
        work_time_to_label.setStyleSheet("font-size: 14px; color: #666;")

        self.work_time_max_spinbox = ModernSpinBox()
        self.work_time_max_spinbox.setRange(1, 3600)
        self.work_time_max_spinbox.setFixedWidth(100)
        self.work_time_max_spinbox.spinbox.setSuffix("  秒")

        work_time_layout.addWidget(self.work_time_min_spinbox)
        work_time_layout.addWidget(work_time_to_label)
        work_time_layout.addWidget(self.work_time_max_spinbox)
        work_time_layout.addStretch()

        layout.addWidget(label, row, 0)
        layout.addWidget(work_time_widget, row, 1)
        row += 1

        # 休息时间范围
        label = QLabel("休息时间范围:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))
        rest_time_widget = QWidget()
        rest_time_layout = QHBoxLayout(rest_time_widget)
        rest_time_layout.setContentsMargins(0, 0, 0, 0)
        rest_time_layout.setSpacing(10)

        self.rest_time_min_spinbox = ModernSpinBox()
        self.rest_time_min_spinbox.setRange(1, 300)
        self.rest_time_min_spinbox.setFixedWidth(100)
        self.rest_time_min_spinbox.spinbox.setSuffix("  秒")

        rest_time_to_label = QLabel("到")
        rest_time_to_label.setStyleSheet("font-size: 14px; color: #666;")

        self.rest_time_max_spinbox = ModernSpinBox()
        self.rest_time_max_spinbox.setRange(1, 300)
        self.rest_time_max_spinbox.setFixedWidth(100)
        self.rest_time_max_spinbox.spinbox.setSuffix("  秒")

        rest_time_layout.addWidget(self.rest_time_min_spinbox)
        rest_time_layout.addWidget(rest_time_to_label)
        rest_time_layout.addWidget(self.rest_time_max_spinbox)
        rest_time_layout.addStretch()

        layout.addWidget(label, row, 0)
        layout.addWidget(rest_time_widget, row, 1)
        row += 1

        return rest_group

    def load_settings(self):
        """
        从配置文件加载设置 - 参考系统设置的加载方式
        ========================================
        功能描述: 从统一配置管理器加载基础配置参数到UI控件
        配置映射: 使用扁平化配置键名，与simple_config.py保持一致
        错误处理: 加载失败时使用默认值，确保UI正常显示
        ========================================
        """
        try:
            # 🎯 基础配置设置列表 - 使用扁平化配置键名
            basic_settings = [
                ('apk_path_edit', 'basic_config.apk_paths', ''),
                ('apk_package_edit', 'basic_config.apk_packages', ''),
                ('subscription_edit', 'basic_config.subscription_url', ''),
                ('emulator_path_edit', 'basic_config.emulator_shared_path', ''),
                ('timeout_spinbox', 'basic_config.task_timeout_seconds', 900),
            ]

            for widget_name, config_key, default_value in basic_settings:
                if hasattr(self, widget_name):
                    widget = getattr(self, widget_name)
                    value = self.config_manager.get(config_key, default_value)
                    if hasattr(widget, 'setText'):
                        widget.setText(str(value))
                    elif hasattr(widget, 'setValue'):
                        widget.setValue(value)
                    self.logger.debug(f"加载基础配置: {config_key} = {value}")

            # 🎯 休息功能配置设置列表
            rest_settings = [
                ('rest_enabled_switch', 'rest_config.enabled', False),
                ('work_time_min_spinbox', 'rest_config.work_time_min', 30),
                ('work_time_max_spinbox', 'rest_config.work_time_max', 60),
                ('rest_time_min_spinbox', 'rest_config.rest_time_min', 5),
                ('rest_time_max_spinbox', 'rest_config.rest_time_max', 10),
            ]

            for widget_name, config_key, default_value in rest_settings:
                if hasattr(self, widget_name):
                    widget = getattr(self, widget_name)
                    value = self.config_manager.get(config_key, default_value)
                    if hasattr(widget, 'set_checked'):
                        widget.set_checked(value)
                    elif hasattr(widget, 'setValue'):
                        widget.setValue(value)
                    self.logger.debug(f"加载休息配置: {config_key} = {value}")

            self.logger.info("基础配置加载完成")
        except Exception as e:
            self.logger.error(f"加载基础配置失败: {e}")

    def connect_signals(self):
        """
        连接信号和槽 - 实现配置变更时自动保存
        ========================================
        功能描述: 连接UI控件的变更信号到配置保存方法
        实现方式: 使用lambda表达式传递配置键名和新值
        自动保存: 配置变更时立即保存到配置文件，无需手动保存按钮
        ========================================
        """
        try:
            # APK配置变更信号
            self.apk_path_edit.textChanged.connect(
                lambda text: self._on_setting_changed('basic_config.apk_paths', text)
            )
            self.apk_package_edit.textChanged.connect(
                lambda text: self._on_setting_changed('basic_config.apk_packages', text)
            )

            # 网络配置变更信号
            self.subscription_edit.textChanged.connect(
                lambda text: self._on_setting_changed('basic_config.subscription_url', text)
            )

            # 任务配置变更信号
            self.emulator_path_edit.textChanged.connect(
                lambda text: self._on_setting_changed('basic_config.emulator_shared_path', text)
            )
            self.timeout_spinbox.valueChanged.connect(
                lambda value: self._on_setting_changed('basic_config.task_timeout_seconds', value)
            )

            # 休息功能配置变更信号
            self.rest_enabled_switch.toggled.connect(
                lambda checked: self._on_setting_changed('rest_config.enabled', checked)
            )
            self.work_time_min_spinbox.valueChanged.connect(
                lambda value: self._on_setting_changed('rest_config.work_time_min', value)
            )
            self.work_time_max_spinbox.valueChanged.connect(
                lambda value: self._on_setting_changed('rest_config.work_time_max', value)
            )
            self.rest_time_min_spinbox.valueChanged.connect(
                lambda value: self._on_setting_changed('rest_config.rest_time_min', value)
            )
            self.rest_time_max_spinbox.valueChanged.connect(
                lambda value: self._on_setting_changed('rest_config.rest_time_max', value)
            )

            self.logger.info("基础配置信号连接完成")
        except Exception as e:
            self.logger.error(f"连接基础配置信号失败: {e}")

    def _on_setting_changed(self, key: str, value):
        """
        处理设置变更 - 参考系统设置的处理方式
        ========================================
        功能描述: 配置变更时自动保存到统一配置系统并发送变更信号
        参数说明:
        - key: 配置键名，如 basic_config.apk_paths
        - value: 新的配置值
        实现逻辑: 立即保存配置，发送变更信号，记录日志
        ========================================
        """
        try:
            if not self.config_manager:
                self.logger.warning(f"配置管理器未初始化，设置更新失败: {key} = {value}")
                return

            # 保存配置到统一配置系统
            success = self.config_manager.set(key, value)
            if success:
                # 立即保存到文件
                self.config_manager.save()
                # 发送配置变更信号
                self.config_changed.emit(key, value)
                self.logger.debug(f"基础配置已更新: {key} = {value}")
            else:
                self.logger.error(f"基础配置更新失败: {key} = {value}")

        except Exception as e:
            self.logger.error(f"处理基础配置变更失败: {key} = {value}, 错误: {e}")

    def __del__(self):
        """析构函数 - 清理观察者"""
        try:
            if hasattr(self, 'config_manager') and self.config_manager:
                self.config_manager.unregister_observer(self.on_config_changed)
                self.logger.info("基础配置UI观察者已注销")
        except Exception as e:
            self.logger.error(f"注销基础配置观察者失败: {e}")

    def connect_browse_buttons(self):
        """连接浏览按钮事件"""
        try:
            # 连接APK路径浏览按钮
            if hasattr(self, 'apk_path_btn'):
                self.apk_path_btn.clicked.connect(self.browse_apk_path)

            # 连接模拟器共享路径浏览按钮
            if hasattr(self, 'emulator_path_btn'):
                self.emulator_path_btn.clicked.connect(self.browse_emulator_path)

            # 连接复制分割符按钮
            if hasattr(self, 'copy_separator_btn'):
                self.copy_separator_btn.clicked.connect(self.copy_separator)

            self.logger.info("浏览按钮事件连接完成")
        except Exception as e:
            self.logger.error(f"连接浏览按钮事件失败: {e}")

    def browse_apk_path(self):
        """浏览APK文件路径"""
        try:
            from PyQt6.QtWidgets import QFileDialog

            # 获取当前路径
            current_path = self.apk_path_edit.text()

            # 打开文件选择对话框
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择APK文件",
                current_path,
                "APK文件 (*.apk);;所有文件 (*)"
            )

            if file_path:
                # 如果当前输入框有内容，追加新路径
                current_text = self.apk_path_edit.text()
                if current_text:
                    new_text = f"{current_text}|{file_path}"
                else:
                    new_text = file_path

                self.apk_path_edit.setText(new_text)
                self.logger.info(f"APK路径已更新: {file_path}")

        except Exception as e:
            self.logger.error(f"浏览APK路径失败: {e}")

    def browse_emulator_path(self):
        """浏览模拟器共享路径"""
        try:
            from PyQt6.QtWidgets import QFileDialog

            # 获取当前路径
            current_path = self.emulator_path_edit.text()

            # 打开文件夹选择对话框
            folder_path = QFileDialog.getExistingDirectory(
                self,
                "选择模拟器共享文件夹",
                current_path,
                QFileDialog.Option.ShowDirsOnly
            )

            if folder_path:
                self.emulator_path_edit.setText(folder_path)
                self.logger.info(f"模拟器共享路径已更新: {folder_path}")

        except Exception as e:
            self.logger.error(f"浏览模拟器共享路径失败: {e}")

    def copy_separator(self):
        """复制分割符号到剪贴板"""
        try:
            from PyQt6.QtWidgets import QApplication

            # 复制分割符到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText("|")

            self.logger.info("分割符号 | 已复制到剪贴板")

            # 可选：显示一个短暂的提示
            self.copy_separator_btn.setText("✓ 已复制")
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(1500, lambda: self.copy_separator_btn.setText("点击复制分割符号"))

        except Exception as e:
            self.logger.error(f"复制分割符失败: {e}")
