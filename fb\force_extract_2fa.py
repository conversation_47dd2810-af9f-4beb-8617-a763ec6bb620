#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 Facebook 2FA密钥强制提取工具
========================================
功能描述: 当无法通过正常方式获取2FA密钥时，尝试从底层数据强制提取

适用场景:
- 无法访问Facebook 2FA设置页面
- 手机号码已更换无法接收验证码
- 需要恢复已配置的2FA密钥

主要功能:
1. 深度扫描Facebook应用数据
2. 提取加密的认证数据
3. 解析SQLite数据库中的2FA信息
4. 搜索内存转储中的密钥
5. 分析网络请求中的认证信息

使用方法:
python fb/force_extract_2fa.py [emulator_id]

注意事项:
- 需要root权限
- 可能需要Facebook应用处于登录状态
- 仅用于合法的账户恢复目的
========================================
"""

import asyncio
import sys
import re
import json
import base64
import sqlite3
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class FacebookTwoFactorForceExtractor:
    """Facebook 2FA密钥强制提取器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化强制提取器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.temp_dir = Path(tempfile.gettempdir()) / f"fb_force_extract_{emulator_id}"
        self.temp_dir.mkdir(exist_ok=True)
        
        # 可能包含2FA数据的文件模式
        self.target_patterns = [
            "*auth*", "*2fa*", "*totp*", "*secret*", "*token*", "*otp*",
            "*security*", "*verify*", "*code*", "*key*", "*.db", "*.sqlite",
            "*.json", "*.xml", "*.plist", "*.dat", "*.bin"
        ]
        
        log_info(f"[强制提取] Facebook 2FA强制提取器初始化 - 模拟器{emulator_id}", component="ForceExtractor")

    async def run_force_extraction(self):
        """运行强制提取流程"""
        try:
            log_info(f"[强制提取] 开始Facebook 2FA强制提取", component="ForceExtractor")
            
            print("🔓 Facebook 2FA密钥强制提取工具")
            print("=" * 50)
            print("⚠️  此工具用于紧急情况下的密钥恢复")
            print("⚠️  请确保您有合法权限访问此账户")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 检查Facebook应用状态
            await self._check_facebook_status()
            
            # 强制提取方法1: 深度数据库扫描
            secrets1 = await self._method1_deep_database_scan()
            
            # 强制提取方法2: 内存转储分析
            secrets2 = await self._method2_memory_dump_analysis()
            
            # 强制提取方法3: 网络流量分析
            secrets3 = await self._method3_network_traffic_analysis()
            
            # 强制提取方法4: 应用缓存分析
            secrets4 = await self._method4_app_cache_analysis()
            
            # 强制提取方法5: 系统日志分析
            secrets5 = await self._method5_system_log_analysis()
            
            # 合并所有结果
            all_secrets = []
            all_secrets.extend(secrets1)
            all_secrets.extend(secrets2)
            all_secrets.extend(secrets3)
            all_secrets.extend(secrets4)
            all_secrets.extend(secrets5)
            
            # 去重和验证
            unique_secrets = await self._process_extracted_secrets(all_secrets)
            
            # 保存结果
            await self._save_extraction_results(unique_secrets)
            
            # 显示结果
            await self._display_results(unique_secrets)
            
            log_info(f"[强制提取] Facebook 2FA强制提取完成", component="ForceExtractor")
            
        except Exception as e:
            log_error(f"[强制提取] 强制提取失败: {e}", component="ForceExtractor")
        finally:
            # 清理临时文件
            await self._cleanup()

    async def _check_facebook_status(self):
        """检查Facebook应用状态"""
        try:
            print("🔍 检查Facebook应用状态...")
            
            # 检查应用是否安装
            success, result = self.task.ld.execute_ld(self.emulator_id, "pm list packages | grep facebook")
            if success and "com.facebook.katana" in result:
                print("✅ Facebook应用已安装")
            else:
                print("❌ Facebook应用未安装")
                return
            
            # 检查应用是否正在运行
            success, result = self.task.ld.execute_ld(self.emulator_id, "ps | grep facebook")
            if success and "facebook" in result:
                print("✅ Facebook应用正在运行")
            else:
                print("⚠️  Facebook应用未运行，建议先启动应用")
            
            # 检查登录状态
            success, result = self.task.ld.execute_ld(self.emulator_id, "dumpsys account | grep facebook")
            if success and result.strip():
                print("✅ 检测到Facebook账户登录")
                # 提取账户信息
                lines = result.strip().split('\n')
                for line in lines:
                    if 'name=' in line:
                        print(f"📱 账户: {line.strip()}")
                        break
            else:
                print("⚠️  未检测到Facebook账户登录")
            
            print()
            
        except Exception as e:
            log_error(f"[强制提取] 检查Facebook状态失败: {e}", component="ForceExtractor")

    async def _method1_deep_database_scan(self) -> List[Dict[str, Any]]:
        """方法1: 深度数据库扫描"""
        try:
            print("🗄️  方法1: 深度数据库扫描")
            print("-" * 30)
            
            secrets = []
            
            # 扫描Facebook应用的所有数据库文件
            db_locations = [
                "/data/data/com.facebook.katana/databases/",
                "/data/data/com.facebook.katana/app_webview/databases/",
                "/data/data/com.facebook.katana/app_textures/databases/",
                "/data/data/com.facebook.katana/cache/",
                "/storage/emulated/0/Android/data/com.facebook.katana/databases/",
            ]
            
            for location in db_locations:
                print(f"🔍 扫描位置: {location}")
                
                # 查找所有数据库文件
                success, result = self.task.ld.execute_ld(self.emulator_id, f"find {location} -name '*.db' -o -name '*.sqlite' 2>/dev/null")
                if success and result.strip():
                    db_files = result.strip().split('\n')
                    print(f"   找到 {len(db_files)} 个数据库文件")
                    
                    for db_file in db_files:
                        if db_file.strip():
                            print(f"   📊 分析: {db_file}")
                            db_secrets = await self._analyze_database_file(db_file.strip())
                            if db_secrets:
                                secrets.extend(db_secrets)
                                print(f"   ✅ 找到 {len(db_secrets)} 个可能的密钥")
                else:
                    print(f"   ❌ 未找到数据库文件")
            
            print(f"🗄️  方法1完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets
            
        except Exception as e:
            log_error(f"[强制提取] 深度数据库扫描失败: {e}", component="ForceExtractor")
            return []

    async def _method2_memory_dump_analysis(self) -> List[Dict[str, Any]]:
        """方法2: 内存转储分析"""
        try:
            print("🧠 方法2: 内存转储分析")
            print("-" * 30)
            
            secrets = []
            
            # 获取Facebook应用的进程ID
            success, result = self.task.ld.execute_ld(self.emulator_id, "ps | grep com.facebook.katana")
            if not success or not result.strip():
                print("❌ Facebook应用未运行，跳过内存分析")
                return []
            
            # 提取PID
            lines = result.strip().split('\n')
            pid = None
            for line in lines:
                parts = line.split()
                if len(parts) > 1 and 'com.facebook.katana' in line:
                    pid = parts[1]
                    break
            
            if not pid:
                print("❌ 无法获取Facebook应用PID")
                return []
            
            print(f"📱 Facebook应用PID: {pid}")
            
            # 尝试读取进程内存映射
            success, result = self.task.ld.execute_ld(self.emulator_id, f"cat /proc/{pid}/maps | grep -E '(heap|stack|anon)'")
            if success and result.strip():
                print("✅ 获取内存映射成功")
                
                # 尝试从内存中搜索可能的密钥模式
                memory_secrets = await self._search_memory_patterns(pid)
                if memory_secrets:
                    secrets.extend(memory_secrets)
                    print(f"✅ 从内存中找到 {len(memory_secrets)} 个可能的密钥")
            else:
                print("❌ 无法访问进程内存")
            
            print(f"🧠 方法2完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets
            
        except Exception as e:
            log_error(f"[强制提取] 内存转储分析失败: {e}", component="ForceExtractor")
            return []

    async def _method3_network_traffic_analysis(self) -> List[Dict[str, Any]]:
        """方法3: 网络流量分析"""
        try:
            print("🌐 方法3: 网络流量分析")
            print("-" * 30)
            
            secrets = []
            
            # 检查网络连接
            success, result = self.task.ld.execute_ld(self.emulator_id, "netstat -an | grep facebook")
            if success and result.strip():
                print("✅ 检测到Facebook网络连接")
                connections = result.strip().split('\n')
                print(f"   活跃连接数: {len(connections)}")
            else:
                print("❌ 未检测到Facebook网络连接")
            
            # 尝试分析网络日志
            log_locations = [
                "/data/data/com.facebook.katana/files/",
                "/data/data/com.facebook.katana/cache/",
                "/sdcard/Android/data/com.facebook.katana/files/",
            ]
            
            for location in log_locations:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"find {location} -name '*log*' -o -name '*network*' -o -name '*http*' 2>/dev/null")
                if success and result.strip():
                    log_files = result.strip().split('\n')
                    print(f"🔍 分析网络日志: {len(log_files)} 个文件")
                    
                    for log_file in log_files[:5]:  # 只分析前5个文件
                        if log_file.strip():
                            log_secrets = await self._analyze_network_log(log_file.strip())
                            if log_secrets:
                                secrets.extend(log_secrets)
            
            print(f"🌐 方法3完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets
            
        except Exception as e:
            log_error(f"[强制提取] 网络流量分析失败: {e}", component="ForceExtractor")
            return []

    async def _method4_app_cache_analysis(self) -> List[Dict[str, Any]]:
        """方法4: 应用缓存分析"""
        try:
            print("💾 方法4: 应用缓存分析")
            print("-" * 30)
            
            secrets = []
            
            # 分析应用缓存目录
            cache_locations = [
                "/data/data/com.facebook.katana/cache/",
                "/data/data/com.facebook.katana/code_cache/",
                "/data/data/com.facebook.katana/app_webview/",
                "/storage/emulated/0/Android/data/com.facebook.katana/cache/",
            ]
            
            for location in cache_locations:
                print(f"🔍 分析缓存: {location}")
                
                # 查找所有缓存文件
                success, result = self.task.ld.execute_ld(self.emulator_id, f"find {location} -type f 2>/dev/null | head -20")
                if success and result.strip():
                    cache_files = result.strip().split('\n')
                    print(f"   找到 {len(cache_files)} 个缓存文件")
                    
                    for cache_file in cache_files:
                        if cache_file.strip():
                            # 搜索可能的密钥模式
                            success2, content = self.task.ld.execute_ld(self.emulator_id, f"strings '{cache_file}' | grep -E '[A-Z2-7]{{16,}}' | head -5")
                            if success2 and content.strip():
                                potential_keys = content.strip().split('\n')
                                for key in potential_keys:
                                    if self._is_valid_base32_key(key.strip()):
                                        secrets.append({
                                            'secret_key': key.strip(),
                                            'source': f'cache:{cache_file}',
                                            'method': 'app_cache_analysis'
                                        })
                                        print(f"   🔑 发现密钥: {key.strip()[:8]}...")
                else:
                    print(f"   ❌ 无法访问缓存目录")
            
            print(f"💾 方法4完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets
            
        except Exception as e:
            log_error(f"[强制提取] 应用缓存分析失败: {e}", component="ForceExtractor")
            return []

    async def _method5_system_log_analysis(self) -> List[Dict[str, Any]]:
        """方法5: 系统日志分析"""
        try:
            print("📋 方法5: 系统日志分析")
            print("-" * 30)
            
            secrets = []
            
            # 分析系统日志中的Facebook相关信息
            log_commands = [
                "logcat -d | grep -i facebook | grep -E '(auth|2fa|totp|secret|token)'",
                "dmesg | grep -i facebook",
                "cat /proc/kmsg | grep -i facebook 2>/dev/null || echo 'no kmsg access'"
            ]
            
            for cmd in log_commands:
                print(f"🔍 执行: {cmd.split('|')[0]}...")
                success, result = self.task.ld.execute_ld(self.emulator_id, cmd)
                if success and result.strip() and "no kmsg access" not in result:
                    lines = result.strip().split('\n')
                    print(f"   找到 {len(lines)} 条相关日志")
                    
                    # 搜索日志中的密钥模式
                    for line in lines[:10]:  # 只分析前10条
                        if line.strip():
                            # 查找Base32模式
                            base32_matches = re.findall(r'[A-Z2-7]{16,}', line)
                            for match in base32_matches:
                                if self._is_valid_base32_key(match):
                                    secrets.append({
                                        'secret_key': match,
                                        'source': 'system_log',
                                        'method': 'system_log_analysis',
                                        'log_line': line.strip()[:100]
                                    })
                                    print(f"   🔑 日志中发现密钥: {match[:8]}...")
                else:
                    print(f"   ❌ 无日志数据或无访问权限")
            
            print(f"📋 方法5完成: 找到 {len(secrets)} 个可能的密钥")
            print()
            return secrets
            
        except Exception as e:
            log_error(f"[强制提取] 系统日志分析失败: {e}", component="ForceExtractor")
            return []

    async def _analyze_database_file(self, db_file: str) -> List[Dict[str, Any]]:
        """分析数据库文件"""
        try:
            secrets = []

            # 将数据库文件复制到本地进行分析
            local_db = self.temp_dir / f"temp_{Path(db_file).name}"

            success, _ = self.task.ld.execute_ld(self.emulator_id, f"cp '{db_file}' /sdcard/temp_db.db")
            if success:
                # 通过共享目录获取文件
                success2, _ = self.task.ld.execute_ld(self.emulator_id, f"cp /sdcard/temp_db.db '{self.task.ld.share_path}/temp_db.db'")
                if success2:
                    share_db_path = Path(self.task.ld.share_path) / "temp_db.db"
                    if share_db_path.exists():
                        # 分析SQLite数据库
                        try:
                            conn = sqlite3.connect(str(share_db_path))
                            cursor = conn.cursor()

                            # 获取所有表名
                            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                            tables = cursor.fetchall()

                            for table in tables:
                                table_name = table[0]
                                try:
                                    # 搜索可能包含密钥的列
                                    cursor.execute(f"PRAGMA table_info({table_name})")
                                    columns = cursor.fetchall()

                                    auth_columns = []
                                    for col in columns:
                                        col_name = col[1].lower()
                                        if any(keyword in col_name for keyword in ['auth', '2fa', 'secret', 'token', 'key', 'otp']):
                                            auth_columns.append(col[1])

                                    if auth_columns:
                                        # 查询这些列的数据
                                        for col in auth_columns:
                                            cursor.execute(f"SELECT {col} FROM {table_name} WHERE {col} IS NOT NULL LIMIT 10")
                                            rows = cursor.fetchall()

                                            for row in rows:
                                                value = str(row[0])
                                                if self._is_valid_base32_key(value):
                                                    secrets.append({
                                                        'secret_key': value,
                                                        'source': f'db:{db_file}:{table_name}:{col}',
                                                        'method': 'database_analysis'
                                                    })

                                except Exception:
                                    continue

                            conn.close()

                        except Exception as e:
                            log_error(f"[强制提取] SQLite分析失败: {e}", component="ForceExtractor")

                        # 清理临时文件
                        share_db_path.unlink(missing_ok=True)

            # 清理模拟器中的临时文件
            self.task.ld.execute_ld(self.emulator_id, "rm -f /sdcard/temp_db.db")

            return secrets

        except Exception as e:
            log_error(f"[强制提取] 数据库文件分析失败: {e}", component="ForceExtractor")
            return []

    async def _search_memory_patterns(self, pid: str) -> List[Dict[str, Any]]:
        """搜索内存中的密钥模式"""
        try:
            secrets = []

            # 尝试从进程内存中搜索Base32模式
            # 注意：这需要root权限，且可能不稳定
            success, result = self.task.ld.execute_ld(self.emulator_id, f"cat /proc/{pid}/cmdline 2>/dev/null")
            if success:
                print(f"   进程命令行: {result.strip()}")

            # 尝试读取进程的环境变量
            success, result = self.task.ld.execute_ld(self.emulator_id, f"cat /proc/{pid}/environ 2>/dev/null | tr '\\0' '\\n' | grep -E '[A-Z2-7]{{16,}}'")
            if success and result.strip():
                env_keys = result.strip().split('\n')
                for key in env_keys:
                    if self._is_valid_base32_key(key.strip()):
                        secrets.append({
                            'secret_key': key.strip(),
                            'source': f'memory:environ:{pid}',
                            'method': 'memory_analysis'
                        })

            return secrets

        except Exception as e:
            log_error(f"[强制提取] 内存模式搜索失败: {e}", component="ForceExtractor")
            return []

    async def _analyze_network_log(self, log_file: str) -> List[Dict[str, Any]]:
        """分析网络日志文件"""
        try:
            secrets = []

            # 搜索日志文件中的Base32模式
            success, result = self.task.ld.execute_ld(self.emulator_id, f"strings '{log_file}' | grep -E '[A-Z2-7]{{16,}}' | head -10")
            if success and result.strip():
                potential_keys = result.strip().split('\n')
                for key in potential_keys:
                    if self._is_valid_base32_key(key.strip()):
                        secrets.append({
                            'secret_key': key.strip(),
                            'source': f'network_log:{log_file}',
                            'method': 'network_log_analysis'
                        })

            return secrets

        except Exception as e:
            log_error(f"[强制提取] 网络日志分析失败: {e}", component="ForceExtractor")
            return []

    def _is_valid_base32_key(self, key: str) -> bool:
        """验证是否为有效的Base32密钥"""
        try:
            if not key or len(key) < 16:
                return False

            # 清理密钥
            cleaned = re.sub(r'[^A-Z2-7]', '', key.upper())
            if len(cleaned) < 16:
                return False

            # 验证Base32字符集
            if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in cleaned):
                return False

            # 尝试解码
            try:
                base64.b32decode(cleaned)
                return True
            except Exception:
                return False

        except Exception:
            return False

    async def _process_extracted_secrets(self, all_secrets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理提取的密钥（去重和验证）"""
        try:
            print("🔍 处理提取的密钥...")

            # 去重
            unique_secrets = []
            seen_keys = set()

            for secret in all_secrets:
                key = secret.get('secret_key', '')
                if key and key not in seen_keys:
                    seen_keys.add(key)

                    # 验证密钥
                    if self._is_valid_base32_key(key):
                        # 生成测试TOTP验证码
                        try:
                            import hmac
                            import hashlib
                            import struct
                            import time

                            decoded_key = base64.b32decode(key)
                            timestamp = int(time.time()) // 30
                            msg = struct.pack('>Q', timestamp)
                            hmac_digest = hmac.new(decoded_key, msg, hashlib.sha1).digest()
                            offset = hmac_digest[-1] & 0x0f
                            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
                            code = (code & 0x7fffffff) % 1000000

                            secret['test_totp'] = f"{code:06d}"
                            secret['verified'] = True

                        except Exception:
                            secret['verified'] = False

                        unique_secrets.append(secret)

            print(f"✅ 处理完成: {len(unique_secrets)} 个有效密钥")
            return unique_secrets

        except Exception as e:
            log_error(f"[强制提取] 密钥处理失败: {e}", component="ForceExtractor")
            return []

    async def _save_extraction_results(self, secrets: List[Dict[str, Any]]):
        """保存提取结果"""
        try:
            if not secrets:
                return

            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存到JSON文件
            output_dir = Path("./fb_2fa_results/force_extraction/")
            output_dir.mkdir(parents=True, exist_ok=True)

            json_file = output_dir / f"force_extracted_secrets_{self.emulator_id}_{timestamp}.json"

            result_data = {
                'emulator_id': self.emulator_id,
                'extraction_time': datetime.datetime.now().isoformat(),
                'total_secrets': len(secrets),
                'secrets': secrets
            }

            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)

            # 保存到文本文件
            txt_file = output_dir / f"force_extracted_secrets_{self.emulator_id}_{timestamp}.txt"

            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(f"Facebook 2FA密钥强制提取结果\n")
                f.write(f"模拟器ID: {self.emulator_id}\n")
                f.write(f"提取时间: {result_data['extraction_time']}\n")
                f.write(f"总密钥数: {len(secrets)}\n")
                f.write("=" * 50 + "\n\n")

                for i, secret in enumerate(secrets, 1):
                    f.write(f"{i}. 密钥: {secret['secret_key']}\n")
                    f.write(f"   来源: {secret['source']}\n")
                    f.write(f"   方法: {secret['method']}\n")
                    if secret.get('verified'):
                        f.write(f"   测试验证码: {secret.get('test_totp', 'N/A')}\n")
                    f.write(f"   验证状态: {'✅ 有效' if secret.get('verified') else '❌ 无效'}\n")
                    f.write("-" * 30 + "\n")

            print(f"💾 结果已保存:")
            print(f"   📄 {json_file}")
            print(f"   📄 {txt_file}")

        except Exception as e:
            log_error(f"[强制提取] 保存结果失败: {e}", component="ForceExtractor")

    async def _display_results(self, secrets: List[Dict[str, Any]]):
        """显示提取结果"""
        try:
            print("\n" + "=" * 50)
            print("🎯 强制提取结果")
            print("=" * 50)

            if not secrets:
                print("❌ 未找到任何2FA密钥")
                print("\n💡 建议:")
                print("1. 确保Facebook应用已登录并启用了2FA")
                print("2. 尝试在Facebook应用中进行一些操作以触发2FA相关数据")
                print("3. 检查是否有其他认证器应用安装")
                return

            print(f"✅ 找到 {len(secrets)} 个可能的2FA密钥:")
            print()

            for i, secret in enumerate(secrets, 1):
                print(f"🔑 密钥 {i}:")
                print(f"   密钥: {secret['secret_key']}")
                print(f"   来源: {secret['source']}")
                print(f"   提取方法: {secret['method']}")

                if secret.get('verified'):
                    print(f"   ✅ 验证状态: 有效")
                    print(f"   🔢 当前验证码: {secret.get('test_totp', 'N/A')}")
                else:
                    print(f"   ❌ 验证状态: 无效或无法验证")

                print("-" * 30)

            print("\n💡 使用建议:")
            print("1. 使用验证状态为'有效'的密钥")
            print("2. 在认证器应用中手动添加这些密钥进行验证")
            print("3. 确认验证码与Facebook要求的验证码匹配")
            print("4. 建议更新Facebook的2FA设置以使用新的手机号码")

        except Exception as e:
            log_error(f"[强制提取] 显示结果失败: {e}", component="ForceExtractor")

    async def _cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir, ignore_errors=True)
        except Exception:
            pass

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2

        print("⚠️  Facebook 2FA密钥强制提取工具")
        print("⚠️  此工具仅用于合法的账户恢复目的")
        print("⚠️  请确保您有权限访问此Facebook账户")
        print()

        confirm = input("确认继续? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return

        # 创建强制提取器
        extractor = FacebookTwoFactorForceExtractor(emulator_id)

        # 运行强制提取
        await extractor.run_force_extraction()

        print("\n" + "=" * 50)
        print("✅ Facebook 2FA密钥强制提取完成")

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
