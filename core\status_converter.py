#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 统一UI更新和状态转换服务
========================================
功能描述: 统一管理所有UI更新、状态转换和显示逻辑，避免重复代码

模块结构:
1. 状态定义和枚举
2. 状态转换服务 (StatusConverter)
3. UI更新管理器 (UIUpdateManager)
4. 便捷接口函数

主要功能:
- 状态转换: 状态枚举、颜色映射、心跳符号转换
- UI更新: 表格行更新、批量更新、详细状态更新
- 数据格式化: 模拟器数据提取和表格格式化
- 统计计算: 运行状态统计

调用关系: 被UI层调用，替代分散的更新逻辑
========================================
"""

import re
from pathlib import Path
from typing import Dict, List, Any, Tuple
from PyQt6.QtGui import QColor


# ============================================================================
# 🎯 1. 状态定义和枚举
# ============================================================================
# 功能描述: 定义模拟器状态枚举，提供系统唯一的状态标准
# 调用关系: 被所有状态相关模块引用，作为状态定义的基础
# 注意事项: 状态值必须保持一致性，修改时需要同步更新所有引用
# ============================================================================

class EmulatorStatus:
    """🎯 模拟器状态枚举 - 系统唯一的状态定义"""
    QUEUED = "排队中"
    STARTING = "启动中"
    RUNNING = "运行中"
    STOPPED = "已停止"
    FAILED = "启动失败"
    CANCELLED = "已取消"
    ABNORMAL = "异常"
    UNKNOWN = "未知"

    @classmethod
    def get_all_statuses(cls):
        """获取所有可用状态"""
        return [
            cls.QUEUED, cls.STARTING, cls.RUNNING, cls.STOPPED,
            cls.FAILED, cls.CANCELLED, cls.ABNORMAL, cls.UNKNOWN
        ]


class HeartbeatStatus:
    """🎯 心跳状态枚举 - 心跳监控专用状态"""
    NORMAL = "活动正常"              # 正常状态
    SUSPECTED = "疑似异常"           # 疑似异常
    CONFIRMED_ABNORMAL = "确认异常"  # 确认异常
    RECOVERING = "恢复中"            # 恢复中

    @classmethod
    def get_all_statuses(cls):
        """获取所有心跳状态"""
        return [
            cls.NORMAL, cls.SUSPECTED, cls.CONFIRMED_ABNORMAL, cls.RECOVERING
        ]

# 🎯 保持向后兼容性的别名
TaskActivityStatus = HeartbeatStatus


class InstagramDMStatus:
    """
    Instagram私信任务状态枚举
    ========================================
    功能描述: 定义Instagram私信任务的所有可能状态和格式化方法
    状态类型:
    - DEFAULT: 默认状态（未开始任务）
    - QUEUED: 任务排队中（等待执行）
    - RUNNING: 任务执行中（显示进度）
    - COMPLETED: 任务完成（显示成功率）
    - ERROR: 任务异常（显示错误信息）
    ========================================
    """
    # 🎯 状态常量定义
    DEFAULT = "-"                                           # 默认状态：未开始任务
    QUEUED = "粉丝私信任务排队中"                              # 排队状态：等待执行
    RUNNING_TEMPLATE = "私信中（已发送{sent}/目标{target}）"    # 运行状态：显示进度
    COMPLETED_TEMPLATE = "已完成（成功{success}/目标{target}）" # 完成状态：显示成功率
    ERROR_TEMPLATE = "异常（{error_msg}）"                   # 错误状态：显示错误信息

    @classmethod
    def format_running(cls, sent: int, target: int) -> str:
        """格式化运行中状态 - 显示已发送数量和目标数量"""
        return cls.RUNNING_TEMPLATE.format(sent=sent, target=target)

    @classmethod
    def format_completed(cls, success: int, target: int) -> str:
        """格式化完成状态 - 显示成功数量和目标数量"""
        return cls.COMPLETED_TEMPLATE.format(success=success, target=target)

    @classmethod
    def format_error(cls, error_msg: str) -> str:
        """格式化错误状态 - 显示具体错误信息"""
        return cls.ERROR_TEMPLATE.format(error_msg=error_msg)


# ============================================================================
# 🎯 2. 状态转换服务
# ============================================================================
# 功能描述: 提供状态转换、颜色映射、心跳符号转换等核心服务
# 调用关系: 被UI更新管理器和便捷接口函数调用，提供状态转换能力
# 注意事项: 映射表修改需要确保UI显示一致性，颜色值需要考虑可读性
# ============================================================================

class StatusConverter:
    """🎯 统一的状态转换服务"""



    # 🎯 状态颜色映射表
    STATUS_COLOR_MAP = {
        EmulatorStatus.RUNNING: QColor("#4CAF50"),     # 绿色
        EmulatorStatus.STOPPED: QColor("#F44336"),     # 红色
        EmulatorStatus.STARTING: QColor("#FF9800"),    # 橙色
        EmulatorStatus.QUEUED: QColor("#2196F3"),      # 蓝色
        EmulatorStatus.FAILED: QColor("#E91E63"),      # 粉红色
        EmulatorStatus.CANCELLED: QColor("#9E9E9E"),   # 灰色
        EmulatorStatus.ABNORMAL: QColor("#FF5722"),    # 深橙色
        EmulatorStatus.UNKNOWN: QColor("#9E9E9E")      # 灰色
    }

    # 🎯 心跳状态颜色映射表
    HEARTBEAT_COLOR_MAP = {
        HeartbeatStatus.NORMAL: QColor("#4CAF50"),           # 绿色 - 活动正常
        HeartbeatStatus.SUSPECTED: QColor("#FF9800"),       # 橙色 - 疑似异常
        HeartbeatStatus.CONFIRMED_ABNORMAL: QColor("#F44336"), # 红色 - 确认异常
        HeartbeatStatus.RECOVERING: QColor("#2196F3")       # 蓝色 - 恢复中
    }

    # 🎯 保持向后兼容性的别名
    TASK_ACTIVITY_COLOR_MAP = HEARTBEAT_COLOR_MAP

    # 🎯 Instagram私信任务状态颜色映射表 - UI显示用
    INSTAGRAM_DM_COLOR_MAP = {
        InstagramDMStatus.DEFAULT: QColor("#999999"),        # 灰色 - 默认状态（未开始）
        "排队中": QColor("#FF9800"),                         # 橙色 - 排队中（等待执行）
        "私信中": QColor("#2196F3"),                         # 蓝色 - 执行中（正在发送）
        "已完成": QColor("#4CAF50"),                         # 绿色 - 完成（任务成功）
        "异常": QColor("#F44336"),                           # 红色 - 异常（任务失败）
    }



    @classmethod
    def get_status_color(cls, status: str) -> QColor:
        """获取状态对应的颜色"""
        return cls.STATUS_COLOR_MAP.get(status, cls.STATUS_COLOR_MAP[EmulatorStatus.UNKNOWN])

    @classmethod
    def get_heartbeat_color(cls, heartbeat_status: str) -> QColor:
        """获取心跳状态对应的颜色"""
        return cls.HEARTBEAT_COLOR_MAP.get(heartbeat_status, cls.HEARTBEAT_COLOR_MAP[HeartbeatStatus.NORMAL])

    @classmethod
    def get_task_activity_color(cls, activity_status: str) -> QColor:
        """获取心跳状态对应的颜色 - 向后兼容性别名"""
        return cls.get_heartbeat_color(activity_status)

    @classmethod
    def get_instagram_dm_status_color(cls, status: str) -> QColor:
        """
        获取Instagram任务状态对应的颜色（支持私信和关注任务）
        ========================================
        功能描述: 根据状态文本返回对应的UI显示颜色
        匹配规则: 使用关键词匹配，支持动态状态文本
        - 默认状态("-") → 灰色
        - 包含"排队中" → 橙色
        - 包含"私信中"或"关注中" → 蓝色
        - 包含"已完成" → 绿色
        - 包含"异常"或"失败" → 红色
        - 其他情况 → 默认灰色
        ========================================
        """
        # 🎯 状态关键词匹配 - 支持私信和关注任务的动态状态文本
        if status == InstagramDMStatus.DEFAULT:
            return cls.INSTAGRAM_DM_COLOR_MAP[InstagramDMStatus.DEFAULT]
        elif "排队中" in status:
            return cls.INSTAGRAM_DM_COLOR_MAP["排队中"]
        elif "私信中" in status or "关注中" in status:
            return cls.INSTAGRAM_DM_COLOR_MAP["私信中"]  # 蓝色 - 执行中状态
        elif "已完成" in status:
            return cls.INSTAGRAM_DM_COLOR_MAP["已完成"]
        elif "异常" in status or "失败" in status:
            return cls.INSTAGRAM_DM_COLOR_MAP["异常"]  # 红色 - 失败状态
        else:
            return cls.INSTAGRAM_DM_COLOR_MAP[InstagramDMStatus.DEFAULT]


# ============================================================================
# 🎯 3. UI更新管理器
# ============================================================================
# 功能描述: 统一管理所有UI更新逻辑，消除分散的重复代码
# 调用关系: 被主窗口和异步桥梁调用，替代原有分散的UI更新逻辑
# 注意事项: 表格模型操作需要线程安全，批量更新时要考虑性能优化
# ============================================================================

class UIUpdateManager:
    """🎯 统一UI更新管理器 - 集中所有UI更新逻辑"""

    def __init__(self, table_model=None):
        """初始化UI更新管理器"""
        self.table_model = table_model
        self.status_converter = StatusConverter

    # ------------------------------------------------------------------------
    # 🎯 3.1 数据提取和格式化
    # ------------------------------------------------------------------------
    # 功能描述: 统一数据提取逻辑，支持多种数据格式转换为表格显示格式
    # 调用关系: 被表格更新方法调用，处理模拟器数据的格式化和统计
    # 注意事项: 需要兼容dict和对象两种数据格式，确保列索引正确对应
    # ------------------------------------------------------------------------

    def extract_emulator_data(self, emulator) -> Dict[str, Any]:
        """统一数据提取逻辑 - 支持多种数据格式"""
        try:
            if hasattr(emulator, 'to_dict'):
                return emulator.to_dict()
            elif isinstance(emulator, dict):
                return emulator
            else:
                return {}
        except Exception:
            return {}

    def format_emulator_for_table(self, emulator_data: Dict[str, Any]) -> List[Any]:
        """统一表格行数据格式化"""
        # 获取模拟器状态
        emulator_status = emulator_data.get('status', EmulatorStatus.UNKNOWN)

        # 获取心跳状态 - 从心跳管理器获取
        heartbeat_status = self._get_heartbeat_status(emulator_data.get('id', 0))

        # 获取模拟器的运行日志 - 只有运行中的模拟器才显示日志
        emulator_id = emulator_data.get('id', emulator_data.get('index', 0))
        if emulator_status == EmulatorStatus.RUNNING:
            running_log = get_emulator_running_log(emulator_id)
        else:
            running_log = "-"  # 非运行状态不显示日志

        # 按列顺序构建行数据：选择、索引、名称、模拟器状态、顶层句柄、绑定句柄、PID、心跳状态、任务状态、运行日志、运行时长、启动时间、备注
        return [
            False,  # 0. 选择框
            emulator_id,  # 1. 索引
            emulator_data.get('name', f"雷电模拟器-{emulator_id}"),  # 2. 名称
            emulator_status,  # 3. 模拟器状态
            str(emulator_data.get('top_hwnd', -1)) if emulator_data.get('top_hwnd', -1) != -1 else "-",  # 4. 顶层句柄
            str(emulator_data.get('bind_hwnd', -1)) if emulator_data.get('bind_hwnd', -1) != -1 else "-",  # 5. 绑定句柄
            emulator_data.get('vbox_pid', -1),  # 6. PID（VBox进程PID）
            heartbeat_status,  # 7. 心跳状态
            "-",  # 8. 任务状态
            running_log,  # 9. 运行日志 - 从app.log读取
            "-",  # 10. 运行时长
            "-",  # 11. 启动时间
            "-"   # 12. 备注
        ]

    def _get_heartbeat_status(self, emulator_id: int) -> str:
        """获取模拟器的心跳状态"""
        try:
            from .heartbeat_manager import get_simple_heartbeat_manager
            heartbeat_manager = get_simple_heartbeat_manager()

            if emulator_id in heartbeat_manager.monitored_emulators:
                emulator_info = heartbeat_manager.monitored_emulators[emulator_id]
                return emulator_info.get('status', HeartbeatStatus.NORMAL)
            else:
                # 如果模拟器未被监控，返回默认状态
                return "-"
        except Exception:
            return "-"

    def calculate_statistics(self, emulator_list: List[Any]) -> Tuple[int, int]:
        """统一统计信息计算"""
        total_count = len(emulator_list)
        running_count = 0

        for emu in emulator_list:
            data = self.extract_emulator_data(emu)
            status = data.get('status')
            if status == EmulatorStatus.RUNNING:
                running_count += 1

        return running_count, total_count

    # ------------------------------------------------------------------------
    # 🎯 3.2 表格更新方法
    # ------------------------------------------------------------------------
    # 功能描述: 提供单行更新、详细更新、批量更新等多种表格更新方式
    # 调用关系: 被便捷接口函数和主窗口调用，执行具体的UI数据更新
    # 注意事项: 更新前需要检查数据变化，避免无效更新，批量操作要优化性能
    # ------------------------------------------------------------------------

    def update_table_row_status(self, emulator_id: int, new_status: str, additional_data: Dict[str, Any] = None) -> bool:
        """统一表格行状态更新"""
        if not self.table_model:
            return False

        row = self._find_emulator_row(emulator_id)
        if row == -1:
            return False

        # 获取显示状态和心跳状态
        display_status = new_status  # 业务层已返回中文状态

        # 🎯 特殊处理：停止状态的模拟器心跳状态应为"-"
        if new_status in [EmulatorStatus.STOPPED, EmulatorStatus.FAILED, EmulatorStatus.CANCELLED]:
            heartbeat_status = "-"  # 停止状态清空心跳状态
        else:
            heartbeat_status = self._get_heartbeat_status(emulator_id)

        # 检查是否需要更新
        old_status = self.table_model.emulators[row][3]  # 模拟器状态列
        if old_status == display_status and not additional_data:
            return False

        # 更新基本状态字段
        self.table_model.emulators[row][3] = display_status  # 3. 模拟器状态
        self.table_model.emulators[row][7] = heartbeat_status    # 7. 心跳状态

        # 更新附加数据字段
        if additional_data:
            self._update_additional_fields(row, additional_data)

        # 发送数据变化信号
        self._emit_data_changed(row, 3, 7)  # 从状态列到心跳状态列
        return True

    def update_table_row_detailed(self, emulator_id: int, new_status: str, detailed_data: Dict[str, Any]) -> bool:
        """统一详细状态更新"""
        # 先执行基本状态更新
        success = self.update_table_row_status(emulator_id, new_status, detailed_data)

        if success and detailed_data:
            row = self._find_emulator_row(emulator_id)
            if row != -1:
                # 更新详细信息特有字段
                self._update_detailed_fields(row, new_status, detailed_data)
                # 发送额外的更新信号
                self._emit_data_changed(row, 8, 11)  # 从任务状态到启动时间（移除ADB端口后）

        return success

    def update_instagram_task_status(self, emulator_id: int, task_status: str) -> bool:
        """专门更新Instagram任务状态（第8列）"""
        if not self.table_model:
            return False

        row = self._find_emulator_row(emulator_id)
        if row == -1:
            return False

        # 检查是否需要更新
        old_task_status = self.table_model.emulators[row][8]  # 任务状态列
        if old_task_status == task_status:
            return False

        # 更新任务状态字段
        self.table_model.emulators[row][8] = task_status  # 8. 任务状态

        # 发送数据变化信号
        self._emit_data_changed(row, 8, 8)  # 只更新任务状态列
        return True

    def batch_update_table(self, updates: List[Dict[str, Any]]) -> List[int]:
        """统一批量表格更新"""
        if not self.table_model:
            return []

        changed_rows = []

        for update in updates:
            emulator_id = update.get('id')
            new_status = update.get('status')
            additional_data = update.get('data', {})

            if emulator_id is not None and new_status:
                row = self._find_emulator_row(emulator_id)
                if row != -1:
                    # 获取显示状态和心跳状态
                    display_status = new_status

                    # 🎯 特殊处理：停止状态的模拟器心跳状态应为"-"
                    if new_status in [EmulatorStatus.STOPPED, EmulatorStatus.FAILED, EmulatorStatus.CANCELLED]:
                        heartbeat_status = "-"  # 停止状态清空心跳状态
                    else:
                        heartbeat_status = self._get_heartbeat_status(emulator_id)

                    # 检查是否需要更新
                    if self.table_model.emulators[row][3] != display_status:
                        self.table_model.emulators[row][3] = display_status
                        self.table_model.emulators[row][7] = heartbeat_status  # 7. 心跳状态

                        # 更新附加字段
                        if additional_data:
                            self._update_additional_fields(row, additional_data)

                        changed_rows.append(row)

        # 批量发送更新信号
        if changed_rows:
            self._emit_batch_data_changed(changed_rows)

        return changed_rows

    # ------------------------------------------------------------------------
    # 🎯 3.3 辅助方法
    # ------------------------------------------------------------------------
    # 功能描述: 提供行查找、字段更新、信号发送等底层辅助功能
    # 调用关系: 被表格更新方法内部调用，处理具体的数据操作和信号通知
    # 注意事项: 行查找需要处理模拟器不存在的情况，信号发送要确保线程安全
    # ------------------------------------------------------------------------

    def _find_emulator_row(self, emulator_id: int) -> int:
        """查找模拟器在表格中的行号"""
        if not self.table_model or not hasattr(self.table_model, 'emulators'):
            return -1

        for row, emulator_data in enumerate(self.table_model.emulators):
            if len(emulator_data) > 1 and emulator_data[1] == emulator_id:  # 1. 索引列
                return row
        return -1

    def _update_additional_fields(self, row: int, additional_data: Dict[str, Any]):
        """更新附加数据字段"""
        if 'top_hwnd' in additional_data:
            self.table_model.emulators[row][4] = str(additional_data['top_hwnd']) if additional_data['top_hwnd'] != -1 else "-"  # 4. 顶层句柄
        if 'bind_hwnd' in additional_data:
            self.table_model.emulators[row][5] = str(additional_data['bind_hwnd']) if additional_data['bind_hwnd'] != -1 else "-"  # 5. 绑定句柄
        if 'pid' in additional_data:
            self.table_model.emulators[row][6] = additional_data['pid']  # 6. PID
        # 端口字段已移除

    def _update_detailed_fields(self, row: int, new_status: str, detailed_data: Dict[str, Any]):
        """更新详细信息字段"""
        # 启动时间
        if 'startup_time' in detailed_data and detailed_data['startup_time']:
            self.table_model.emulators[row][11] = detailed_data['startup_time']  # 11. 启动时间（移除ADB端口后）

        # 🎯 运行日志字段 - 支持直接设置或自动获取
        if 'running_log' in detailed_data:
            # 直接设置运行日志（来自UI层的日志更新）
            self.table_model.emulators[row][9] = detailed_data['running_log']  # 9. 运行日志
        else:
            # 自动获取运行日志（原有逻辑）
            emulator_id = self.table_model.emulators[row][1]  # 获取模拟器ID（索引列）
            if new_status == EmulatorStatus.RUNNING:
                running_log = get_emulator_running_log(emulator_id)
            else:
                running_log = "-"  # 非运行状态不显示日志
            self.table_model.emulators[row][9] = running_log  # 9. 运行日志（移除ADB端口后）

        # 运行时长字段
        if new_status == EmulatorStatus.RUNNING:
            self.table_model.emulators[row][10] = "00:00:00"      # 10. 运行时长（移除ADB端口后）

    def _emit_data_changed(self, row: int, start_col: int, end_col: int):
        """发送数据变化信号"""
        if self.table_model and hasattr(self.table_model, 'index') and hasattr(self.table_model, 'dataChanged'):
            start_index = self.table_model.index(row, start_col)
            end_index = self.table_model.index(row, end_col)
            self.table_model.dataChanged.emit(start_index, end_index)

    def _emit_batch_data_changed(self, changed_rows: List[int]):
        """批量发送数据变化信号"""
        if not changed_rows or not self.table_model:
            return

        # 发送批量更新信号 - 优化性能
        if hasattr(self.table_model, 'beginResetModel') and hasattr(self.table_model, 'endResetModel'):
            self.table_model.beginResetModel()
            self.table_model.endResetModel()
        else:
            # 逐行发送信号
            for row in changed_rows:
                self._emit_data_changed(row, 3, 7)  # 从状态列到心跳状态列（移除ADB端口后）


# ============================================================================
# 🎯 4. 便捷接口函数
# ============================================================================
# 功能描述: 提供全局便捷接口，简化外部调用，隐藏内部实现复杂性
# 调用关系: 被主窗口、异步桥梁等外部模块调用，作为统一的UI更新入口
# 注意事项: 单例模式确保管理器唯一性，接口参数要保持向后兼容
# ============================================================================

# 全局UI更新管理器实例
_ui_update_manager = None


def get_ui_update_manager(table_model=None) -> UIUpdateManager:
    """获取UI更新管理器单例"""
    global _ui_update_manager
    if _ui_update_manager is None or (table_model and _ui_update_manager.table_model != table_model):
        _ui_update_manager = UIUpdateManager(table_model)
    return _ui_update_manager


# ------------------------------------------------------------------------
# 🎯 4.1 状态更新接口
# ------------------------------------------------------------------------
# 功能描述: 提供简化的状态更新接口，支持基本更新、详细更新、批量更新
# 调用关系: 被主窗口状态变化处理函数调用，简化UI更新操作
# 注意事项: 自动处理表格模型传递，确保更新操作的原子性
# ------------------------------------------------------------------------

def update_emulator_status(emulator_id: int, new_status: str, additional_data: Dict[str, Any] = None, table_model=None) -> bool:
    """便捷的状态更新接口"""
    manager = get_ui_update_manager(table_model)
    return manager.update_table_row_status(emulator_id, new_status, additional_data)


def update_emulator_detailed(emulator_id: int, new_status: str, detailed_data: Dict[str, Any], table_model=None) -> bool:
    """便捷的详细状态更新接口"""
    manager = get_ui_update_manager(table_model)
    return manager.update_table_row_detailed(emulator_id, new_status, detailed_data)


def batch_update_emulators(updates: List[Dict[str, Any]], table_model=None) -> List[int]:
    """便捷的批量更新接口"""
    manager = get_ui_update_manager(table_model)
    return manager.batch_update_table(updates)


# ------------------------------------------------------------------------
# 🎯 4.2 数据格式化接口
# ------------------------------------------------------------------------
# 功能描述: 提供数据格式化和统计计算的便捷接口
# 调用关系: 被模拟器扫描完成后的数据处理流程调用，格式化显示数据
# 注意事项: 格式化过程要保持数据完整性，统计计算要考虑性能优化
# ------------------------------------------------------------------------

def format_emulator_list_for_table(emulator_list: List[Any]) -> List[List[Any]]:
    """便捷的表格数据格式化接口"""
    manager = get_ui_update_manager()
    table_data = []

    for emulator in emulator_list:
        data = manager.extract_emulator_data(emulator)
        row_data = manager.format_emulator_for_table(data)
        table_data.append(row_data)

    return table_data


def get_emulator_statistics(emulator_list: List[Any]) -> Tuple[int, int]:
    """便捷的统计信息计算接口"""
    manager = get_ui_update_manager()
    return manager.calculate_statistics(emulator_list)


# ============================================================================
# 🎯 表格运行日志列功能 - 事件驱动更新
# ============================================================================
# 功能: 为表格"运行日志"列提供模拟器状态数据 (非下方系统日志面板)
# 更新: 事件驱动实时更新，替代定时器方案
# 区分: 表格列=单个模拟器状态 vs 下方面板=全局系统日志
# ============================================================================

class EmulatorLogReader:
    """表格运行日志列数据读取器 - 区别于下方系统日志面板"""

    def __init__(self, log_file_path: str = "logs/app.log"):
        self.log_file_path = Path(log_file_path)
        self.cache = {}  # 缓存最近读取的日志

    def get_latest_log_for_emulator(self, emulator_id: int, max_lines: int = 5) -> str:
        """获取模拟器日志 - 用于表格运行日志列显示"""
        try:
            if not self.log_file_path.exists():
                return "日志文件不存在"

            # 读取日志文件的最后几行
            with open(self.log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 从后往前查找与该模拟器相关的日志
            relevant_logs = []
            emulator_patterns = [
                rf'模拟器\s*{emulator_id}[^\d]',  # 模拟器1、模拟器 1
                rf'模拟器{emulator_id}[^\d]',    # 模拟器1
                rf'emulator_id:\s*{emulator_id}[^\d]',  # emulator_id: 1
                rf'emulator-{5554 + 2 * emulator_id}',  # emulator-5556 (ADB端口)
            ]

            # 从最新的日志开始查找
            for line in reversed(lines[-200:]):  # 只检查最近200行
                line = line.strip()
                if not line:
                    continue

                # 检查是否包含该模拟器的信息
                for pattern in emulator_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        # 提取时间戳和消息
                        log_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - .* - .* - (.+)', line)
                        if log_match:
                            timestamp = log_match.group(1)
                            message = log_match.group(2)
                            # 简化时间戳显示
                            time_part = timestamp.split(' ')[1]  # 只显示时间部分
                            relevant_logs.append(f"[{time_part}] {message}")
                        else:
                            relevant_logs.append(line)
                        break

                if len(relevant_logs) >= max_lines:
                    break

            if relevant_logs:
                # 返回最新的日志（因为是从后往前查找的，需要反转）
                return relevant_logs[0] if relevant_logs else "暂无相关日志"
            else:
                return "暂无相关日志"

        except Exception as e:
            return f"读取日志失败: {str(e)}"

    def get_emulator_status_from_log(self, emulator_id: int) -> str:
        """从日志获取模拟器状态 - 用于表格运行日志列"""
        try:
            latest_log = self.get_latest_log_for_emulator(emulator_id, max_lines=1)

            # 根据日志内容判断状态
            if "启动成功" in latest_log or "运行中" in latest_log:
                return "模拟器运行正常"
            elif "异常" in latest_log or "失败" in latest_log or "错误" in latest_log:
                return "检测到异常活动"
            elif "启动中" in latest_log or "启动" in latest_log:
                return "模拟器启动中"
            elif "停止" in latest_log or "关闭" in latest_log:
                return "模拟器已停止"
            else:
                return latest_log if latest_log != "暂无相关日志" else "-"

        except Exception as e:
            return f"状态检测失败: {str(e)}"


# 全局日志读取器实例
_log_reader = None


def get_log_reader() -> EmulatorLogReader:
    """获取全局日志读取器实例"""
    global _log_reader
    if _log_reader is None:
        _log_reader = EmulatorLogReader()
    return _log_reader


def get_emulator_running_log(emulator_id: int) -> str:
    """便捷函数：获取模拟器的运行日志"""
    reader = get_log_reader()
    return reader.get_emulator_status_from_log(emulator_id)


# ============================================================================
# 🎯 Instagram关注任务状态管理
# ============================================================================

class InstagramFollowStatus:
    """Instagram关注任务状态格式化器"""

    @staticmethod
    def format_in_progress(followed_count: int, total_count: int) -> str:
        """格式化进行中的状态"""
        return f"关注中 ({followed_count}/{total_count})"

    @staticmethod
    def format_completed(followed_count: int, total_count: int) -> str:
        """格式化完成状态"""
        return f"已完成 ({followed_count}/{total_count})"

    @staticmethod
    def format_failed(followed_count: int, total_count: int, error: str = "") -> str:
        """格式化失败状态"""
        error_info = f" - {error}" if error else ""
        return f"失败 ({followed_count}/{total_count}){error_info}"

    @staticmethod
    def format_cancelled(followed_count: int, total_count: int) -> str:
        """格式化取消状态"""
        return f"已取消 ({followed_count}/{total_count})"