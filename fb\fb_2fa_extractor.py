#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Facebook双因子认证密钥提取器 - 底层数据提取
========================================
功能描述: 从Android模拟器中提取Facebook 2FA密钥和相关认证信息
实现方式: 基于ADB命令和底层数据访问，支持多种提取方法

主要功能:
1. Facebook应用数据提取 - 从应用数据目录提取2FA密钥
2. 认证器应用数据提取 - 从Google Authenticator等应用提取
3. 系统数据库访问 - 直接访问Android系统数据库
4. 备份数据解析 - 解析应用备份文件中的2FA信息

技术实现:
- 使用ADB shell命令访问应用数据
- 解析SQLite数据库文件
- 处理加密的认证密钥
- 支持多种2FA格式（TOTP、HOTP）

调用关系: 被FB任务执行器调用，提供底层数据提取服务
注意事项: 需要root权限或系统级权限访问应用数据
========================================
"""

import os
import re
import json
import base64
import sqlite3
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

# 导入项目核心模块
import sys
sys.path.append(str(Path(__file__).parent.parent))
from core.logger_manager import log_info, log_error, log_warning
from core.leidianapi.LeiDian_Reorganized import Dnconsole

# ============================================================================
# 🎯 1. 数据结构定义
# ============================================================================

@dataclass
class TwoFactorSecret:
    """2FA密钥数据结构"""
    service_name: str  # 服务名称（如Facebook）
    account: str       # 账户名
    secret_key: str    # 密钥字符串
    issuer: str        # 发行者
    algorithm: str     # 算法（SHA1, SHA256等）
    digits: int        # 位数（通常6或8）
    period: int        # 周期（通常30秒）
    counter: int       # 计数器（HOTP使用）
    type: str          # 类型（TOTP或HOTP）

@dataclass
class ExtractionResult:
    """提取结果数据结构"""
    success: bool
    secrets: List[TwoFactorSecret]
    error_message: str
    extraction_method: str
    timestamp: str

# ============================================================================
# 🎯 2. Facebook 2FA密钥提取器
# ============================================================================

class FacebookTwoFactorExtractor:
    """🎯 Facebook双因子认证密钥提取器"""
    
    def __init__(self, emulator_id: int):
        """
        初始化提取器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id

        # 初始化雷电API - 使用默认路径
        default_base_path = "G:/leidian/LDPlayer9"
        default_share_path = "C:/Users/<USER>/Documents/leidian9"
        self.ld = Dnconsole(default_base_path, default_share_path, emulator_id=emulator_id)

        self.temp_dir = Path(tempfile.gettempdir()) / f"fb_2fa_extract_{emulator_id}"
        self.temp_dir.mkdir(exist_ok=True)
        
        # Facebook相关包名
        self.facebook_packages = [
            "com.facebook.katana",           # Facebook主应用
            "com.facebook.orca",             # Messenger
            "com.facebook.mlite",            # Facebook Lite
        ]
        
        # 认证器应用包名
        self.authenticator_packages = [
            "com.google.android.apps.authenticator2",  # Google Authenticator
            "com.microsoft.msa.authenticator",         # Microsoft Authenticator
            "com.authy.authy",                         # Authy
            "org.fedorahosted.freeotp",               # FreeOTP
        ]
        
        log_info(f"[模拟器{emulator_id}] Facebook 2FA提取器初始化完成", component="FacebookTwoFactorExtractor")

    # ------------------------------------------------------------------------
    # 🎯 3. 主要提取方法
    # ------------------------------------------------------------------------

    def extract_2fa_secrets(self) -> ExtractionResult:
        """
        提取2FA密钥的主入口方法
        
        Returns:
            ExtractionResult: 提取结果
        """
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始提取Facebook 2FA密钥", component="FacebookTwoFactorExtractor")
            
            all_secrets = []
            extraction_methods = []
            
            # 方法1: 从Facebook应用数据提取
            fb_secrets = self._extract_from_facebook_app()
            if fb_secrets:
                all_secrets.extend(fb_secrets)
                extraction_methods.append("facebook_app")
                log_info(f"[模拟器{self.emulator_id}] 从Facebook应用提取到{len(fb_secrets)}个密钥", component="FacebookTwoFactorExtractor")
            
            # 方法2: 从认证器应用提取
            auth_secrets = self._extract_from_authenticator_apps()
            if auth_secrets:
                all_secrets.extend(auth_secrets)
                extraction_methods.append("authenticator_apps")
                log_info(f"[模拟器{self.emulator_id}] 从认证器应用提取到{len(auth_secrets)}个密钥", component="FacebookTwoFactorExtractor")
            
            # 方法3: 从系统数据库提取
            system_secrets = self._extract_from_system_database()
            if system_secrets:
                all_secrets.extend(system_secrets)
                extraction_methods.append("system_database")
                log_info(f"[模拟器{self.emulator_id}] 从系统数据库提取到{len(system_secrets)}个密钥", component="FacebookTwoFactorExtractor")
            
            # 去重处理
            unique_secrets = self._deduplicate_secrets(all_secrets)
            
            import datetime
            result = ExtractionResult(
                success=len(unique_secrets) > 0,
                secrets=unique_secrets,
                error_message="" if unique_secrets else "未找到任何2FA密钥",
                extraction_method="+".join(extraction_methods),
                timestamp=datetime.datetime.now().isoformat()
            )
            
            log_info(f"[模拟器{self.emulator_id}] 2FA密钥提取完成，共找到{len(unique_secrets)}个唯一密钥", component="FacebookTwoFactorExtractor")
            return result
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 2FA密钥提取失败: {e}", component="FacebookTwoFactorExtractor")
            import datetime
            return ExtractionResult(
                success=False,
                secrets=[],
                error_message=str(e),
                extraction_method="error",
                timestamp=datetime.datetime.now().isoformat()
            )

    def _extract_from_facebook_app(self) -> List[TwoFactorSecret]:
        """从Facebook应用数据目录提取2FA密钥"""
        try:
            secrets = []
            
            for package in self.facebook_packages:
                # 检查应用是否安装
                if not self._is_app_installed(package):
                    continue
                
                log_info(f"[模拟器{self.emulator_id}] 正在从{package}提取2FA数据", component="FacebookTwoFactorExtractor")
                
                # 尝试提取应用数据
                app_secrets = self._extract_from_app_data(package)
                if app_secrets:
                    secrets.extend(app_secrets)
            
            return secrets
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从Facebook应用提取失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    def _extract_from_authenticator_apps(self) -> List[TwoFactorSecret]:
        """从认证器应用提取2FA密钥"""
        try:
            secrets = []
            
            for package in self.authenticator_packages:
                if not self._is_app_installed(package):
                    continue
                
                log_info(f"[模拟器{self.emulator_id}] 正在从认证器应用{package}提取数据", component="FacebookTwoFactorExtractor")
                
                # 根据不同的认证器应用使用不同的提取方法
                if "authenticator2" in package:  # Google Authenticator
                    app_secrets = self._extract_from_google_authenticator()
                elif "authy" in package:  # Authy
                    app_secrets = self._extract_from_authy()
                else:
                    app_secrets = self._extract_from_app_data(package)
                
                if app_secrets:
                    secrets.extend(app_secrets)
            
            return secrets
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从认证器应用提取失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    def _extract_from_system_database(self) -> List[TwoFactorSecret]:
        """从Android系统数据库提取2FA信息"""
        try:
            secrets = []
            
            # 尝试访问系统级数据库
            system_db_paths = [
                "/data/system/accounts.db",
                "/data/data/com.android.providers.settings/databases/settings.db",
                "/data/system/users/0/accounts.db"
            ]
            
            for db_path in system_db_paths:
                try:
                    db_secrets = self._extract_from_database_file(db_path)
                    if db_secrets:
                        secrets.extend(db_secrets)
                except Exception as e:
                    log_warning(f"[模拟器{self.emulator_id}] 无法访问系统数据库{db_path}: {e}", component="FacebookTwoFactorExtractor")
                    continue
            
            return secrets
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从系统数据库提取失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    # ------------------------------------------------------------------------
    # 🎯 4. 辅助方法
    # ------------------------------------------------------------------------

    def _is_app_installed(self, package_name: str) -> bool:
        """检查应用是否已安装"""
        try:
            result = self.ld.adb(self.emulator_id, f"pm list packages | grep {package_name}")
            return package_name in result
        except Exception:
            return False

    def _extract_from_app_data(self, package_name: str) -> List[TwoFactorSecret]:
        """从应用数据目录提取2FA密钥"""
        try:
            secrets = []
            
            # 应用数据目录路径
            app_data_paths = [
                f"/data/data/{package_name}/databases/",
                f"/data/data/{package_name}/shared_prefs/",
                f"/data/data/{package_name}/files/",
            ]
            
            for data_path in app_data_paths:
                # 列出目录内容
                result = self.ld.adb(self.emulator_id, f"ls -la {data_path}")
                if "No such file" in result or not result.strip():
                    continue
                
                # 查找可能包含2FA数据的文件
                files = self._parse_file_list(result)
                for file_info in files:
                    if self._is_potential_2fa_file(file_info['name']):
                        file_secrets = self._extract_from_file(f"{data_path}/{file_info['name']}")
                        if file_secrets:
                            secrets.extend(file_secrets)
            
            return secrets
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从应用数据{package_name}提取失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    def _parse_file_list(self, ls_output: str) -> List[Dict[str, str]]:
        """解析ls命令输出"""
        files = []
        for line in ls_output.strip().split('\n'):
            if line.strip() and not line.startswith('total'):
                parts = line.split()
                if len(parts) >= 9:
                    files.append({
                        'name': parts[-1],
                        'size': parts[4] if len(parts) > 4 else '0',
                        'permissions': parts[0] if len(parts) > 0 else ''
                    })
        return files

    def _is_potential_2fa_file(self, filename: str) -> bool:
        """判断文件是否可能包含2FA数据"""
        potential_patterns = [
            r'.*auth.*\.db$',
            r'.*2fa.*\.db$',
            r'.*totp.*\.db$',
            r'.*secret.*\.db$',
            r'.*account.*\.db$',
            r'.*\.xml$',
            r'.*\.json$',
        ]
        
        for pattern in potential_patterns:
            if re.match(pattern, filename.lower()):
                return True
        return False

    def _extract_from_file(self, file_path: str) -> List[TwoFactorSecret]:
        """从具体文件提取2FA密钥"""
        try:
            # 首先尝试读取文件内容
            content = self.ld.adb(self.emulator_id, f"cat {file_path}")
            if not content or "No such file" in content:
                return []
            
            secrets = []
            
            # 根据文件类型选择解析方法
            if file_path.endswith('.db'):
                secrets = self._parse_database_content(content, file_path)
            elif file_path.endswith('.xml'):
                secrets = self._parse_xml_content(content)
            elif file_path.endswith('.json'):
                secrets = self._parse_json_content(content)
            else:
                secrets = self._parse_text_content(content)
            
            return secrets
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从文件{file_path}提取失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    def _parse_database_content(self, content: str, file_path: str) -> List[TwoFactorSecret]:
        """解析数据库文件内容"""
        try:
            # 将数据库文件复制到本地临时目录
            local_db_path = self.temp_dir / f"temp_{os.path.basename(file_path)}"
            
            # 使用base64传输数据库文件
            base64_content = self.ld.adb(self.emulator_id, f"base64 {file_path}")
            if base64_content and "No such file" not in base64_content:
                try:
                    decoded_content = base64.b64decode(base64_content)
                    with open(local_db_path, 'wb') as f:
                        f.write(decoded_content)
                    
                    # 使用SQLite解析数据库
                    return self._extract_from_sqlite_db(str(local_db_path))
                except Exception as e:
                    log_warning(f"[模拟器{self.emulator_id}] Base64解码失败: {e}", component="FacebookTwoFactorExtractor")
            
            return []
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 解析数据库内容失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    def _extract_from_sqlite_db(self, db_path: str) -> List[TwoFactorSecret]:
        """从SQLite数据库提取2FA密钥"""
        try:
            secrets = []
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 获取所有表名
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                
                for table in tables:
                    table_name = table[0]
                    try:
                        # 获取表结构
                        cursor.execute(f"PRAGMA table_info({table_name});")
                        columns = cursor.fetchall()
                        column_names = [col[1] for col in columns]
                        
                        # 查找可能包含2FA数据的列
                        potential_columns = []
                        for col in column_names:
                            if any(keyword in col.lower() for keyword in ['secret', 'key', 'token', 'auth', '2fa', 'totp']):
                                potential_columns.append(col)
                        
                        if potential_columns:
                            # 查询数据
                            cursor.execute(f"SELECT * FROM {table_name};")
                            rows = cursor.fetchall()
                            
                            for row in rows:
                                row_dict = dict(zip(column_names, row))
                                secret = self._extract_secret_from_row(row_dict, table_name)
                                if secret:
                                    secrets.append(secret)
                    
                    except Exception as e:
                        log_warning(f"[模拟器{self.emulator_id}] 处理表{table_name}失败: {e}", component="FacebookTwoFactorExtractor")
                        continue
            
            return secrets
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] SQLite数据库解析失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    def _extract_secret_from_row(self, row_dict: Dict[str, Any], table_name: str) -> Optional[TwoFactorSecret]:
        """从数据库行中提取2FA密钥"""
        try:
            # 查找可能的密钥字段
            secret_key = None
            service_name = "Unknown"
            account = "Unknown"

            # 常见的密钥字段名
            secret_fields = ['secret', 'key', 'token', 'auth_key', 'totp_secret', 'seed']
            service_fields = ['service', 'issuer', 'provider', 'name', 'label']
            account_fields = ['account', 'user', 'email', 'username']

            for field, value in row_dict.items():
                if not value:
                    continue

                field_lower = field.lower()

                # 查找密钥
                if any(sf in field_lower for sf in secret_fields):
                    if isinstance(value, str) and len(value) > 10:
                        secret_key = value

                # 查找服务名
                if any(sf in field_lower for sf in service_fields):
                    if isinstance(value, str):
                        service_name = value

                # 查找账户名
                if any(af in field_lower for af in account_fields):
                    if isinstance(value, str):
                        account = value

            # 如果找到密钥，创建TwoFactorSecret对象
            if secret_key:
                # 清理密钥格式
                clean_secret = self._clean_secret_key(secret_key)
                if clean_secret:
                    return TwoFactorSecret(
                        service_name=service_name,
                        account=account,
                        secret_key=clean_secret,
                        issuer=service_name,
                        algorithm="SHA1",  # 默认算法
                        digits=6,          # 默认位数
                        period=30,         # 默认周期
                        counter=0,         # 默认计数器
                        type="TOTP"        # 默认类型
                    )

            return None

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从数据库行提取密钥失败: {e}", component="FacebookTwoFactorExtractor")
            return None

    def _clean_secret_key(self, secret_key: str) -> str:
        """清理和验证密钥格式"""
        try:
            # 移除空格和特殊字符
            cleaned = re.sub(r'[^A-Z2-7]', '', secret_key.upper())

            # 验证Base32格式
            if len(cleaned) >= 16 and all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in cleaned):
                return cleaned

            # 尝试解码Base64
            try:
                decoded = base64.b64decode(secret_key)
                encoded = base64.b32encode(decoded).decode('ascii')
                return encoded
            except:
                pass

            return ""

        except Exception:
            return ""

    def _extract_from_google_authenticator(self) -> List[TwoFactorSecret]:
        """从Google Authenticator提取2FA密钥"""
        try:
            secrets = []
            package = "com.google.android.apps.authenticator2"

            # Google Authenticator的数据库路径
            db_paths = [
                f"/data/data/{package}/databases/accounts.db",
                f"/data/data/{package}/databases/authenticator.db",
            ]

            for db_path in db_paths:
                try:
                    # 尝试直接访问数据库
                    result = self.ld.adb(self.emulator_id, f"ls -la {db_path}")
                    if "No such file" not in result:
                        db_secrets = self._extract_from_database_file(db_path)
                        if db_secrets:
                            secrets.extend(db_secrets)
                except Exception as e:
                    log_warning(f"[模拟器{self.emulator_id}] 无法访问Google Authenticator数据库{db_path}: {e}", component="FacebookTwoFactorExtractor")
                    continue

            return secrets

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从Google Authenticator提取失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    def _extract_from_authy(self) -> List[TwoFactorSecret]:
        """从Authy提取2FA密钥"""
        try:
            secrets = []
            package = "com.authy.authy"

            # Authy的数据存储路径
            data_paths = [
                f"/data/data/{package}/databases/",
                f"/data/data/{package}/shared_prefs/",
                f"/data/data/{package}/files/",
            ]

            for data_path in data_paths:
                try:
                    result = self.ld.adb(self.emulator_id, f"ls -la {data_path}")
                    if "No such file" not in result:
                        files = self._parse_file_list(result)
                        for file_info in files:
                            if any(keyword in file_info['name'].lower() for keyword in ['auth', 'token', 'account']):
                                file_secrets = self._extract_from_file(f"{data_path}/{file_info['name']}")
                                if file_secrets:
                                    secrets.extend(file_secrets)
                except Exception as e:
                    log_warning(f"[模拟器{self.emulator_id}] 无法访问Authy数据路径{data_path}: {e}", component="FacebookTwoFactorExtractor")
                    continue

            return secrets

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从Authy提取失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    def _extract_from_database_file(self, db_path: str) -> List[TwoFactorSecret]:
        """从数据库文件提取2FA密钥"""
        try:
            # 将数据库文件复制到本地
            local_db_path = self.temp_dir / f"temp_{os.path.basename(db_path)}"

            # 使用base64传输
            base64_content = self.ld.adb(self.emulator_id, f"base64 {db_path}")
            if not base64_content or "No such file" in base64_content:
                return []

            try:
                decoded_content = base64.b64decode(base64_content)
                with open(local_db_path, 'wb') as f:
                    f.write(decoded_content)

                return self._extract_from_sqlite_db(str(local_db_path))
            except Exception as e:
                log_warning(f"[模拟器{self.emulator_id}] Base64解码失败: {e}", component="FacebookTwoFactorExtractor")
                return []

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 从数据库文件提取失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    def _parse_xml_content(self, content: str) -> List[TwoFactorSecret]:
        """解析XML文件内容"""
        try:
            secrets = []

            # 查找XML中的2FA相关数据
            patterns = [
                r'<string name="[^"]*secret[^"]*">([^<]+)</string>',
                r'<string name="[^"]*key[^"]*">([^<]+)</string>',
                r'<string name="[^"]*token[^"]*">([^<]+)</string>',
            ]

            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    clean_secret = self._clean_secret_key(match)
                    if clean_secret:
                        secrets.append(TwoFactorSecret(
                            service_name="Facebook",
                            account="Unknown",
                            secret_key=clean_secret,
                            issuer="Facebook",
                            algorithm="SHA1",
                            digits=6,
                            period=30,
                            counter=0,
                            type="TOTP"
                        ))

            return secrets

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 解析XML内容失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    def _parse_json_content(self, content: str) -> List[TwoFactorSecret]:
        """解析JSON文件内容"""
        try:
            secrets = []

            try:
                data = json.loads(content)
            except json.JSONDecodeError:
                return []

            # 递归搜索JSON中的2FA数据
            def search_json(obj, path=""):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        if any(keyword in key.lower() for keyword in ['secret', 'key', 'token', 'auth']):
                            if isinstance(value, str):
                                clean_secret = self._clean_secret_key(value)
                                if clean_secret:
                                    secrets.append(TwoFactorSecret(
                                        service_name="Facebook",
                                        account=path or "Unknown",
                                        secret_key=clean_secret,
                                        issuer="Facebook",
                                        algorithm="SHA1",
                                        digits=6,
                                        period=30,
                                        counter=0,
                                        type="TOTP"
                                    ))
                        else:
                            search_json(value, f"{path}.{key}" if path else key)
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        search_json(item, f"{path}[{i}]" if path else f"[{i}]")

            search_json(data)
            return secrets

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 解析JSON内容失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    def _parse_text_content(self, content: str) -> List[TwoFactorSecret]:
        """解析纯文本内容"""
        try:
            secrets = []

            # 查找可能的密钥模式
            patterns = [
                r'[A-Z2-7]{32,}',  # Base32编码的密钥
                r'otpauth://totp/[^\s]+',  # OTP URI
                r'secret[=:]\s*([A-Z2-7]{16,})',  # secret=KEY格式
            ]

            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    if pattern.startswith('otpauth://'):
                        # 解析OTP URI
                        secret = self._parse_otpauth_uri(match)
                        if secret:
                            secrets.append(secret)
                    else:
                        clean_secret = self._clean_secret_key(match)
                        if clean_secret:
                            secrets.append(TwoFactorSecret(
                                service_name="Facebook",
                                account="Unknown",
                                secret_key=clean_secret,
                                issuer="Facebook",
                                algorithm="SHA1",
                                digits=6,
                                period=30,
                                counter=0,
                                type="TOTP"
                            ))

            return secrets

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 解析文本内容失败: {e}", component="FacebookTwoFactorExtractor")
            return []

    def _parse_otpauth_uri(self, uri: str) -> Optional[TwoFactorSecret]:
        """解析OTP URI格式"""
        try:
            from urllib.parse import urlparse, parse_qs

            parsed = urlparse(uri)
            if parsed.scheme != 'otpauth':
                return None

            # 提取参数
            params = parse_qs(parsed.query)
            path_parts = parsed.path.lstrip('/').split(':', 1)

            service_name = path_parts[0] if len(path_parts) > 0 else "Unknown"
            account = path_parts[1] if len(path_parts) > 1 else "Unknown"

            secret_key = params.get('secret', [''])[0]
            issuer = params.get('issuer', [service_name])[0]
            algorithm = params.get('algorithm', ['SHA1'])[0]
            digits = int(params.get('digits', ['6'])[0])
            period = int(params.get('period', ['30'])[0])
            counter = int(params.get('counter', ['0'])[0])

            if secret_key:
                return TwoFactorSecret(
                    service_name=service_name,
                    account=account,
                    secret_key=secret_key,
                    issuer=issuer,
                    algorithm=algorithm,
                    digits=digits,
                    period=period,
                    counter=counter,
                    type="HOTP" if counter > 0 else "TOTP"
                )

            return None

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 解析OTP URI失败: {e}", component="FacebookTwoFactorExtractor")
            return None

    def _deduplicate_secrets(self, secrets: List[TwoFactorSecret]) -> List[TwoFactorSecret]:
        """去重处理"""
        try:
            seen = set()
            unique_secrets = []

            for secret in secrets:
                # 使用密钥和服务名作为唯一标识
                key = f"{secret.service_name}:{secret.account}:{secret.secret_key}"
                if key not in seen:
                    seen.add(key)
                    unique_secrets.append(secret)

            return unique_secrets

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 去重处理失败: {e}", component="FacebookTwoFactorExtractor")
            return secrets

    def cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                log_info(f"[模拟器{self.emulator_id}] 临时文件清理完成", component="FacebookTwoFactorExtractor")
        except Exception as e:
            log_warning(f"[模拟器{self.emulator_id}] 清理临时文件失败: {e}", component="FacebookTwoFactorExtractor")
