#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 TOTP密钥验证工具
========================================
功能描述: 验证TOTP密钥的有效性和真实性

核心功能:
1. 验证Base32密钥格式
2. 生成当前TOTP验证码
3. 分析密钥特征
4. 判断密钥真实性

使用方法:
python fb/verify_totp_key.py

注意事项:
- 用于验证TOTP密钥的有效性
- 可以生成测试验证码
========================================
"""

import base64
import hmac
import hashlib
import struct
import time
import re
from datetime import datetime

class TOTPKeyVerifier:
    """TOTP密钥验证器"""
    
    def __init__(self):
        """初始化验证器"""
        pass
    
    def verify_key(self, secret_key: str) -> dict:
        """验证TOTP密钥"""
        result = {
            'key': secret_key,
            'is_valid_format': False,
            'is_decodable': False,
            'key_length': len(secret_key),
            'decoded_length': 0,
            'current_totp': None,
            'next_totp': None,
            'analysis': {},
            'authenticity_score': 0
        }
        
        try:
            # 1. 格式验证
            result['is_valid_format'] = self._validate_format(secret_key)
            
            # 2. 解码验证
            if result['is_valid_format']:
                try:
                    decoded = base64.b32decode(secret_key)
                    result['is_decodable'] = True
                    result['decoded_length'] = len(decoded)
                    
                    # 3. 生成TOTP验证码
                    result['current_totp'] = self._generate_totp(secret_key)
                    result['next_totp'] = self._generate_totp(secret_key, time_offset=30)
                    
                except Exception as e:
                    result['decode_error'] = str(e)
            
            # 4. 密钥分析
            result['analysis'] = self._analyze_key(secret_key)
            
            # 5. 真实性评分
            result['authenticity_score'] = self._calculate_authenticity_score(secret_key, result)
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def _validate_format(self, key: str) -> bool:
        """验证Base32格式"""
        try:
            # 检查长度
            if len(key) < 16 or len(key) > 64:
                return False
            
            # 检查字符集 (Base32: A-Z, 2-7)
            valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
            if not set(key.upper()).issubset(valid_chars):
                return False
            
            return True
        except:
            return False
    
    def _generate_totp(self, secret: str, time_offset: int = 0) -> str:
        """生成TOTP验证码"""
        try:
            # 解码Base32密钥
            key = base64.b32decode(secret)
            
            # 获取时间戳
            timestamp = int(time.time() + time_offset) // 30
            
            # 生成TOTP
            msg = struct.pack('>Q', timestamp)
            hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
            offset = hmac_digest[-1] & 0x0f
            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
            code = (code & 0x7fffffff) % 1000000
            
            return f"{code:06d}"
        except:
            return "000000"
    
    def _analyze_key(self, key: str) -> dict:
        """分析密钥特征"""
        analysis = {
            'character_frequency': {},
            'pattern_analysis': {},
            'entropy_estimate': 0,
            'suspicious_patterns': []
        }
        
        try:
            # 字符频率分析
            from collections import Counter
            char_counts = Counter(key)
            analysis['character_frequency'] = dict(char_counts)
            
            # 模式分析
            analysis['pattern_analysis'] = {
                'repeated_chars': self._find_repeated_chars(key),
                'sequential_chars': self._find_sequential_chars(key),
                'common_patterns': self._find_common_patterns(key)
            }
            
            # 熵值估算
            analysis['entropy_estimate'] = self._estimate_entropy(key)
            
            # 可疑模式检测
            analysis['suspicious_patterns'] = self._detect_suspicious_patterns(key)
            
        except Exception as e:
            analysis['error'] = str(e)
        
        return analysis
    
    def _find_repeated_chars(self, key: str) -> list:
        """查找重复字符"""
        repeated = []
        for i in range(len(key) - 1):
            if key[i] == key[i + 1]:
                repeated.append(f"{key[i]} at position {i}-{i+1}")
        return repeated
    
    def _find_sequential_chars(self, key: str) -> list:
        """查找连续字符"""
        sequential = []
        alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'
        
        for i in range(len(key) - 2):
            char1, char2, char3 = key[i], key[i+1], key[i+2]
            if char1 in alphabet and char2 in alphabet and char3 in alphabet:
                idx1, idx2, idx3 = alphabet.index(char1), alphabet.index(char2), alphabet.index(char3)
                if idx2 == idx1 + 1 and idx3 == idx2 + 1:
                    sequential.append(f"{char1}{char2}{char3} at position {i}")
        
        return sequential
    
    def _find_common_patterns(self, key: str) -> list:
        """查找常见模式"""
        patterns = []
        
        # 检查常见单词
        common_words = ['FACEBOOK', 'GOOGLE', 'TWITTER', 'INSTAGRAM', 'TEST', 'DEMO']
        for word in common_words:
            if word in key:
                patterns.append(f"Contains word: {word}")
        
        # 检查重复子串
        for length in [2, 3, 4]:
            for i in range(len(key) - length * 2 + 1):
                substr = key[i:i+length]
                if substr in key[i+length:]:
                    patterns.append(f"Repeated substring: {substr}")
                    break
        
        return patterns
    
    def _estimate_entropy(self, key: str) -> float:
        """估算熵值"""
        try:
            from collections import Counter
            import math
            
            char_counts = Counter(key)
            key_len = len(key)
            
            entropy = 0
            for count in char_counts.values():
                p = count / key_len
                if p > 0:
                    entropy -= p * math.log2(p)
            
            return entropy
        except:
            return 0
    
    def _detect_suspicious_patterns(self, key: str) -> list:
        """检测可疑模式"""
        suspicious = []
        
        # 检查是否过于规律
        if len(set(key)) < len(key) * 0.5:
            suspicious.append("Low character diversity")
        
        # 检查是否有明显的人工模式
        if any(char * 3 in key for char in set(key)):
            suspicious.append("Contains triple repeated characters")
        
        # 检查是否符合真实TOTP密钥的特征
        if len(key) not in [16, 20, 24, 26, 32]:
            suspicious.append("Unusual key length for TOTP")
        
        return suspicious
    
    def _calculate_authenticity_score(self, key: str, result: dict) -> int:
        """计算真实性评分 (0-100)"""
        score = 0
        
        # 基础格式分 (30分)
        if result['is_valid_format']:
            score += 20
        if result['is_decodable']:
            score += 10
        
        # 长度分 (20分)
        if len(key) in [26, 32]:  # 常见的TOTP密钥长度
            score += 20
        elif len(key) in [16, 20, 24]:
            score += 15
        
        # 熵值分 (25分)
        entropy = result['analysis'].get('entropy_estimate', 0)
        if entropy > 4.5:
            score += 25
        elif entropy > 4.0:
            score += 20
        elif entropy > 3.5:
            score += 15
        
        # 模式分 (25分)
        suspicious_count = len(result['analysis'].get('suspicious_patterns', []))
        if suspicious_count == 0:
            score += 25
        elif suspicious_count == 1:
            score += 15
        elif suspicious_count == 2:
            score += 10
        
        return min(score, 100)
    
    def print_verification_result(self, result: dict):
        """打印验证结果"""
        print("🔍 TOTP密钥验证结果")
        print("=" * 50)
        print(f"🔑 密钥: {result['key']}")
        print(f"📏 长度: {result['key_length']} 字符")
        print(f"✅ 格式有效: {'是' if result['is_valid_format'] else '否'}")
        print(f"🔓 可解码: {'是' if result['is_decodable'] else '否'}")
        
        if result['is_decodable']:
            print(f"📊 解码长度: {result['decoded_length']} 字节")
            print(f"🔢 当前验证码: {result['current_totp']}")
            print(f"⏭️  下个验证码: {result['next_totp']}")
        
        print(f"🎯 真实性评分: {result['authenticity_score']}/100")
        
        # 分析结果
        analysis = result.get('analysis', {})
        if analysis:
            print("\n📊 密钥分析:")
            print(f"   熵值: {analysis.get('entropy_estimate', 0):.2f}")
            
            suspicious = analysis.get('suspicious_patterns', [])
            if suspicious:
                print("   ⚠️  可疑模式:")
                for pattern in suspicious:
                    print(f"      - {pattern}")
            else:
                print("   ✅ 未发现可疑模式")
        
        # 真实性判断
        print(f"\n🎯 真实性判断:")
        score = result['authenticity_score']
        if score >= 80:
            print("   ✅ 高度可能是真实的TOTP密钥")
        elif score >= 60:
            print("   ⚠️  可能是真实的TOTP密钥")
        elif score >= 40:
            print("   ❓ 真实性存疑")
        else:
            print("   ❌ 很可能不是真实的TOTP密钥")

def main():
    """主函数"""
    print("🔍 TOTP密钥验证工具")
    print("=" * 40)
    
    # 验证用户提供的密钥
    test_key = "XZJKVEGNFN6S2C2Z7LTDRW5TUZYH4WPU"
    
    print(f"正在验证密钥: {test_key}")
    print()
    
    verifier = TOTPKeyVerifier()
    result = verifier.verify_key(test_key)
    verifier.print_verification_result(result)
    
    print("\n" + "=" * 50)
    print("💡 如果这个密钥的真实性评分较低，")
    print("   说明它可能不是您Facebook账户的真实TOTP密钥。")
    print("   我们需要继续寻找真实的密钥。")

if __name__ == "__main__":
    main()
