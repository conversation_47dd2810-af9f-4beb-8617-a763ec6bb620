#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Instagram关注功能配置界面模块
========================================
功能描述: Instagram关注任务的配置界面，复用instagram_dm_ui.py的结构和样式
主要方法: __init__(), init_ui(), setup_connections(), load_settings()
调用关系: 被任务管理页面调用，通过事件总线与业务层通信
注意事项:
- UI层只负责界面展示，业务逻辑通过事件总线处理
- 完全复用instagram_dm_ui.py的布局结构和组件样式
- 使用统一配置管理器进行设置保存和加载
- 所有配置变更通过信号通知其他组件
========================================
"""

# ============================================================================
# 🎯 Instagram关注配置界面主类功能组
# ============================================================================
# 功能描述: Instagram关注配置界面主类，负责界面创建和事件处理
# 调用关系: 被任务管理页面调用，通过事件总线与业务层通信
# 注意事项: UI层只负责界面展示，业务逻辑通过事件总线处理
# ============================================================================

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                           QLabel, QScrollArea, QGridLayout, QFileDialog, QTextEdit)
from PyQt6.QtCore import pyqtSignal, QTimer
from PyQt6.QtGui import QColor
import logging

# 导入自定义UI组件
from .styled_widgets import (StyledButton, ModernSpinBox, ModernToggleSwitch,
                           StatusCard, HeartbeatIndicator, StyledLineEdit)
from .style_manager import (StyleManager, create_secondary_button, create_warning_button,
                          create_danger_button)


class InstagramFollowUI(QWidget):
    """
    Instagram关注功能配置界面
    ========================================
    功能描述: Instagram关注任务的参数配置和状态显示界面
    主要功能:
    - 基础配置：关注数量、延迟设置、筛选条件
    - 高级配置：地区选择、跳过设置、关注任务
    - 任务状态：实时显示任务执行状态和统计信息
    - 配置管理：与simple_config.py统一配置系统集成
    ========================================
    """

    # 🎯 信号定义 - UI与业务层通信
    setting_changed = pyqtSignal(str, object)      # 配置变更信号：参数名, 新值
    task_start_requested = pyqtSignal()            # 任务启动请求信号
    task_stop_requested = pyqtSignal()             # 任务停止请求信号

    def __init__(self, parent=None, config_manager=None):
        super().__init__(parent)

        # 🎯 配置管理器初始化 - 与统一配置系统集成
        if config_manager is not None:
            self.config_manager = config_manager
        else:
            # 兼容性：如果没有传入配置管理器，创建新实例
            from core.simple_config import get_config_manager
            self.config_manager = get_config_manager()

        # 🎯 日志管理器初始化
        self.logger = logging.getLogger(self.__class__.__name__)

        # 🎯 UI初始化顺序 - 确保正确的依赖关系
        self.init_ui()           # 1. 创建UI控件和布局
        self.load_settings()     # 2. 从配置文件加载设置值到UI
        self.setup_connections() # 3. 连接UI控件信号到配置保存

    def init_ui(self):
        """创建界面布局"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")

        # 创建内容容器
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(30)

        # 标题
        title_label = QLabel("Instagram关注功能配置")
        title_label.setStyleSheet("font-size: 22px; font-weight: bold; color: #333; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 实时任务状态区域
        status_section = self.create_task_status_section()
        layout.addWidget(status_section)

        # 基础配置参数区域
        basic_config_section = self.create_basic_config_section()
        layout.addWidget(basic_config_section)

        # 高级配置区域
        advanced_config_section = self.create_advanced_config_section()
        layout.addWidget(advanced_config_section)

        layout.addStretch()

        scroll_area.setWidget(content_widget)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

    def create_task_status_section(self):
        """创建实时任务状态区域"""
        status_group = QGroupBox("🎯 实时任务状态")
        status_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e3f2fd;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 15px;
                background-color: #f3f9ff;
                min-height: 200px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 12px 0 12px;
                background-color: #f3f9ff;
                color: #1976D2;
            }
        """)

        layout = QVBoxLayout(status_group)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 状态卡片行
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(15)

        self.completed_card = StatusCard("已关注", "0/0", "✅")
        self.success_rate_card = StatusCard("成功率", "0%", "📊")
        self.runtime_card = StatusCard("运行时间", "00:00:00", "⏱️")

        cards_layout.addWidget(self.completed_card)
        cards_layout.addWidget(self.success_rate_card)
        cards_layout.addWidget(self.runtime_card)

        layout.addLayout(cards_layout)

        # 系统状态和控制按钮区域
        control_layout = QHBoxLayout()
        control_layout.setSpacing(15)

        status_label = QLabel("系统状态:")
        status_label.setStyleSheet("font-weight: bold; color: #333; font-size: 14px;")

        self.system_status_indicator = HeartbeatIndicator()
        self.system_status_indicator.set_active(True)
        self.system_status_indicator.set_color(QColor("#4CAF50"))

        # 控制按钮
        self.refresh_btn = create_secondary_button("🔄 刷新", (80, 30))
        self.stop_task_btn = create_danger_button("⏹ 停止任务", (100, 30))

        control_layout.addWidget(status_label)
        control_layout.addWidget(self.system_status_indicator)
        control_layout.addWidget(self.refresh_btn)
        control_layout.addWidget(self.stop_task_btn)
        control_layout.addStretch()

        layout.addLayout(control_layout)

        return status_group

    def create_basic_config_section(self):
        """
        创建基础配置参数区域
        ========================================
        功能描述: 创建Instagram关注任务的基础参数配置界面
        包含控件:
        - 直接用户关注数量：控制直接关注的用户数量(无最小值限制)
        - 用户粉丝关注数量：控制关注粉丝的数量(无最小值限制)
        - 粉丝数筛选：最少粉丝数要求(1起)
        - 切换用户延迟：控制切换用户的时间间隔(0-60000毫秒，无限制)
        - 关注延迟：控制关注操作后的等待时间(0-60000毫秒，无限制)
        ========================================
        """
        basic_group = QGroupBox("🎯 基础配置参数")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e8f5e8;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #f8fff8;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #f8fff8;
                color: #4CAF50;
            }
        """)

        # 使用QGridLayout，确保控件显示一致性
        layout = QGridLayout(basic_group)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setVerticalSpacing(15)
        layout.setHorizontalSpacing(5)
        layout.setColumnMinimumWidth(0, 120)
        layout.setColumnStretch(1, 0)
        layout.setColumnStretch(2, 1)

        row = 0

        # 直接用户关注数量
        label = QLabel("直接关注数量:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.direct_follow_count_spinbox = ModernSpinBox(suffix=" 个")
        self.direct_follow_count_spinbox.setRange(0, 9999)  # 不设置最小值限制
        self.direct_follow_count_spinbox.setFixedWidth(200)
        layout.addWidget(label, row, 0)
        layout.addWidget(self.direct_follow_count_spinbox, row, 1)
        row += 1

        # 用户粉丝关注数量
        label = QLabel("粉丝关注数量:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.fans_follow_count_spinbox = ModernSpinBox(suffix=" 个")
        self.fans_follow_count_spinbox.setRange(0, 9999)  # 不设置最小值限制
        self.fans_follow_count_spinbox.setFixedWidth(200)
        layout.addWidget(label, row, 0)
        layout.addWidget(self.fans_follow_count_spinbox, row, 1)
        row += 1

        # 粉丝数筛选
        label = QLabel("粉丝数筛选:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.min_followers_spinbox = ModernSpinBox(suffix=" 个")
        self.min_followers_spinbox.setRange(1, 999999)  # 最少1个粉丝
        self.min_followers_spinbox.setFixedWidth(200)
        layout.addWidget(label, row, 0)
        layout.addWidget(self.min_followers_spinbox, row, 1)
        row += 1

        # 切换用户延迟
        label = QLabel("切换用户延迟:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        delay_widget = QWidget()
        delay_layout = QHBoxLayout(delay_widget)
        delay_layout.setContentsMargins(0, 0, 0, 0)
        delay_layout.setSpacing(10)

        self.switch_delay_min_spinbox = ModernSpinBox()
        self.switch_delay_min_spinbox.setRange(0, 60000)  # 0毫秒到60秒，去除限制
        self.switch_delay_min_spinbox.setFixedWidth(100)
        self.switch_delay_min_spinbox.spinbox.setSuffix("  毫秒")

        delay_to_label = QLabel("到")
        delay_to_label.setStyleSheet("font-size: 13px; color: #666;")

        self.switch_delay_max_spinbox = ModernSpinBox()
        self.switch_delay_max_spinbox.setRange(0, 60000)  # 0毫秒到60秒，去除限制
        self.switch_delay_max_spinbox.setFixedWidth(100)
        self.switch_delay_max_spinbox.spinbox.setSuffix("  毫秒")

        delay_layout.addWidget(self.switch_delay_min_spinbox)
        delay_layout.addWidget(delay_to_label)
        delay_layout.addWidget(self.switch_delay_max_spinbox)
        delay_layout.addStretch()

        layout.addWidget(label, row, 0)
        layout.addWidget(delay_widget, row, 1, 1, 2)
        row += 1

        # 关注延迟
        label = QLabel("关注延迟:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        follow_delay_widget = QWidget()
        follow_delay_layout = QHBoxLayout(follow_delay_widget)
        follow_delay_layout.setContentsMargins(0, 0, 0, 0)
        follow_delay_layout.setSpacing(10)

        self.follow_delay_min_spinbox = ModernSpinBox()
        self.follow_delay_min_spinbox.setRange(0, 60000)  # 0毫秒到60秒，去除限制
        self.follow_delay_min_spinbox.setFixedWidth(100)
        self.follow_delay_min_spinbox.spinbox.setSuffix("  毫秒")

        follow_delay_to_label = QLabel("到")
        follow_delay_to_label.setStyleSheet("font-size: 13px; color: #666;")

        self.follow_delay_max_spinbox = ModernSpinBox()
        self.follow_delay_max_spinbox.setRange(0, 60000)  # 0毫秒到60秒，去除限制
        self.follow_delay_max_spinbox.setFixedWidth(100)
        self.follow_delay_max_spinbox.spinbox.setSuffix("  毫秒")

        follow_delay_layout.addWidget(self.follow_delay_min_spinbox)
        follow_delay_layout.addWidget(follow_delay_to_label)
        follow_delay_layout.addWidget(self.follow_delay_max_spinbox)
        follow_delay_layout.addStretch()

        layout.addWidget(label, row, 0)
        layout.addWidget(follow_delay_widget, row, 1, 1, 2)
        row += 1

        # 蓝V用户跳过开关
        label = QLabel("蓝V用户跳过:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.skip_verified_switch = ModernToggleSwitch()
        layout.addWidget(label, row, 0)
        layout.addWidget(self.skip_verified_switch, row, 1)
        row += 1

        # 私密用户跳过开关
        label = QLabel("私密用户跳过:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.skip_private_switch = ModernToggleSwitch()
        layout.addWidget(label, row, 0)
        layout.addWidget(self.skip_private_switch, row, 1)

        return basic_group

    def create_advanced_config_section(self):
        """
        创建高级配置区域
        ========================================
        功能描述: 创建Instagram关注任务的高级参数配置界面
        包含控件:
        - 用户地区选择：所有地区、日本、韩国、泰国等地区筛选
        - 关注任务：将原来的"其他任务1"改为"关注任务"
        ========================================
        """
        advanced_group = QGroupBox("🎯 高级配置")
        advanced_group.setStyleSheet(StyleManager.get_groupbox_style('warning'))

        # 使用QGridLayout，确保控件显示一致性
        layout = QGridLayout(advanced_group)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setVerticalSpacing(15)
        layout.setHorizontalSpacing(5)
        layout.setColumnMinimumWidth(0, 120)
        layout.setColumnStretch(1, 0)
        layout.setColumnStretch(2, 1)

        row = 0

        # 用户地区选择标题
        region_label = QLabel("用户地区选择:")
        region_label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        layout.addWidget(region_label, row, 0)
        row += 1

        # 地区选择开关组 - 竖着排列
        region_widget = QWidget()
        region_layout = QVBoxLayout(region_widget)
        region_layout.setContentsMargins(0, 0, 0, 0)
        region_layout.setSpacing(10)

        # 所有地区
        all_regions_row = QWidget()
        all_regions_layout = QHBoxLayout(all_regions_row)
        all_regions_layout.setContentsMargins(0, 0, 0, 0)
        all_regions_layout.setSpacing(10)
        all_regions_label = QLabel("所有地区:")
        all_regions_label.setStyleSheet("font-size: 13px; color: #666; font-weight: 500;")
        all_regions_label.setFixedWidth(80)
        self.all_regions_switch = ModernToggleSwitch()
        all_regions_layout.addWidget(all_regions_label)
        all_regions_layout.addWidget(self.all_regions_switch)
        all_regions_layout.addStretch()
        region_layout.addWidget(all_regions_row)

        # 日本
        japan_row = QWidget()
        japan_layout = QHBoxLayout(japan_row)
        japan_layout.setContentsMargins(0, 0, 0, 0)
        japan_layout.setSpacing(10)
        japan_label = QLabel("日本:")
        japan_label.setStyleSheet("font-size: 13px; color: #666; font-weight: 500;")
        japan_label.setFixedWidth(80)
        self.japan_switch = ModernToggleSwitch()
        japan_layout.addWidget(japan_label)
        japan_layout.addWidget(self.japan_switch)
        japan_layout.addStretch()
        region_layout.addWidget(japan_row)

        # 韩国
        korea_row = QWidget()
        korea_layout = QHBoxLayout(korea_row)
        korea_layout.setContentsMargins(0, 0, 0, 0)
        korea_layout.setSpacing(10)
        korea_label = QLabel("韩国:")
        korea_label.setStyleSheet("font-size: 13px; color: #666; font-weight: 500;")
        korea_label.setFixedWidth(80)
        self.korea_switch = ModernToggleSwitch()
        korea_layout.addWidget(korea_label)
        korea_layout.addWidget(self.korea_switch)
        korea_layout.addStretch()
        region_layout.addWidget(korea_row)

        # 泰国
        thailand_row = QWidget()
        thailand_layout = QHBoxLayout(thailand_row)
        thailand_layout.setContentsMargins(0, 0, 0, 0)
        thailand_layout.setSpacing(10)
        thailand_label = QLabel("泰国:")
        thailand_label.setStyleSheet("font-size: 13px; color: #666; font-weight: 500;")
        thailand_label.setFixedWidth(80)
        self.thailand_switch = ModernToggleSwitch()
        thailand_layout.addWidget(thailand_label)
        thailand_layout.addWidget(self.thailand_switch)
        thailand_layout.addStretch()
        region_layout.addWidget(thailand_row)

        layout.addWidget(QLabel(""), row, 0)  # 空标签占位
        layout.addWidget(region_widget, row, 1, 1, 2)
        row += 1

        # 关注用户路径
        path_label = QLabel("关注用户路径:")
        path_label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")

        path_widget = QWidget()
        path_layout = QHBoxLayout(path_widget)
        path_layout.setContentsMargins(0, 0, 0, 0)
        path_layout.setSpacing(10)

        self.follow_users_path_input = StyledLineEdit()
        self.follow_users_path_input.setMinimumWidth(300)
        self.follow_users_path_input.setPlaceholderText("选择包含关注用户列表的文件")

        self.browse_follow_users_btn = create_secondary_button("浏览", (60, 30))

        path_layout.addWidget(self.follow_users_path_input)
        path_layout.addWidget(self.browse_follow_users_btn)
        path_layout.addStretch()

        layout.addWidget(path_label, row, 0)
        layout.addWidget(path_widget, row, 1, 1, 2)

        return advanced_group

    def setup_connections(self):
        """设置信号连接"""
        # 基础配置信号连接
        if hasattr(self, 'direct_follow_count_spinbox'):
            self.direct_follow_count_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("instagram_follow.direct_follow_count", v)
            )

        if hasattr(self, 'fans_follow_count_spinbox'):
            self.fans_follow_count_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("instagram_follow.fans_follow_count", v)
            )

        if hasattr(self, 'min_followers_spinbox'):
            self.min_followers_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("instagram_follow.min_followers", v)
            )

        if hasattr(self, 'switch_delay_min_spinbox'):
            self.switch_delay_min_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("instagram_follow.switch_delay_min", v)
            )

        if hasattr(self, 'switch_delay_max_spinbox'):
            self.switch_delay_max_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("instagram_follow.switch_delay_max", v)
            )

        if hasattr(self, 'follow_delay_min_spinbox'):
            self.follow_delay_min_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("instagram_follow.follow_delay_min", v)
            )

        if hasattr(self, 'follow_delay_max_spinbox'):
            self.follow_delay_max_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("instagram_follow.follow_delay_max", v)
            )

        if hasattr(self, 'skip_verified_switch'):
            self.skip_verified_switch.toggled.connect(
                lambda v: self._on_setting_changed("instagram_follow.skip_verified", v)
            )

        if hasattr(self, 'skip_private_switch'):
            self.skip_private_switch.toggled.connect(
                lambda v: self._on_setting_changed("instagram_follow.skip_private", v)
            )

        # 地区选择信号连接
        if hasattr(self, 'all_regions_switch'):
            self.all_regions_switch.toggled.connect(
                lambda v: self._on_setting_changed("instagram_follow.all_regions", v)
            )

        if hasattr(self, 'japan_switch'):
            self.japan_switch.toggled.connect(
                lambda v: self._on_setting_changed("instagram_follow.japan", v)
            )

        if hasattr(self, 'korea_switch'):
            self.korea_switch.toggled.connect(
                lambda v: self._on_setting_changed("instagram_follow.korea", v)
            )

        if hasattr(self, 'thailand_switch'):
            self.thailand_switch.toggled.connect(
                lambda v: self._on_setting_changed("instagram_follow.thailand", v)
            )

        # 文件路径信号连接
        if hasattr(self, 'follow_users_path_input'):
            self.follow_users_path_input.textChanged.connect(
                lambda v: self._on_setting_changed("instagram_follow.follow_users_path", v)
            )

        # 按钮信号连接
        if hasattr(self, 'browse_follow_users_btn'):
            self.browse_follow_users_btn.clicked.connect(self._on_browse_follow_users_file)

        if hasattr(self, 'refresh_btn'):
            self.refresh_btn.clicked.connect(self._on_refresh_status)

        if hasattr(self, 'stop_task_btn'):
            self.stop_task_btn.clicked.connect(self._on_stop_task)

    def load_settings(self):
        """
        加载设置值
        ========================================
        功能描述: 从统一配置系统加载Instagram关注参数到UI控件
        加载顺序:
        1. 基础配置：关注数量、延迟设置、筛选条件
        2. 任务控制：跳过设置开关
        3. 高级配置：地区选择、任务模式
        注意: 所有配置都从simple_config.py统一管理
        ========================================
        """
        try:
            # 🎯 基础配置加载
            direct_follow_count = self.config_manager.get("instagram_follow.direct_follow_count", 50)
            self.direct_follow_count_spinbox.setValue(direct_follow_count)

            fans_follow_count = self.config_manager.get("instagram_follow.fans_follow_count", 50)
            self.fans_follow_count_spinbox.setValue(fans_follow_count)

            min_followers = self.config_manager.get("instagram_follow.min_followers", 1)
            self.min_followers_spinbox.setValue(min_followers)

            switch_delay_min = self.config_manager.get("instagram_follow.switch_delay_min", 100)
            self.switch_delay_min_spinbox.setValue(switch_delay_min)

            switch_delay_max = self.config_manager.get("instagram_follow.switch_delay_max", 2000)
            self.switch_delay_max_spinbox.setValue(switch_delay_max)

            follow_delay_min = self.config_manager.get("instagram_follow.follow_delay_min", 100)
            self.follow_delay_min_spinbox.setValue(follow_delay_min)

            follow_delay_max = self.config_manager.get("instagram_follow.follow_delay_max", 2000)
            self.follow_delay_max_spinbox.setValue(follow_delay_max)

            skip_verified = self.config_manager.get("instagram_follow.skip_verified", True)
            self.skip_verified_switch.set_checked(skip_verified)

            skip_private = self.config_manager.get("instagram_follow.skip_private", True)
            self.skip_private_switch.set_checked(skip_private)

            # 加载地区选择配置
            all_regions = self.config_manager.get("instagram_follow.all_regions", True)
            self.all_regions_switch.set_checked(all_regions)

            japan = self.config_manager.get("instagram_follow.japan", False)
            self.japan_switch.set_checked(japan)

            korea = self.config_manager.get("instagram_follow.korea", False)
            self.korea_switch.set_checked(korea)

            thailand = self.config_manager.get("instagram_follow.thailand", False)
            self.thailand_switch.set_checked(thailand)

            # 加载关注用户路径设置
            follow_users_path = self.config_manager.get("instagram_follow.follow_users_path", "guanzhu.txt")
            self.follow_users_path_input.setText(follow_users_path)

            self.logger.info("Instagram关注配置加载完成")

        except Exception as e:
            self.logger.error(f"加载Instagram关注配置失败: {e}")

    def _on_setting_changed(self, key: str, value):
        """
        设置变更处理
        ========================================
        功能描述: 处理UI控件值变更，保存到统一配置系统
        处理流程:
        1. 保存配置到config_manager（立即持久化到app_config.json）
        2. 发送setting_changed信号通知其他组件
        3. 记录配置变更日志
        参数: key=配置键名, value=新配置值
        ========================================
        """
        try:
            # 🎯 保存到统一配置系统
            self.config_manager.set(key, value)
            # 🎯 发送变更信号
            self.setting_changed.emit(key, value)
            # 🎯 记录变更日志
            self.logger.debug(f"Instagram关注配置更新: {key} = {value}")
        except Exception as e:
            self.logger.error(f"保存Instagram关注配置失败: {e}")

    def _on_browse_follow_users_file(self):
        """浏览关注用户文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择关注用户文件", self.follow_users_path_input.text(), "文本文件 (*.txt);;所有文件 (*)"
        )
        if file_path:
            self.follow_users_path_input.setText(file_path)

    def _on_refresh_status(self):
        """刷新状态"""
        self.logger.info("刷新Instagram关注任务状态")
        # 这里可以添加刷新状态的逻辑

    def _on_stop_task(self):
        """停止任务"""
        self.logger.info("请求停止Instagram关注任务")
        self.task_stop_requested.emit()

    def update_task_status(self, completed: int, total: int, success_rate: float, runtime: str):
        """更新任务状态显示"""
        try:
            self.completed_card.update_value(f"{completed}/{total}")
            self.success_rate_card.update_value(f"{success_rate:.1f}%")
            self.runtime_card.update_value(runtime)
        except Exception as e:
            self.logger.error(f"更新任务状态显示失败: {e}")
