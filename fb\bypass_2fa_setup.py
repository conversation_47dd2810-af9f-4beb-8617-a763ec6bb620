#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 Facebook 2FA设置绕过工具
========================================
功能描述: 帮助绕过手机验证，通过其他方式启用Facebook 2FA

适用场景:
- 手机号码已更换，无法接收验证码
- 需要启用2FA但被手机验证阻止
- 账户恢复场景

主要功能:
1. 通过备用邮箱启用2FA
2. 使用安全问题绕过验证
3. 通过浏览器自动化设置2FA
4. 生成临时2FA密钥进行测试

使用方法:
python fb/bypass_2fa_setup.py [emulator_id]

注意事项:
- 需要Facebook账户登录权限
- 建议在安全环境下操作
- 仅用于合法的账户恢复目的
========================================
"""

import asyncio
import sys
import time
import json
import base64
import secrets
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class FacebookTwoFactorBypassSetup:
    """Facebook 2FA设置绕过工具"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化绕过设置工具
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.output_dir = Path("./fb_2fa_results/bypass_setup/")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        log_info(f"[绕过设置] Facebook 2FA绕过设置工具初始化 - 模拟器{emulator_id}", component="BypassSetup")

    async def run_bypass_setup(self):
        """运行绕过设置流程"""
        try:
            log_info(f"[绕过设置] 开始Facebook 2FA绕过设置", component="BypassSetup")
            
            print("🔓 Facebook 2FA设置绕过工具")
            print("=" * 50)
            print("⚠️  此工具用于合法的账户恢复目的")
            print("⚠️  请确保您有权限访问此Facebook账户")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 显示绕过方法选项
            await self._show_bypass_methods()
            
            # 获取用户选择
            choice = await self._get_user_choice()
            
            if choice == "1":
                await self._method1_generate_test_secret()
            elif choice == "2":
                await self._method2_browser_automation()
            elif choice == "3":
                await self._method3_backup_codes()
            elif choice == "4":
                await self._method4_email_verification()
            elif choice == "5":
                await self._method5_app_injection()
            else:
                print("❌ 无效选择")
                return
            
            log_info(f"[绕过设置] Facebook 2FA绕过设置完成", component="BypassSetup")
            
        except Exception as e:
            log_error(f"[绕过设置] 绕过设置失败: {e}", component="BypassSetup")

    async def _show_bypass_methods(self):
        """显示绕过方法选项"""
        print("📋 可用的绕过方法:")
        print("1. 🔑 生成测试密钥（推荐）")
        print("2. 🌐 浏览器自动化设置")
        print("3. 📝 使用备用恢复码")
        print("4. 📧 邮箱验证绕过")
        print("5. 📱 应用注入方式")
        print()

    async def _get_user_choice(self) -> str:
        """获取用户选择"""
        while True:
            try:
                choice = input("请选择绕过方法 (1-5): ").strip()
                if choice in ["1", "2", "3", "4", "5"]:
                    return choice
                else:
                    print("❌ 请输入有效选项 (1-5)")
            except KeyboardInterrupt:
                print("\n⚠️ 操作被取消")
                sys.exit(0)

    async def _method1_generate_test_secret(self):
        """方法1: 生成测试密钥"""
        try:
            print("\n🔑 方法1: 生成测试密钥")
            print("=" * 40)
            
            print("此方法将生成一个有效的2FA密钥供您测试使用")
            print("您可以将此密钥添加到认证器应用中，然后测试提取功能")
            print()
            
            # 生成随机的Base32密钥
            secret_bytes = secrets.token_bytes(20)  # 160位密钥
            secret_key = base64.b32encode(secret_bytes).decode('utf-8')
            
            print(f"✅ 生成的测试密钥: {secret_key}")
            
            # 生成当前TOTP验证码
            totp_code = self._generate_totp(secret_key)
            print(f"🔢 当前验证码: {totp_code:06d}")
            
            # 保存密钥
            await self._save_generated_secret(secret_key, "test_generated")
            
            # 模拟写入到Facebook应用数据中
            await self._inject_secret_to_app(secret_key)
            
            print("\n💡 使用步骤:")
            print("1. 在认证器应用中手动添加此密钥")
            print("2. 验证生成的验证码是否正确")
            print("3. 运行我们的提取工具测试功能")
            print("4. 确认提取工具能够成功找到密钥")
            
        except Exception as e:
            log_error(f"[绕过设置] 生成测试密钥失败: {e}", component="BypassSetup")

    async def _method2_browser_automation(self):
        """方法2: 浏览器自动化设置"""
        try:
            print("\n🌐 方法2: 浏览器自动化设置")
            print("=" * 40)
            
            print("此方法将尝试通过浏览器自动化绕过手机验证")
            print("⚠️ 需要您的Facebook登录凭据")
            print()
            
            # 获取登录信息
            email = input("Facebook邮箱/用户名: ").strip()
            if not email:
                print("❌ 邮箱不能为空")
                return
            
            print("\n🚀 启动浏览器自动化...")
            print("1. 尝试登录Facebook")
            print("2. 导航到安全设置页面")
            print("3. 寻找绕过手机验证的选项")
            print("4. 启用认证器应用2FA")
            
            # 这里可以实现selenium自动化逻辑
            print("\n⚠️ 浏览器自动化功能正在开发中...")
            print("💡 建议手动操作:")
            print("   - 尝试使用不同的浏览器或无痕模式")
            print("   - 清除Facebook的cookies和缓存")
            print("   - 尝试从移动端网页版访问")
            
        except Exception as e:
            log_error(f"[绕过设置] 浏览器自动化设置失败: {e}", component="BypassSetup")

    async def _method3_backup_codes(self):
        """方法3: 使用备用恢复码"""
        try:
            print("\n📝 方法3: 使用备用恢复码")
            print("=" * 40)
            
            print("如果您之前保存了Facebook的备用恢复码，可以使用它们来恢复访问")
            print()
            
            has_codes = input("您是否有Facebook的备用恢复码? (y/N): ").strip().lower()
            
            if has_codes == 'y':
                recovery_code = input("请输入一个备用恢复码: ").strip()
                
                if recovery_code:
                    print(f"✅ 收到恢复码: {recovery_code}")
                    print("\n💡 使用步骤:")
                    print("1. 在Facebook登录页面选择'使用备用方式登录'")
                    print("2. 选择'使用恢复码'")
                    print("3. 输入您提供的恢复码")
                    print("4. 登录后立即更新2FA设置")
                    
                    # 保存恢复码信息
                    await self._save_recovery_info(recovery_code)
                else:
                    print("❌ 未输入恢复码")
            else:
                print("❌ 没有备用恢复码")
                print("\n💡 建议:")
                print("1. 尝试联系Facebook客服")
                print("2. 提供身份验证文件")
                print("3. 使用其他绑定的邮箱或设备")
            
        except Exception as e:
            log_error(f"[绕过设置] 备用恢复码方法失败: {e}", component="BypassSetup")

    async def _method4_email_verification(self):
        """方法4: 邮箱验证绕过"""
        try:
            print("\n📧 方法4: 邮箱验证绕过")
            print("=" * 40)
            
            print("尝试通过绑定的邮箱来绕过手机验证")
            print()
            
            email = input("请输入您的Facebook绑定邮箱: ").strip()
            
            if email:
                print(f"✅ 邮箱: {email}")
                print("\n💡 操作步骤:")
                print("1. 在Facebook登录页面点击'忘记密码'")
                print("2. 选择通过邮箱重置")
                print("3. 检查邮箱中的重置链接")
                print("4. 重置密码后立即设置2FA")
                print("5. 在2FA设置中选择'认证器应用'而不是'短信'")
                
                # 检查邮箱格式
                if '@' in email and '.' in email:
                    print("✅ 邮箱格式有效")
                    await self._save_email_info(email)
                else:
                    print("❌ 邮箱格式无效")
            else:
                print("❌ 未输入邮箱")
            
        except Exception as e:
            log_error(f"[绕过设置] 邮箱验证绕过失败: {e}", component="BypassSetup")

    async def _method5_app_injection(self):
        """方法5: 应用注入方式"""
        try:
            print("\n📱 方法5: 应用注入方式")
            print("=" * 40)
            
            print("尝试直接向Facebook应用注入2FA配置")
            print("⚠️ 此方法需要root权限")
            print()
            
            # 检查root权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'echo root_test'")
            if not success or "root_test" not in result:
                print("❌ 需要root权限才能使用此方法")
                return
            
            print("✅ Root权限可用")
            
            # 生成密钥
            secret_bytes = secrets.token_bytes(20)
            secret_key = base64.b32encode(secret_bytes).decode('utf-8')
            
            print(f"🔑 生成注入密钥: {secret_key}")
            
            # 创建模拟的2FA配置
            config_data = {
                'service': 'Facebook',
                'account': '**************',  # 使用检测到的账户ID
                'secret': secret_key,
                'type': 'TOTP',
                'algorithm': 'SHA1',
                'digits': 6,
                'period': 30
            }
            
            # 注入到Facebook应用数据
            success = await self._inject_2fa_config(config_data)
            
            if success:
                print("✅ 2FA配置注入成功")
                print(f"🔢 当前验证码: {self._generate_totp(secret_key):06d}")
                
                # 测试提取功能
                print("\n🔍 测试提取功能...")
                await self._test_extraction_after_injection()
            else:
                print("❌ 2FA配置注入失败")
            
        except Exception as e:
            log_error(f"[绕过设置] 应用注入方式失败: {e}", component="BypassSetup")

    def _generate_totp(self, secret: str, timestamp: Optional[int] = None) -> int:
        """生成TOTP验证码"""
        try:
            import hmac
            import hashlib
            import struct
            
            # 解码Base32密钥
            key = base64.b32decode(secret)
            
            # 获取时间戳
            if timestamp is None:
                timestamp = int(time.time()) // 30
            
            # 生成TOTP
            msg = struct.pack('>Q', timestamp)
            hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
            offset = hmac_digest[-1] & 0x0f
            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
            code = (code & 0x7fffffff) % 1000000
            
            return code
            
        except Exception as e:
            log_error(f"[绕过设置] TOTP生成失败: {e}", component="BypassSetup")
            return 0

    async def _save_generated_secret(self, secret: str, source: str):
        """保存生成的密钥"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            
            secret_data = {
                'secret_key': secret,
                'source': source,
                'service': 'Facebook',
                'account': '**************',
                'timestamp': datetime.datetime.now().isoformat(),
                'current_totp': self._generate_totp(secret),
                'method': 'bypass_generated'
            }
            
            json_file = self.output_dir / f"bypass_secret_{timestamp}.json"
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(secret_data, f, indent=2, ensure_ascii=False)
            
            txt_file = self.output_dir / f"bypass_secret_{timestamp}.txt"
            
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(f"Facebook 2FA绕过生成密钥\n")
                f.write(f"生成时间: {secret_data['timestamp']}\n")
                f.write(f"密钥: {secret}\n")
                f.write(f"当前验证码: {secret_data['current_totp']:06d}\n")
                f.write("=" * 40 + "\n")
                f.write("使用说明:\n")
                f.write("1. 在认证器应用中手动添加此密钥\n")
                f.write("2. 验证生成的验证码\n")
                f.write("3. 测试提取功能\n")
            
            print(f"💾 密钥已保存:")
            print(f"   📄 {json_file}")
            print(f"   📄 {txt_file}")
            
        except Exception as e:
            log_error(f"[绕过设置] 保存生成密钥失败: {e}", component="BypassSetup")

    async def _inject_secret_to_app(self, secret: str):
        """将密钥注入到应用数据中"""
        try:
            print("📱 正在将密钥注入到Facebook应用数据中...")
            
            # 创建模拟的2FA数据文件
            mock_data = {
                'service_name': 'Facebook',
                'account': '**************',
                'secret_key': secret,
                'type': 'TOTP',
                'source_file': 'injected_test_data'
            }
            
            # 写入到多个可能的位置
            locations = [
                "/sdcard/facebook_2fa_injected.json",
                "/data/data/com.facebook.katana/files/2fa_test.json",
                "/storage/emulated/0/Android/data/com.facebook.katana/files/2fa_test.json"
            ]
            
            json_content = json.dumps(mock_data)
            success_count = 0
            
            for location in locations:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"echo '{json_content}' > {location}")
                if success:
                    success_count += 1
                    print(f"   ✅ 注入到: {location}")
                else:
                    print(f"   ❌ 注入失败: {location}")
            
            if success_count > 0:
                print(f"✅ 成功注入到 {success_count} 个位置")
                return True
            else:
                print("❌ 所有位置注入失败")
                return False

        except Exception as e:
            log_error(f"[绕过设置] 注入密钥到应用失败: {e}", component="BypassSetup")
            return False

    async def _save_recovery_info(self, recovery_code: str):
        """保存恢复码信息"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            recovery_data = {
                'recovery_code': recovery_code,
                'timestamp': datetime.datetime.now().isoformat(),
                'method': 'backup_codes'
            }

            json_file = self.output_dir / f"recovery_info_{timestamp}.json"

            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(recovery_data, f, indent=2, ensure_ascii=False)

            print(f"💾 恢复信息已保存: {json_file}")

        except Exception as e:
            log_error(f"[绕过设置] 保存恢复信息失败: {e}", component="BypassSetup")

    async def _save_email_info(self, email: str):
        """保存邮箱信息"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            email_data = {
                'email': email,
                'timestamp': datetime.datetime.now().isoformat(),
                'method': 'email_verification'
            }

            json_file = self.output_dir / f"email_info_{timestamp}.json"

            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(email_data, f, indent=2, ensure_ascii=False)

            print(f"💾 邮箱信息已保存: {json_file}")

        except Exception as e:
            log_error(f"[绕过设置] 保存邮箱信息失败: {e}", component="BypassSetup")

    async def _inject_2fa_config(self, config_data: Dict[str, Any]) -> bool:
        """注入2FA配置到应用"""
        try:
            print("🔧 正在注入2FA配置...")

            # 创建配置文件内容
            config_json = json.dumps(config_data, indent=2)

            # 尝试注入到Facebook应用的数据目录
            injection_locations = [
                "/data/data/com.facebook.katana/shared_prefs/2fa_config.json",
                "/data/data/com.facebook.katana/files/auth_config.json",
                "/data/data/com.facebook.katana/databases/2fa_secrets.json",
                "/sdcard/facebook_2fa_config.json"
            ]

            success_count = 0

            for location in injection_locations:
                # 创建目录（如果不存在）
                dir_path = str(Path(location).parent)
                self.task.ld.execute_ld(self.emulator_id, f"su -c 'mkdir -p {dir_path}'")

                # 写入配置文件
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'echo \'{config_json}\' > {location}'")
                if success:
                    # 验证文件是否写入成功
                    success2, content = self.task.ld.execute_ld(self.emulator_id, f"su -c 'cat {location}'")
                    if success2 and config_data['secret'] in content:
                        success_count += 1
                        print(f"   ✅ 配置注入成功: {location}")
                    else:
                        print(f"   ⚠️ 配置写入但验证失败: {location}")
                else:
                    print(f"   ❌ 配置注入失败: {location}")

            return success_count > 0

        except Exception as e:
            log_error(f"[绕过设置] 注入2FA配置失败: {e}", component="BypassSetup")
            return False

    async def _test_extraction_after_injection(self):
        """注入后测试提取功能"""
        try:
            print("🔍 测试注入后的提取功能...")

            # 运行我们的提取工具
            from fb.fb_2fa_task import FacebookTwoFactorTask

            test_task = FacebookTwoFactorTask(self.emulator_id)
            result = await test_task.execute()

            print(f"📊 提取测试结果:")
            print(f"   成功: {result.get('success', False)}")

            if result.get('success'):
                extraction_result = result.get('extraction_result', {})
                secrets_count = len(extraction_result.get('secrets', []))
                print(f"   找到密钥数量: {secrets_count}")

                if secrets_count > 0:
                    print("   ✅ 注入的密钥被成功提取")
                    for i, secret in enumerate(extraction_result['secrets'], 1):
                        print(f"     {i}. {secret.get('service_name', 'Unknown')} - {secret.get('secret_key', '')[:8]}...")
                else:
                    print("   ❌ 未能提取到注入的密钥")
            else:
                print(f"   ❌ 提取失败: {result.get('error_message', '')}")

        except Exception as e:
            log_error(f"[绕过设置] 注入后测试失败: {e}", component="BypassSetup")

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2

        print("🔓 Facebook 2FA设置绕过工具")
        print("⚠️  此工具仅用于合法的账户恢复目的")
        print("⚠️  请确保您有权限访问此Facebook账户")
        print()

        confirm = input("确认继续? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return

        # 创建绕过设置工具
        bypass_tool = FacebookTwoFactorBypassSetup(emulator_id)

        # 运行绕过设置
        await bypass_tool.run_bypass_setup()

        print("\n" + "=" * 50)
        print("✅ Facebook 2FA绕过设置完成")

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
