#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 直接Facebook操作工具
========================================
功能描述: 不依赖检测，直接执行所有可能的Facebook跳转方法

核心策略:
1. 不检测是否成功，直接执行所有方法
2. 让用户自己确认哪个方法有效
3. 提供多种备用方案

使用方法:
python fb/direct_facebook_action.py
========================================
"""

import subprocess
import time

def execute_all_facebook_methods():
    """执行所有可能的Facebook跳转方法"""
    print("🔓 直接Facebook操作工具")
    print("=" * 50)
    print("⚠️ 不依赖检测，直接执行所有可能的方法")
    print("⚠️ 请您自己观察模拟器2屏幕的变化")
    print()
    
    emulator_id = 2
    ldconsole_path = "G:/leidian/LDPlayer9/ldconsole.exe"
    
    print(f"📱 目标模拟器: 模拟器{emulator_id}")
    print()
    
    # 方法1: 尝试所有可能的Facebook应用启动方式
    print("📱 方法1: 尝试启动Facebook应用...")
    facebook_packages = [
        "com.facebook.katana",
        "com.facebook.lite", 
        "com.facebook.orca"
    ]
    
    facebook_start_commands = [
        "monkey -p {package} -c android.intent.category.LAUNCHER 1",
        "am start -n {package}/.LoginActivity",
        "am start -n {package}/.MainActivity", 
        "am start {package}",
        "am start -a android.intent.action.MAIN -c android.intent.category.LAUNCHER -n {package}/.LoginActivity"
    ]
    
    for package in facebook_packages:
        print(f"   🔍 尝试启动: {package}")
        for cmd_template in facebook_start_commands:
            cmd = cmd_template.format(package=package)
            print(f"      执行: {cmd}")
            
            try:
                full_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", f"shell {cmd}"]
                result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
                print(f"         结果: {'成功' if result.returncode == 0 else '失败'}")
                time.sleep(2)  # 等待2秒
            except Exception as e:
                print(f"         异常: {e}")
    
    print("\n   ⏳ 等待5秒，请观察模拟器屏幕...")
    time.sleep(5)
    
    # 方法2: 尝试Facebook深度链接
    print("\n📱 方法2: 尝试Facebook深度链接...")
    facebook_deep_links = [
        "fb://login/identify",
        "fb://recover/initiate",
        "facebook://login/identify",
        "facebook://recover/initiate",
        "fb://login",
        "facebook://login"
    ]
    
    for deep_link in facebook_deep_links:
        print(f"   🔍 尝试深度链接: {deep_link}")
        cmd = f"am start -a android.intent.action.VIEW -d '{deep_link}'"
        
        try:
            full_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", f"shell {cmd}"]
            result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
            print(f"      结果: {'成功' if result.returncode == 0 else '失败'}")
            time.sleep(2)
        except Exception as e:
            print(f"      异常: {e}")
    
    print("\n   ⏳ 等待5秒，请观察模拟器屏幕...")
    time.sleep(5)
    
    # 方法3: 尝试所有浏览器打开Facebook
    print("\n🌐 方法3: 尝试浏览器打开Facebook...")
    browsers = [
        "com.android.chrome",
        "com.android.browser",
        "org.mozilla.firefox",
        "com.opera.browser"
    ]
    
    facebook_urls = [
        "https://m.facebook.com/login/identify",
        "https://www.facebook.com/login/identify",
        "https://mbasic.facebook.com/login/identify",
        "https://facebook.com/login/identify",
        "https://m.facebook.com/recover/initiate",
        "https://www.facebook.com/recover/initiate"
    ]
    
    # 首先尝试系统默认浏览器
    print("   🔍 尝试系统默认浏览器...")
    for url in facebook_urls:
        print(f"      打开URL: {url}")
        cmd = f"am start -a android.intent.action.VIEW -d '{url}'"
        
        try:
            full_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", f"shell {cmd}"]
            result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
            print(f"         结果: {'成功' if result.returncode == 0 else '失败'}")
            time.sleep(3)  # 等待3秒让页面加载
        except Exception as e:
            print(f"         异常: {e}")
    
    print("\n   ⏳ 等待5秒，请观察模拟器屏幕...")
    time.sleep(5)
    
    # 然后尝试特定浏览器
    for browser in browsers:
        print(f"   🔍 尝试特定浏览器: {browser}")
        for url in facebook_urls[:2]:  # 只尝试前2个URL
            print(f"      打开URL: {url}")
            cmd = f"am start -n {browser}/.BrowserActivity -a android.intent.action.VIEW -d '{url}'"
            
            try:
                full_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", f"shell {cmd}"]
                result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
                print(f"         结果: {'成功' if result.returncode == 0 else '失败'}")
                time.sleep(2)
            except Exception as e:
                print(f"         异常: {e}")
    
    print("\n   ⏳ 等待5秒，请观察模拟器屏幕...")
    time.sleep(5)
    
    # 方法4: 尝试直接操作屏幕
    print("\n👆 方法4: 尝试屏幕操作...")
    screen_actions = [
        ("点击屏幕中央", "input tap 360 640"),
        ("向上滑动", "input swipe 360 800 360 400"),
        ("向下滑动", "input swipe 360 400 360 800"),
        ("按Home键", "input keyevent KEYCODE_HOME"),
        ("按菜单键", "input keyevent KEYCODE_MENU"),
        ("按返回键", "input keyevent KEYCODE_BACK")
    ]
    
    for desc, cmd in screen_actions:
        print(f"   🔍 {desc}: {cmd}")
        
        try:
            full_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", f"shell {cmd}"]
            result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
            print(f"      结果: {'成功' if result.returncode == 0 else '失败'}")
            time.sleep(1)
        except Exception as e:
            print(f"      异常: {e}")
    
    # 方法5: 获取当前屏幕信息
    print("\n📱 方法5: 获取当前屏幕信息...")
    info_commands = [
        ("当前Activity", "dumpsys window windows | grep -E 'mCurrentFocus'"),
        ("运行的应用", "ps | grep -E '(facebook|chrome|browser)'"),
        ("屏幕截图", "screencap -p /sdcard/current_screen.png")
    ]
    
    for desc, cmd in info_commands:
        print(f"   🔍 {desc}...")
        
        try:
            full_cmd = [ldconsole_path, f"action{emulator_id}", "--key", "call.adb", "--value", f"shell {cmd}"]
            result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                output = result.stdout.strip()[:200]  # 限制输出长度
                print(f"      结果: {output}")
            else:
                print(f"      结果: 无输出或失败")
        except Exception as e:
            print(f"      异常: {e}")
    
    print("\n" + "=" * 50)
    print("✅ 所有方法执行完成")
    print()
    print("🔍 请您现在检查模拟器2的屏幕:")
    print("1. 是否有Facebook应用打开？")
    print("2. 是否有浏览器打开Facebook页面？")
    print("3. 屏幕上是否显示任何Facebook相关内容？")
    print()
    print("💡 如果看到Facebook相关内容:")
    print("- 寻找'忘记密码?'链接")
    print("- 寻找'找回账户'按钮")
    print("- 寻找登录页面的重置选项")
    print()
    print("💡 如果没有看到任何变化:")
    print("- Facebook应用可能未安装")
    print("- 浏览器可能无法访问网络")
    print("- 需要手动操作")

def show_manual_instructions():
    """显示手动操作指导"""
    print("\n🔧 手动操作指导")
    print("=" * 50)
    print("如果所有自动方法都失败，请手动操作:")
    print()
    print("📱 步骤1: 手动打开Facebook")
    print("1. 在模拟器2中寻找Facebook应用图标")
    print("2. 如果找到，点击打开")
    print("3. 如果没有，打开浏览器")
    print()
    print("🌐 步骤2: 手动访问Facebook重置页面")
    print("1. 在浏览器地址栏输入:")
    print("   https://m.facebook.com/login/identify")
    print("2. 或者搜索'Facebook密码重置'")
    print()
    print("📝 步骤3: 完成密码重置")
    print("1. 输入您的邮箱地址")
    print("2. 选择通过邮箱重置密码")
    print("3. 检查邮箱中的重置链接")
    print("4. 设置新密码")
    print()
    print("💡 重要提示:")
    print("- 使用邮箱而不是手机号")
    print("- 避免短信验证")
    print("- 检查垃圾邮件文件夹")

def main():
    """主函数"""
    try:
        print("🔓 直接Facebook操作工具")
        print("⚠️ 不依赖检测，直接执行所有可能的方法")
        print("⚠️ 请您观察模拟器2屏幕的变化")
        print()
        
        confirm = input("确认执行所有Facebook跳转方法? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return
        
        # 执行所有方法
        execute_all_facebook_methods()
        
        # 询问结果
        print("\n" + "=" * 50)
        result = input("请问模拟器2屏幕是否有任何变化? (输入 'YES' 如果有变化, 'NO' 如果没有): ").strip()
        
        if result.upper() == 'YES':
            print("\n🎉 太好了！有变化说明某个方法有效!")
            print("请按照屏幕上的指导完成Facebook密码重置")
        else:
            print("\n❌ 没有变化，显示手动操作指导")
            show_manual_instructions()
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
