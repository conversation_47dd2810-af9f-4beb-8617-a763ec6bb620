#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 Facebook应用详细诊断工具
========================================
功能描述: 详细诊断Facebook应用状态和2FA配置

核心功能:
1. 检查Facebook应用安装状态
2. 分析应用数据目录结构
3. 检查用户登录状态
4. 诊断2FA配置状态
5. 提供详细的解决方案

使用方法:
python fb/facebook_diagnostic.py [emulator_id]

注意事项:
- 需要root权限
- 提供详细的诊断信息
- 给出具体的解决建议
========================================
"""

import asyncio
import sys
import re
import json
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class FacebookDiagnostic:
    """Facebook应用详细诊断器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化诊断器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.facebook_package = "com.facebook.katana"
        self.facebook_data_path = f"/data/data/{self.facebook_package}"
        
        log_info(f"[Facebook诊断] Facebook诊断器初始化 - 模拟器{emulator_id}", component="FacebookDiagnostic")

    async def run_diagnostic(self):
        """运行完整诊断"""
        try:
            log_info(f"[Facebook诊断] 开始Facebook应用诊断", component="FacebookDiagnostic")
            
            print("🔍 Facebook应用详细诊断工具")
            print("=" * 50)
            print("⚠️  详细分析Facebook应用状态")
            print("⚠️  诊断2FA配置情况")
            print("⚠️  提供具体解决方案")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：基础环境检查
            await self._check_basic_environment()
            
            # 第二步：Facebook应用状态检查
            await self._check_facebook_app_status()
            
            # 第三步：用户登录状态检查
            await self._check_user_login_status()
            
            # 第四步：数据目录详细分析
            await self._analyze_data_directory_details()
            
            # 第五步：2FA状态诊断
            await self._diagnose_2fa_status()
            
            # 第六步：网络和服务检查
            await self._check_network_and_services()
            
            # 第七步：提供解决方案
            await self._provide_solutions()
            
            log_info(f"[Facebook诊断] Facebook应用诊断完成", component="FacebookDiagnostic")
            
        except Exception as e:
            log_error(f"[Facebook诊断] 诊断失败: {e}", component="FacebookDiagnostic")

    async def _check_basic_environment(self):
        """检查基础环境"""
        try:
            print("🔧 基础环境检查...")
            print("-" * 40)
            
            # 检查Android版本
            success, result = self.task.ld.execute_ld(self.emulator_id, "getprop ro.build.version.release")
            if success and result.strip():
                android_version = result.strip()
                print(f"📱 Android版本: {android_version}")
            
            # 检查设备型号
            success, result = self.task.ld.execute_ld(self.emulator_id, "getprop ro.product.model")
            if success and result.strip():
                device_model = result.strip()
                print(f"📱 设备型号: {device_model}")
            
            # 检查root权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'whoami'")
            if success and "root" in result:
                print("✅ Root权限: 可用")
            else:
                print("❌ Root权限: 不可用")
            
            # 检查存储空间
            success, result = self.task.ld.execute_ld(self.emulator_id, "df -h /data")
            if success and result.strip():
                storage_info = result.strip().split('\n')[-1]
                print(f"💾 /data分区存储: {storage_info}")
            
            print("✅ 基础环境检查完成")
            print()
            
        except Exception as e:
            log_error(f"[Facebook诊断] 基础环境检查失败: {e}", component="FacebookDiagnostic")

    async def _check_facebook_app_status(self):
        """检查Facebook应用状态"""
        try:
            print("📱 Facebook应用状态检查...")
            print("-" * 40)
            
            # 检查应用是否安装
            success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages | grep {self.facebook_package}")
            if success and self.facebook_package in result:
                print("✅ Facebook应用: 已安装")
                
                # 获取应用版本信息
                success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"dumpsys package {self.facebook_package} | grep versionName")
                if success2 and result2.strip():
                    version_line = result2.strip().split('\n')[0]
                    version = version_line.split('=')[-1] if '=' in version_line else "未知"
                    print(f"📦 应用版本: {version}")
                
                # 检查应用权限
                success3, result3 = self.task.ld.execute_ld(self.emulator_id, f"dumpsys package {self.facebook_package} | grep permission")
                if success3 and result3.strip():
                    permissions = result3.strip().split('\n')
                    print(f"🔐 应用权限: {len(permissions)} 个权限")
                    
                    # 检查关键权限
                    key_permissions = ['INTERNET', 'CAMERA', 'READ_PHONE_STATE', 'ACCESS_NETWORK_STATE']
                    for perm in key_permissions:
                        if any(perm in line for line in permissions):
                            print(f"   ✅ {perm}")
                        else:
                            print(f"   ❌ {perm}")
                
            else:
                print("❌ Facebook应用: 未安装")
                return
            
            # 检查应用是否正在运行
            success, result = self.task.ld.execute_ld(self.emulator_id, f"ps | grep {self.facebook_package}")
            if success and result.strip():
                processes = result.strip().split('\n')
                print(f"🔄 运行状态: 正在运行 ({len(processes)} 个进程)")
                for process in processes:
                    parts = process.split()
                    if len(parts) > 1:
                        pid = parts[1]
                        print(f"   📊 PID: {pid}")
            else:
                print("⚠️  运行状态: 未运行")
            
            # 检查应用数据目录
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'ls -la {self.facebook_data_path}'")
            if success and "No such file" not in result:
                print("✅ 数据目录: 存在")
                
                # 检查目录大小
                success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'du -sh {self.facebook_data_path}'")
                if success2 and result2.strip():
                    size_info = result2.strip().split()[0]
                    print(f"📊 数据目录大小: {size_info}")
            else:
                print("❌ 数据目录: 不存在")
            
            print("✅ Facebook应用状态检查完成")
            print()
            
        except Exception as e:
            log_error(f"[Facebook诊断] 应用状态检查失败: {e}", component="FacebookDiagnostic")

    async def _check_user_login_status(self):
        """检查用户登录状态"""
        try:
            print("👤 用户登录状态检查...")
            print("-" * 40)
            
            # 检查SharedPreferences中的用户信息
            prefs_path = f"{self.facebook_data_path}/shared_prefs"
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {prefs_path} -name \"*.xml\" 2>/dev/null'")
            if success and result.strip():
                xml_files = result.strip().split('\n')
                print(f"📋 SharedPreferences文件: {len(xml_files)} 个")
                
                # 查找用户相关的配置文件
                user_related_files = []
                for xml_file in xml_files:
                    if any(keyword in xml_file.lower() for keyword in ['user', 'account', 'login', 'auth', 'session']):
                        user_related_files.append(xml_file)
                
                if user_related_files:
                    print(f"👤 用户相关配置文件: {len(user_related_files)} 个")
                    for file_path in user_related_files[:3]:  # 只显示前3个
                        file_name = file_path.split('/')[-1]
                        print(f"   📄 {file_name}")
                        
                        # 尝试读取文件内容查找用户ID
                        success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'cat {file_path}' | grep -E '(user|id|account)' | head -3")
                        if success2 and result2.strip():
                            lines = result2.strip().split('\n')
                            for line in lines:
                                if 'user' in line.lower() or 'id' in line.lower():
                                    print(f"      🔍 {line.strip()[:80]}...")
                else:
                    print("❌ 未找到用户相关配置文件")
            else:
                print("❌ 未找到SharedPreferences文件")
            
            # 检查数据库中的用户信息
            db_path = f"{self.facebook_data_path}/databases"
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {db_path} -name \"*.db\" 2>/dev/null'")
            if success and result.strip():
                db_files = result.strip().split('\n')
                print(f"📊 数据库文件: {len(db_files)} 个")
                
                for db_file in db_files[:3]:  # 只检查前3个数据库
                    if db_file.strip():
                        db_name = db_file.split('/')[-1]
                        print(f"   🔍 检查数据库: {db_name}")
                        
                        # 尝试查看数据库表
                        success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'sqlite3 {db_file} \".tables\"'")
                        if success2 and result2.strip():
                            tables = result2.strip().split()
                            print(f"      📋 表数量: {len(tables)}")
                            
                            # 查找用户相关的表
                            user_tables = [table for table in tables if any(keyword in table.lower() for keyword in ['user', 'account', 'auth', 'login'])]
                            if user_tables:
                                print(f"      👤 用户相关表: {', '.join(user_tables[:3])}")
                        else:
                            print(f"      ❌ 无法读取数据库表")
            else:
                print("❌ 未找到数据库文件")
            
            print("✅ 用户登录状态检查完成")
            print()
            
        except Exception as e:
            log_error(f"[Facebook诊断] 用户登录状态检查失败: {e}", component="FacebookDiagnostic")

    async def _analyze_data_directory_details(self):
        """分析数据目录详细信息"""
        try:
            print("📁 数据目录详细分析...")
            print("-" * 40)
            
            # 显示完整的目录结构
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {self.facebook_data_path} -type d | sort'")
            if success and result.strip():
                directories = result.strip().split('\n')
                print(f"📊 目录结构 ({len(directories)} 个目录):")
                for directory in directories:
                    relative_path = directory.replace(self.facebook_data_path, "").lstrip('/')
                    if relative_path:
                        level = relative_path.count('/')
                        indent = "  " * level
                        dir_name = relative_path.split('/')[-1]
                        print(f"   {indent}📁 {dir_name}")
            
            # 统计文件数量
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {self.facebook_data_path} -type f | wc -l'")
            if success and result.strip():
                file_count = result.strip()
                print(f"📄 总文件数: {file_count}")
            
            # 查找最大的文件
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {self.facebook_data_path} -type f -exec ls -la {{}} \\; | sort -k5 -nr | head -5'")
            if success and result.strip():
                large_files = result.strip().split('\n')
                print(f"📊 最大的文件:")
                for file_info in large_files:
                    parts = file_info.split()
                    if len(parts) >= 9:
                        size = parts[4]
                        file_path = ' '.join(parts[8:])
                        file_name = file_path.split('/')[-1]
                        print(f"   📄 {file_name} ({size} bytes)")
            
            # 查找最近修改的文件
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {self.facebook_data_path} -type f -exec ls -lt {{}} \\; | head -5'")
            if success and result.strip():
                recent_files = result.strip().split('\n')
                print(f"🕒 最近修改的文件:")
                for file_info in recent_files:
                    parts = file_info.split()
                    if len(parts) >= 9:
                        date_time = ' '.join(parts[5:8])
                        file_path = ' '.join(parts[8:])
                        file_name = file_path.split('/')[-1]
                        print(f"   📄 {file_name} ({date_time})")
            
            print("✅ 数据目录详细分析完成")
            print()
            
        except Exception as e:
            log_error(f"[Facebook诊断] 数据目录分析失败: {e}", component="FacebookDiagnostic")

    async def _diagnose_2fa_status(self):
        """诊断2FA状态"""
        try:
            print("🔐 2FA状态诊断...")
            print("-" * 40)
            
            # 搜索2FA相关的关键词
            keywords = ['2fa', 'totp', 'authenticator', 'two.factor', 'mfa', 'otp', 'secret', 'auth.code']
            
            found_2fa_references = False
            
            for keyword in keywords:
                print(f"🔍 搜索关键词: {keyword}")
                
                # 在所有文件中搜索关键词
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {self.facebook_data_path} -type f -exec grep -l \"{keyword}\" {{}} \\; 2>/dev/null'")
                if success and result.strip():
                    files = result.strip().split('\n')
                    print(f"   ✅ 找到 {len(files)} 个相关文件")
                    found_2fa_references = True
                    
                    for file_path in files[:3]:  # 只显示前3个
                        if file_path.strip():
                            file_name = file_path.split('/')[-1]
                            print(f"      📄 {file_name}")
                            
                            # 显示匹配的内容
                            success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"su -c 'grep -n \"{keyword}\" {file_path} | head -2'")
                            if success2 and result2.strip():
                                matches = result2.strip().split('\n')
                                for match in matches:
                                    print(f"         🔍 {match[:60]}...")
                else:
                    print(f"   ❌ 未找到相关文件")
            
            if not found_2fa_references:
                print("❌ 未找到任何2FA相关的引用")
                print("💡 这强烈表明Facebook账户未启用2FA功能")
            else:
                print("✅ 找到2FA相关引用，但可能是应用代码而非用户数据")
            
            print("✅ 2FA状态诊断完成")
            print()
            
        except Exception as e:
            log_error(f"[Facebook诊断] 2FA状态诊断失败: {e}", component="FacebookDiagnostic")

    async def _check_network_and_services(self):
        """检查网络和服务"""
        try:
            print("🌐 网络和服务检查...")
            print("-" * 40)
            
            # 检查网络连接
            success, result = self.task.ld.execute_ld(self.emulator_id, "ping -c 1 facebook.com")
            if success and "1 packets transmitted, 1 received" in result:
                print("✅ 网络连接: 正常")
            else:
                print("❌ 网络连接: 异常")
            
            # 检查Facebook相关的网络连接
            success, result = self.task.ld.execute_ld(self.emulator_id, "netstat -an | grep facebook")
            if success and result.strip():
                connections = result.strip().split('\n')
                print(f"🔗 Facebook网络连接: {len(connections)} 个")
            else:
                print("❌ Facebook网络连接: 无")
            
            # 检查系统服务
            success, result = self.task.ld.execute_ld(self.emulator_id, "service list | grep -i facebook")
            if success and result.strip():
                services = result.strip().split('\n')
                print(f"⚙️  Facebook系统服务: {len(services)} 个")
            else:
                print("❌ Facebook系统服务: 无")
            
            print("✅ 网络和服务检查完成")
            print()
            
        except Exception as e:
            log_error(f"[Facebook诊断] 网络和服务检查失败: {e}", component="FacebookDiagnostic")

    async def _provide_solutions(self):
        """提供解决方案"""
        try:
            print("💡 解决方案和建议")
            print("=" * 50)
            
            print("📋 诊断结论:")
            print("根据详细的诊断结果，我们可以确认：")
            print()
            print("✅ Facebook应用已正确安装并可以访问")
            print("✅ Root权限工作正常")
            print("✅ 数据提取工具功能正常")
            print("❌ Facebook账户当前没有启用2FA功能")
            print()
            
            print("🔧 解决方案:")
            print("1. 恢复Facebook账户访问权限:")
            print("   - 使用备用邮箱重置密码")
            print("   - 联系Facebook客服进行身份验证")
            print("   - 使用可信联系人功能")
            print()
            print("2. 启用2FA功能:")
            print("   - 登录Facebook网页版")
            print("   - 进入设置 → 安全和登录")
            print("   - 选择双重验证")
            print("   - 添加认证器应用")
            print()
            print("3. 提取2FA密钥:")
            print("   - 启用2FA后重新运行我们的提取工具")
            print("   - 工具将能够找到并提取真实的2FA密钥")
            print()
            print("📞 Facebook客服联系方式:")
            print("- 网页: https://www.facebook.com/help/contact")
            print("- 选择'无法访问账户'或'账户被黑客入侵'")
            print("- 提供身份验证文件（身份证、护照等）")
            print("- 说明手机号码已更换的情况")
            print()
            print("⚠️  重要提醒:")
            print("我们的工具已经验证可以正常工作，问题在于")
            print("Facebook账户本身没有2FA数据可以提取。")
            
        except Exception as e:
            log_error(f"[Facebook诊断] 提供解决方案失败: {e}", component="FacebookDiagnostic")

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2
        
        print("🔍 Facebook应用详细诊断工具")
        print("⚠️  详细分析Facebook应用状态")
        print("⚠️  诊断2FA配置情况")
        print("⚠️  提供具体解决方案")
        print()
        
        confirm = input("确认开始诊断? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return
        
        # 创建诊断器
        diagnostic = FacebookDiagnostic(emulator_id)
        
        # 运行诊断
        await diagnostic.run_diagnostic()
        
        print("\n" + "=" * 50)
        print("✅ Facebook应用诊断完成")
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
