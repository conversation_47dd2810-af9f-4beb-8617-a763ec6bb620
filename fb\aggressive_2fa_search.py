#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔓 激进的2FA密钥搜索工具
========================================
功能描述: 针对已启用2FA的账户进行激进搜索

核心策略:
1. 强制触发2FA相关操作
2. 实时监控文件系统变化
3. 搜索所有可能的加密格式
4. 分析网络流量和内存
5. 检查系统级存储

使用方法:
python fb/aggressive_2fa_search.py [emulator_id]

注意事项:
- 针对已启用2FA的账户
- 需要root权限
- 会触发Facebook应用操作
========================================
"""

import asyncio
import sys
import re
import json
import base64
import tempfile
import struct
import hashlib
import time
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from fb.fb_2fa_task import FacebookTwoFactorTask
from core.logger_manager import log_info, log_error, log_warning

class Aggressive2FASearcher:
    """激进的2FA密钥搜索器"""
    
    def __init__(self, emulator_id: int = 2):
        """
        初始化激进搜索器
        
        Args:
            emulator_id: 模拟器ID
        """
        self.emulator_id = emulator_id
        self.task = None
        self.temp_dir = Path(tempfile.gettempdir()) / f"aggressive_2fa_{emulator_id}"
        self.temp_dir.mkdir(exist_ok=True)
        
        # Facebook和Instagram应用包名
        self.target_packages = [
            "com.facebook.katana",      # Facebook
            "com.instagram.android",    # Instagram
            "com.facebook.orca",        # Messenger
            "com.facebook.lite"         # Facebook Lite
        ]
        
        log_info(f"[激进搜索] 激进2FA搜索器初始化 - 模拟器{emulator_id}", component="AggressiveSearcher")

    async def run_aggressive_search(self):
        """运行激进搜索流程"""
        try:
            log_info(f"[激进搜索] 开始激进2FA搜索", component="AggressiveSearcher")
            
            print("🔓 激进的2FA密钥搜索工具")
            print("=" * 50)
            print("⚠️  针对已启用2FA的账户进行激进搜索")
            print("⚠️  会触发应用操作和实时监控")
            print("⚠️  需要root权限")
            print()
            
            # 初始化任务
            self.task = FacebookTwoFactorTask(self.emulator_id)
            
            if not self.task.ld:
                print("❌ 无法连接到模拟器")
                return
            
            # 第一步：验证环境和2FA状态
            if not await self._verify_2fa_environment():
                return
            
            # 第二步：启动实时监控
            await self._start_realtime_monitoring()
            
            # 第三步：触发2FA相关操作
            await self._trigger_2fa_operations()
            
            # 第四步：激进搜索所有位置
            all_secrets = await self._aggressive_search_all_locations()
            
            # 第五步：分析加密数据
            encrypted_secrets = await self._analyze_encrypted_data()
            all_secrets.extend(encrypted_secrets)
            
            # 第六步：内存和网络分析
            runtime_secrets = await self._analyze_runtime_data()
            all_secrets.extend(runtime_secrets)
            
            # 第七步：验证和保存结果
            verified_secrets = await self._verify_and_save_results(all_secrets)
            
            # 第八步：显示最终结果
            await self._display_final_results(verified_secrets)
            
            log_info(f"[激进搜索] 激进2FA搜索完成", component="AggressiveSearcher")
            
        except Exception as e:
            log_error(f"[激进搜索] 激进搜索失败: {e}", component="AggressiveSearcher")
        finally:
            await self._cleanup()

    async def _verify_2fa_environment(self) -> bool:
        """验证2FA环境"""
        try:
            print("🔧 验证2FA环境...")
            print("-" * 40)
            
            # 检查root权限
            success, result = self.task.ld.execute_ld(self.emulator_id, "su -c 'whoami'")
            if not success or "root" not in result:
                print("❌ Root权限不可用")
                return False
            
            print("✅ Root权限可用")
            
            # 检查目标应用
            installed_apps = []
            for package in self.target_packages:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"pm list packages | grep {package}")
                if success and package in result:
                    installed_apps.append(package)
                    print(f"✅ 发现应用: {package}")
            
            if not installed_apps:
                print("❌ 未找到目标应用")
                return False
            
            self.installed_packages = installed_apps
            
            # 检查应用是否正在运行
            for package in installed_apps:
                success, result = self.task.ld.execute_ld(self.emulator_id, f"ps | grep {package}")
                if success and result.strip():
                    print(f"🔄 {package} 正在运行")
                else:
                    print(f"⚠️  {package} 未运行，尝试启动...")
                    # 尝试启动应用
                    self.task.ld.execute_ld(self.emulator_id, f"am start -n {package}/.LoginActivity")
                    await asyncio.sleep(3)
            
            print("✅ 2FA环境验证完成")
            print()
            return True
            
        except Exception as e:
            log_error(f"[激进搜索] 2FA环境验证失败: {e}", component="AggressiveSearcher")
            return False

    async def _start_realtime_monitoring(self):
        """启动实时监控"""
        try:
            print("📊 启动实时监控...")
            print("-" * 40)
            
            # 创建监控目录
            monitor_dir = "/sdcard/2fa_monitor"
            self.task.ld.execute_ld(self.emulator_id, f"mkdir -p {monitor_dir}")
            
            # 启动文件系统监控
            print("📁 启动文件系统监控...")
            for package in self.installed_packages:
                data_path = f"/data/data/{package}"
                
                # 记录当前文件状态
                success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'find {data_path} -type f -exec ls -la {{}} \\; > {monitor_dir}/{package}_before.txt 2>/dev/null'")
                if success:
                    print(f"   ✅ {package} 文件状态已记录")
            
            # 启动网络监控
            print("🌐 启动网络监控...")
            success, result = self.task.ld.execute_ld(self.emulator_id, f"su -c 'netstat -an > {monitor_dir}/network_before.txt'")
            if success:
                print("   ✅ 网络状态已记录")
            
            print("✅ 实时监控启动完成")
            print()
            
        except Exception as e:
            log_error(f"[激进搜索] 实时监控启动失败: {e}", component="AggressiveSearcher")

    async def _trigger_2fa_operations(self):
        """触发2FA相关操作"""
        try:
            print("🎯 触发2FA相关操作...")
            print("-" * 40)
            
            # 操作1: 访问Facebook安全设置
            print("🔐 尝试访问Facebook安全设置...")
            security_intents = [
                "am start -a android.intent.action.VIEW -d 'https://www.facebook.com/settings/security'",
                "am start -n com.facebook.katana/.activity.FbMainTabActivity",
                "am start -n com.facebook.katana/.LoginActivity"
            ]
            
            for intent in security_intents:
                success, result = self.task.ld.execute_ld(self.emulator_id, intent)
                if success:
                    print(f"   ✅ 执行成功: {intent.split()[-1]}")
                    await asyncio.sleep(5)  # 等待应用响应
                else:
                    print(f"   ❌ 执行失败: {intent.split()[-1]}")
            
            # 操作2: 模拟2FA验证请求
            print("🔢 模拟2FA验证请求...")
            
            # 发送广播触发2FA相关功能
            broadcast_actions = [
                "am broadcast -a com.facebook.katana.ACTION_2FA_REQUEST",
                "am broadcast -a com.facebook.katana.ACTION_SECURITY_CHECK",
                "am broadcast -a android.intent.action.USER_PRESENT"
            ]
            
            for action in broadcast_actions:
                success, result = self.task.ld.execute_ld(self.emulator_id, action)
                if success:
                    print(f"   ✅ 广播发送成功")
                await asyncio.sleep(2)
            
            # 操作3: 触发应用内2FA功能
            print("📱 触发应用内2FA功能...")
            
            # 模拟按键操作
            key_sequences = [
                "input keyevent KEYCODE_MENU",
                "input keyevent KEYCODE_BACK",
                "input keyevent KEYCODE_HOME"
            ]
            
            for key in key_sequences:
                self.task.ld.execute_ld(self.emulator_id, key)
                await asyncio.sleep(1)
            
            print("✅ 2FA操作触发完成")
            print()
            
        except Exception as e:
            log_error(f"[激进搜索] 2FA操作触发失败: {e}", component="AggressiveSearcher")

    async def _aggressive_search_all_locations(self) -> List[Dict[str, Any]]:
        """激进搜索所有位置"""
        try:
            print("🔍 激进搜索所有位置...")
            print("-" * 40)
            
            all_secrets = []
            
            # 搜索位置列表
            search_locations = [
                # 应用数据目录
                "/data/data/",
                # 系统目录
                "/system/",
                "/vendor/",
                # 存储目录
                "/sdcard/",
                "/storage/emulated/0/",
                # 缓存目录
                "/cache/",
                "/data/cache/",
                # 临时目录
                "/tmp/",
                "/data/local/tmp/",
                # 其他可能位置
                "/data/misc/",
                "/data/system/",
            ]
            
            for location in search_locations:
                print(f"🔍 搜索位置: {location}")
                
                # 搜索Base32模式
                search_cmd = f"su -c 'find {location} -type f -exec grep -l \"[A-Z2-7]{{16,}}\" {{}} \\; 2>/dev/null | head -20'"
                success, result = self.task.ld.execute_ld(self.emulator_id, search_cmd)
                
                if success and result.strip():
                    files = result.strip().split('\n')
                    print(f"   📄 找到 {len(files)} 个可能的文件")
                    
                    for file_path in files:
                        if file_path.strip():
                            file_secrets = await self._extract_from_file(file_path.strip())
                            all_secrets.extend(file_secrets)
                            
                            if file_secrets:
                                print(f"      🔑 从 {file_path.split('/')[-1]} 提取到 {len(file_secrets)} 个密钥")
                else:
                    print(f"   ❌ 未找到相关文件")
            
            print(f"✅ 激进搜索完成: 找到 {len(all_secrets)} 个可能的密钥")
            print()
            return all_secrets
            
        except Exception as e:
            log_error(f"[激进搜索] 激进搜索失败: {e}", component="AggressiveSearcher")
            return []

    async def _analyze_encrypted_data(self) -> List[Dict[str, Any]]:
        """分析加密数据"""
        try:
            print("🔐 分析加密数据...")
            print("-" * 40)
            
            secrets = []
            
            # 查找加密文件
            encrypted_patterns = [
                "*.enc",
                "*.encrypted", 
                "*.key",
                "*.keystore",
                "*.p12",
                "*.jks"
            ]
            
            for pattern in encrypted_patterns:
                print(f"🔍 搜索加密文件: {pattern}")
                
                search_cmd = f"su -c 'find /data -name \"{pattern}\" 2>/dev/null'"
                success, result = self.task.ld.execute_ld(self.emulator_id, search_cmd)
                
                if success and result.strip():
                    files = result.strip().split('\n')
                    print(f"   📄 找到 {len(files)} 个加密文件")
                    
                    for file_path in files:
                        if file_path.strip():
                            # 尝试多种解密方法
                            decrypted_secrets = await self._try_decrypt_file(file_path.strip())
                            secrets.extend(decrypted_secrets)
                            
                            if decrypted_secrets:
                                print(f"      🔓 解密成功: {file_path.split('/')[-1]}")
                else:
                    print(f"   ❌ 未找到 {pattern} 文件")
            
            print(f"✅ 加密数据分析完成: 找到 {len(secrets)} 个密钥")
            print()
            return secrets
            
        except Exception as e:
            log_error(f"[激进搜索] 加密数据分析失败: {e}", component="AggressiveSearcher")
            return []

    async def _analyze_runtime_data(self) -> List[Dict[str, Any]]:
        """分析运行时数据"""
        try:
            print("🧠 分析运行时数据...")
            print("-" * 40)
            
            secrets = []
            
            # 分析进程内存
            for package in self.installed_packages:
                print(f"🔍 分析 {package} 进程内存...")
                
                # 获取进程PID
                success, result = self.task.ld.execute_ld(self.emulator_id, f"pgrep {package.split('.')[-1]}")
                if success and result.strip():
                    pids = result.strip().split('\n')
                    
                    for pid in pids:
                        if pid.strip().isdigit():
                            print(f"   🔍 分析PID: {pid}")
                            
                            # 分析进程内存映射
                            success2, result2 = self.task.ld.execute_ld(self.emulator_id, f"cat /proc/{pid}/maps 2>/dev/null | grep -E '(heap|stack|anon)'")
                            if success2 and result2.strip():
                                print(f"      📊 内存映射: {len(result2.strip().split())} 个区域")
                            
                            # 分析进程环境变量
                            success3, result3 = self.task.ld.execute_ld(self.emulator_id, f"cat /proc/{pid}/environ 2>/dev/null | tr '\\0' '\\n' | grep -E '[A-Z2-7]{{16,}}'")
                            if success3 and result3.strip():
                                env_keys = result3.strip().split('\n')
                                for key in env_keys:
                                    if self._is_valid_base32_key(key.strip()):
                                        secrets.append({
                                            'secret_key': key.strip(),
                                            'source': f'process_env:{pid}',
                                            'method': 'runtime_analysis',
                                            'confidence': 'high'
                                        })
                                        print(f"      🔑 环境变量中发现密钥: {key.strip()[:8]}...")
            
            print(f"✅ 运行时数据分析完成: 找到 {len(secrets)} 个密钥")
            print()
            return secrets
            
        except Exception as e:
            log_error(f"[激进搜索] 运行时数据分析失败: {e}", component="AggressiveSearcher")
            return []

    async def _extract_from_file(self, file_path: str) -> List[Dict[str, Any]]:
        """从文件提取密钥"""
        try:
            secrets = []

            # 使用多种方法提取
            extraction_methods = [
                f"su -c 'cat {file_path} | grep -oE \"[A-Z2-7]{{16,}}\"'",
                f"su -c 'strings {file_path} | grep -oE \"[A-Z2-7]{{16,}}\"'",
                f"su -c 'hexdump -C {file_path} | grep -oE \"[A-Z2-7]{{16,}}\"'"
            ]

            for method in extraction_methods:
                success, result = self.task.ld.execute_ld(self.emulator_id, method)
                if success and result.strip():
                    potential_keys = result.strip().split('\n')
                    for key in potential_keys:
                        if self._is_valid_base32_key(key.strip()):
                            secrets.append({
                                'secret_key': key.strip(),
                                'source': f'file:{file_path.split("/")[-1]}',
                                'method': 'file_extraction',
                                'confidence': 'medium'
                            })
                    break  # 如果找到了就不用尝试其他方法

            return secrets

        except Exception:
            return []

    async def _try_decrypt_file(self, file_path: str) -> List[Dict[str, Any]]:
        """尝试解密文件"""
        try:
            secrets = []

            # 尝试不同的解密方法
            decrypt_methods = [
                # Base64解码
                f"su -c 'base64 -d {file_path} 2>/dev/null | strings | grep -oE \"[A-Z2-7]{{16,}}\"'",
                # 简单XOR解密
                f"su -c 'od -t x1 {file_path} | grep -oE \"[A-Z2-7]{{16,}}\"'",
                # 直接字符串提取
                f"su -c 'strings {file_path} | grep -oE \"[A-Z2-7]{{16,}}\"'"
            ]

            for method in decrypt_methods:
                success, result = self.task.ld.execute_ld(self.emulator_id, method)
                if success and result.strip():
                    potential_keys = result.strip().split('\n')
                    for key in potential_keys:
                        if self._is_valid_base32_key(key.strip()):
                            secrets.append({
                                'secret_key': key.strip(),
                                'source': f'decrypted:{file_path.split("/")[-1]}',
                                'method': 'file_decryption',
                                'confidence': 'high'
                            })

            return secrets

        except Exception:
            return []

    def _is_valid_base32_key(self, key: str) -> bool:
        """验证是否为有效的Base32密钥"""
        try:
            if not key or len(key) < 16:
                return False

            # 清理密钥
            cleaned = re.sub(r'[^A-Z2-7]', '', key.upper())
            if len(cleaned) < 16:
                return False

            # 验证Base32字符集
            if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in cleaned):
                return False

            # 尝试解码
            try:
                base64.b32decode(cleaned)
                return True
            except Exception:
                return False

        except Exception:
            return False

    def _generate_totp(self, secret: str) -> str:
        """生成TOTP验证码"""
        try:
            import hmac
            import hashlib
            import struct
            import time

            # 解码Base32密钥
            key = base64.b32decode(secret)

            # 获取当前时间戳
            timestamp = int(time.time()) // 30

            # 生成TOTP
            msg = struct.pack('>Q', timestamp)
            hmac_digest = hmac.new(key, msg, hashlib.sha1).digest()
            offset = hmac_digest[-1] & 0x0f
            code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
            code = (code & 0x7fffffff) % 1000000

            return f"{code:06d}"

        except Exception:
            return "000000"

    async def _verify_and_save_results(self, all_secrets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证和保存结果"""
        try:
            print("🔍 验证激进搜索结果...")
            print("-" * 40)

            # 去重
            unique_secrets = []
            seen_keys = set()

            for secret in all_secrets:
                key = secret.get('secret_key', '')
                if key and key not in seen_keys:
                    seen_keys.add(key)

                    # 验证密钥并生成测试TOTP
                    if self._is_valid_base32_key(key):
                        try:
                            current_code = self._generate_totp(key)

                            secret['test_totp'] = current_code
                            secret['verified'] = True
                            secret['key_length'] = len(key)

                            print(f"✅ 验证密钥: {key[:8]}...")
                            print(f"   来源: {secret.get('source', 'unknown')}")
                            print(f"   方法: {secret.get('method', 'unknown')}")
                            print(f"   当前验证码: {current_code}")
                            print(f"   置信度: {secret.get('confidence', 'unknown')}")
                            print()

                        except Exception as e:
                            secret['verified'] = False
                            secret['error'] = str(e)
                            print(f"❌ 密钥验证失败: {key[:8]}... - {e}")

                        unique_secrets.append(secret)

            # 按置信度排序
            confidence_order = {'high': 3, 'medium': 2, 'low': 1}
            unique_secrets.sort(key=lambda x: confidence_order.get(x.get('confidence', 'low'), 0), reverse=True)

            # 保存结果
            if unique_secrets:
                await self._save_aggressive_results(unique_secrets)

            print(f"✅ 验证完成: {len(unique_secrets)} 个有效密钥")
            print()
            return unique_secrets

        except Exception as e:
            log_error(f"[激进搜索] 验证结果失败: {e}", component="AggressiveSearcher")
            return []

    async def _save_aggressive_results(self, secrets: List[Dict[str, Any]]):
        """保存激进搜索结果"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存到JSON文件
            output_dir = Path("./fb_2fa_results/aggressive_search/")
            output_dir.mkdir(parents=True, exist_ok=True)

            json_file = output_dir / f"aggressive_2fa_secrets_{self.emulator_id}_{timestamp}.json"

            result_data = {
                'emulator_id': self.emulator_id,
                'extraction_time': datetime.datetime.now().isoformat(),
                'extraction_method': 'aggressive_2fa_search',
                'target_packages': self.target_packages,
                'total_secrets': len(secrets),
                'secrets': secrets
            }

            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)

            print(f"💾 激进搜索结果已保存: {json_file}")

        except Exception as e:
            log_error(f"[激进搜索] 保存结果失败: {e}", component="AggressiveSearcher")

    async def _display_final_results(self, secrets: List[Dict[str, Any]]):
        """显示最终结果"""
        try:
            print("🎯 激进2FA搜索结果")
            print("=" * 50)

            if not secrets:
                print("❌ 激进搜索未找到任何2FA密钥")
                print("\n💡 可能的原因:")
                print("1. 2FA密钥使用了我们无法破解的加密方式")
                print("2. 密钥存储在硬件安全模块中")
                print("3. 使用了服务器端验证而非本地存储")
                print("4. 需要特定的触发条件才能生成本地密钥")
                print("\n🔧 进一步建议:")
                print("1. 在Facebook应用中手动访问2FA设置")
                print("2. 尝试重新设置2FA功能")
                print("3. 检查是否使用了备用验证码")
                print("4. 联系Facebook客服获取帮助")
                return

            print(f"✅ 激进搜索找到 {len(secrets)} 个2FA密钥:")
            print()

            for i, secret in enumerate(secrets, 1):
                confidence = secret.get('confidence', 'unknown').upper()
                print(f"🔑 密钥 {i} (置信度: {confidence}):")
                print(f"   密钥: {secret['secret_key']}")
                print(f"   来源: {secret.get('source', 'unknown')}")
                print(f"   提取方法: {secret.get('method', 'unknown')}")
                print(f"   当前验证码: {secret.get('test_totp', 'N/A')}")
                print(f"   密钥长度: {secret.get('key_length', 0)} 字符")

                if secret.get('verified'):
                    print(f"   ✅ 验证状态: 有效")
                else:
                    print(f"   ❌ 验证状态: 无效")
                    if secret.get('error'):
                        print(f"   ⚠️  错误: {secret['error']}")

                print("-" * 30)

            print("\n🎉 激进2FA搜索成功!")
            print("\n📋 使用指导:")
            print("1. 优先使用置信度为'HIGH'的密钥")
            print("2. 在认证器应用中测试这些密钥")
            print("3. 验证生成的验证码是否与Facebook匹配")
            print("4. 成功验证后立即备份密钥")

        except Exception as e:
            log_error(f"[激进搜索] 显示结果失败: {e}", component="AggressiveSearcher")

    async def _cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir, ignore_errors=True)

            # 清理监控文件
            self.task.ld.execute_ld(self.emulator_id, "rm -rf /sdcard/2fa_monitor")
        except Exception:
            pass

async def main():
    """主函数"""
    try:
        # 获取模拟器ID参数
        emulator_id = int(sys.argv[1]) if len(sys.argv) > 1 else 2

        print("🔓 激进的2FA密钥搜索工具")
        print("⚠️  针对已启用2FA的账户进行激进搜索")
        print("⚠️  会触发应用操作和实时监控")
        print("⚠️  需要root权限")
        print("⚠️  仅用于合法的账户恢复目的")
        print()

        confirm = input("确认您已启用2FA并继续激进搜索? (输入 'YES' 继续): ").strip()
        if confirm != 'YES':
            print("❌ 操作取消")
            return

        # 创建激进搜索器
        searcher = Aggressive2FASearcher(emulator_id)

        # 运行激进搜索
        await searcher.run_aggressive_search()

        print("\n" + "=" * 50)
        print("✅ 激进2FA搜索完成")

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
