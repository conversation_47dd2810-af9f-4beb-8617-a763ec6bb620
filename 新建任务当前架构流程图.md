# 🎯 当前架构流程图说明

## 📋 任务类型结构
```
├── core/
    ├── instagram_task.py      # Instagram私信任务 ✅已实现V2Ray启动
    ├── douyin_follow_task.py  # 抖音关注任务（示例）
    ├── tiktok_like_task.py    # TikTok点赞任务（示例）
    └── weibo_comment_task.py  # 微博评论任务（示例）
```

---

## 🔄 当前成功的执行流程

### **主流程：**
```
UI点击具体任务按钮 → 异步桥梁 → 统一模拟器管理器 → Instagram任务调度 → InstagramDMTask → 具体业务逻辑
```

### **Instagram私信任务详细流程：**
```
UI点击"Instagram私信"
    ↓
MainWindowV2._on_instagram_dm_start()
    ↓
异步桥梁.execute_operation('instagram_dm_task', task_data)
    ↓
FixedAsyncBridge._handle_instagram_dm_task()
    ↓
统一模拟器管理器.batch_start_emulators() [启动模拟器]
    ↓
_schedule_instagram_tasks_after_startup() [安排任务]
    ↓
_wait_and_execute_instagram_task() [等待启动完成]
    ↓
_execute_instagram_task() [执行Instagram任务]
    ↓
InstagramDMTask.execute() [具体业务逻辑]
    ↓
阶段一：_stage1_app_detection() [应用检测]
    ↓
阶段二：_stage2_v2ray_connection() [V2Ray连接] ✅已实现启动功能
    ↓
阶段三：_stage3_instagram_launch() [Instagram启动]
    ↓
阶段四：_stage4_mass_dm() [批量私信发送]
```

### **抖音关注任务详细流程：**
```
UI点击"抖音关注"
    ↓
MainWindowV2._on_douyin_follow_start()
    ↓
异步桥梁.execute_operation('douyin_follow_task', task_data)
    ↓
FixedAsyncBridge._handle_douyin_follow_task()
    ↓
统一模拟器管理器.batch_start_emulators() [启动模拟器]
    ↓
_schedule_douyin_follow_tasks_after_startup() [安排任务]
    ↓
_wait_and_execute_douyin_follow_task() [等待启动完成]
    ↓
_execute_douyin_follow_task() [执行抖音任务]
    ↓
DouyinFollowTask.execute() [具体业务逻辑]
    ↓
阶段一：_stage1_app_detection() [应用检测]
    ↓
阶段二：_stage2_network_connection() [网络连接检查]
    ↓
阶段三：_stage3_douyin_launch() [抖音应用启动]
    ↓
阶段四：_stage4_mass_follow() [批量用户关注]
```

### **TikTok点赞任务详细流程：**
```
UI点击"TikTok点赞"
    ↓
MainWindowV2._on_tiktok_like_start()
    ↓
异步桥梁.execute_operation('tiktok_like_task', task_data)
    ↓
FixedAsyncBridge._handle_tiktok_like_task()
    ↓
统一模拟器管理器.batch_start_emulators() [启动模拟器]
    ↓
_schedule_tiktok_like_tasks_after_startup() [安排任务]
    ↓
_wait_and_execute_tiktok_like_task() [等待启动完成]
    ↓
_execute_tiktok_like_task() [执行TikTok任务]
    ↓
TikTokLikeTask.execute() [具体业务逻辑]
    ↓
阶段一：_stage1_app_detection() [应用检测]
    ↓
阶段二：_stage2_network_connection() [网络连接检查]
    ↓
阶段三：_stage3_tiktok_launch() [TikTok应用启动]
    ↓
阶段四：_stage4_mass_like() [批量视频点赞]
```

---

## 📊 架构对比

### **❌ 之前失败的架构：**
```
UI → TaskScheduler → TaskExecutor → InstagramDMTask
                ↓
        模拟器启动和任务执行混合
                ↓
        线程管理复杂，状态同步困难
                ↓
        各种操作模式都无法正常运行
```

### **✅ 现在成功的架构：**
```
UI → 异步桥梁 → 统一模拟器管理器（启动模拟器）
                        ↓
                模拟器已由统一模拟器管理器启动完成
                        ↓
                InstagramDMTask只处理Instagram业务逻辑
                        ↓
                V2Ray启动功能已验证成功 ✅
```

---

## 🚀 新任务添加标准模式

### **4步添加法：**
```
步骤1：创建任务类
core/新任务_task.py → 实现execute()方法 → 分阶段业务逻辑

步骤2：异步桥梁集成
core/async_bridge.py → 添加operation分支 → 添加处理方法

步骤3：创建UI界面
ui/新任务_ui.py → 继承QWidget → 发射信号

步骤4：主窗口集成
ui/main_window_v2.py → 创建页面方法 → 连接信号 → 添加标签页
```

### **任务类标准结构（✅已优化配置管理）：**
```
class NewTask:
    def __init__(self, emulator_id: int, config_manager=None):
        self.emulator_id = emulator_id
        self.emulator_manager = get_emulator_manager()

        # 🎯 获取配置管理器（任务类内部获取配置）
        if config_manager is None:
            from .simple_config import get_config_manager
            self.config_manager = get_config_manager()
        else:
            self.config_manager = config_manager

        # 🎯 加载任务配置（从统一配置系统）
        self._load_config()

        # 🎯 注册配置热加载观察者
        self._register_config_observer()

    def _load_config(self):
        """从统一配置系统加载任务配置"""
        self.param1 = self.config_manager.get("新任务名.param1", 默认值)
        self.param2 = self.config_manager.get("新任务名.param2", 默认值)
        # ... 其他配置参数

    def _register_config_observer(self):
        """注册配置热加载观察者"""
        self.config_manager.register_observer(self._on_config_changed)

    def _on_config_changed(self, key: str, old_value, new_value):
        """配置变化处理回调（支持运行时热加载）"""
        if key.startswith("新任务名."):
            self._load_config()  # 自动重新加载配置

    async def execute():
        try:
            # 阶段一：应用检测
            if not await self._stage1_app_detection():
                return {'status': 'failed'}

            # 阶段二：网络连接
            if not await self._stage2_network_connection():
                return {'status': 'failed'}

            # 阶段三：应用启动
            if not await self._stage3_app_launch():
                return {'status': 'failed'}

            # 阶段四：业务逻辑
            result = await self._stage4_business_logic()

            return {'status': 'completed', 'result': result}
        finally:
            # 🎯 任务结束时注销配置观察者
            self.unregister_config_observer()
```

---

## 🔧 关键技术点

### **模拟器启动：**
```
统一模拟器管理器.batch_start_emulators(emulator_ids)
                ↓
        添加到启动队列 → 启动调度器 → 并发控制
                ↓
        状态变化：排队中 → 启动中 → 运行中
```

### **应用启动：**
```
native_api = emulator_manager.get_native_api()
result = await native_api.run_app(emulator_id, package_name)
                ↓
        V2Ray启动：✅已实现并测试成功
        Instagram启动：待实现
        抖音启动：待实现
```

### **任务等待：**
```
_wait_for_emulator_startup_completion(emulator_id)
                ↓
        检查模拟器状态 → 等待运行状态 → 开始执行任务
```

### **配置管理（✅已优化）：**
```
任务类内部配置获取流程：
InstagramDMTask.__init__()
                ↓
        获取配置管理器：get_config_manager()
                ↓
        加载配置：self._load_config()
                ↓
        注册观察者：self._register_config_observer()
                ↓
        配置变化时自动热加载：self._on_config_changed()
```

### **配置热加载机制：**
```
配置文件：app_config.json（统一存储）
                ↓
        配置管理器：simple_config.py（观察者模式）
                ↓
        热加载服务：config_hot_reload.py（文件监控）
                ↓
        任务类观察者：_on_config_changed()（自动更新）
                ↓
        运行时配置更新：无需重启程序
```

---

## 📈 当前实现状态

### **✅ 已实现功能：**
```
✅ 统一模拟器管理器 → 批量启动、状态管理、并发控制
✅ 异步桥梁 → 任务调度、状态通知、错误处理
✅ V2Ray启动功能 → 原生API调用、启动验证
✅ Instagram任务框架 → 完整的阶段划分
✅ UI集成 → 信号连接、状态显示
✅ 配置管理优化 → 任务类内部获取配置，减少耦合
✅ 配置热加载 → 运行时配置更新，观察者模式
✅ 内存管理 → 任务结束时自动注销观察者
```

### **⚠️ 待实现功能：**
```
⚠️ V2Ray节点连接 → 节点列表、连接验证
⚠️ Instagram应用启动 → 应用启动、界面检测
⚠️ 批量私信发送 → 粉丝列表、私信逻辑
⚠️ 其他平台任务 → 抖音、TikTok等
```

---

## 🎯 核心优势

### **架构优势：**
```
1. 模拟器和任务分离 → 模拟器先启动，任务后执行
2. 异步优先设计 → 完全非阻塞，支持并发
3. 统一状态管理 → 集中处理所有状态变化
4. 易于扩展 → 添加新任务只需4个步骤
5. 错误隔离 → 异常不会影响其他任务
```

### **与"启动选中"功能的区别：**
```
"启动选中"按钮 → 仅启动模拟器，不执行任务
    ↓
MainWindowV2._on_start_selected()
    ↓
异步桥梁.execute_operation('start_batch', emulator_ids)
    ↓
统一模拟器管理器.batch_start_emulators() [仅启动模拟器]

VS

"Instagram私信"按钮 → 启动模拟器 + 执行Instagram任务
    ↓
MainWindowV2._on_instagram_dm_start()
    ↓
异步桥梁.execute_operation('instagram_dm_task', task_data)
    ↓
统一模拟器管理器.batch_start_emulators() [启动模拟器]
    ↓
InstagramDMTask.execute() [执行具体任务]
```

---

## 📝 总结

**当前架构的核心特点：**
1. **清晰分层** - UI → 异步桥梁 → 模拟器管理 → 任务执行
2. **模拟器优先** - 先确保模拟器启动成功，再执行任务
3. **异步非阻塞** - 所有操作都是异步的，不影响UI
4. **统一管理** - 集中的状态管理和错误处理
5. **易于扩展** - 标准化的新任务添加流程
6. **✅ 配置优化** - 任务类内部获取配置，减少耦合
7. **✅ 热加载支持** - 运行时配置更新，无需重启

**V2Ray启动功能已成功实现**，**配置热加载功能已验证**，证明了当前架构的正确性和可行性。
